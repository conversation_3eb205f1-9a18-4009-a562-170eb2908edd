import { ref, reactive, toRefs } from 'vue'
import { getMyFavorites } from '../api/favorite'

// 全局收藏状态管理
const favoriteState = reactive({
  favorites: [],
  favoriteIds: new Set(),
  loading: false,
  total: 0
})

// 收藏状态变更事件
const favoriteChangeCallbacks = new Set()

export function useFavorites() {
  // 获取收藏列表
  const fetchFavorites = async (page = 1, size = 12) => {
    favoriteState.loading = true
    try {
      console.log('Fetching favorites from server...', { page, size })
      const response = await getMyFavorites({ page, size })
      if (response.success) {
        favoriteState.favorites = response.data.records
        favoriteState.total = response.data.total

        // 更新收藏ID集合
        favoriteState.favoriteIds.clear()
        response.data.records.forEach(resource => {
          favoriteState.favoriteIds.add(resource.id)
        })

        return response.data
      } else {
        console.error('Fetch favorites failed:', response)
      }
    } catch (error) {
      console.error('Fetch favorites error:', error)
    } finally {
      favoriteState.loading = false
    }
  }

  // 检查是否已收藏
  const isFavorited = (resourceId) => {
    return favoriteState.favoriteIds.has(resourceId)
  }

  // 添加收藏
  const addToFavorites = (resource) => {
    if (!favoriteState.favoriteIds.has(resource.id)) {
      favoriteState.favorites.unshift(resource)
      favoriteState.favoriteIds.add(resource.id)
      favoriteState.total++

      // 通知所有监听者
      notifyFavoriteChange(resource.id, true)
    }
  }

  // 移除收藏
  const removeFromFavorites = (resourceId) => {
    if (favoriteState.favoriteIds.has(resourceId)) {
      favoriteState.favorites = favoriteState.favorites.filter(
        resource => resource.id !== resourceId
      )
      favoriteState.favoriteIds.delete(resourceId)
      favoriteState.total = Math.max(0, favoriteState.total - 1)

      // 通知所有监听者
      notifyFavoriteChange(resourceId, false)
    }
  }

  // 切换收藏状态
  const toggleFavoriteState = (resourceId, isFavorited, resource = null) => {
    if (isFavorited && resource) {
      addToFavorites(resource)
    } else {
      removeFromFavorites(resourceId)
    }
  }

  // 通知收藏状态变更
  const notifyFavoriteChange = (resourceId, isFavorited) => {
    favoriteChangeCallbacks.forEach(callback => {
      try {
        callback(resourceId, isFavorited)
      } catch (error) {
        console.error('Favorite change callback error:', error)
      }
    })
  }

  // 监听收藏状态变更
  const onFavoriteChange = (callback) => {
    favoriteChangeCallbacks.add(callback)

    // 返回取消监听的函数
    return () => {
      favoriteChangeCallbacks.delete(callback)
    }
  }

  // 刷新收藏列表
  const refreshFavorites = () => {
    return fetchFavorites()
  }

  return {
    // 状态 - 使用toRefs确保响应式
    ...toRefs(favoriteState),

    // 方法
    fetchFavorites,
    isFavorited,
    addToFavorites,
    removeFromFavorites,
    toggleFavoriteState,
    onFavoriteChange,
    refreshFavorites
  }
}
