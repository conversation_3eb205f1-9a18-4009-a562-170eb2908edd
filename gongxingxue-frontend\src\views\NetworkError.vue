<template>
  <div class="network-error">
    <div class="error-container">
      <div class="error-illustration">
        <div class="error-icon">
          <el-icon :size="120" color="#E6A23C">
            <Connection />
          </el-icon>
        </div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">网络连接异常</h1>
        <p class="error-description">
          无法连接到服务器，请检查您的网络连接。
        </p>
        <p class="error-suggestion">
          您可以：
        </p>
        <ul class="suggestion-list">
          <li>检查网络连接是否正常</li>
          <li>尝试刷新页面</li>
          <li>检查防火墙设置</li>
          <li>稍后再试</li>
        </ul>
        
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="retry" :loading="retrying">
            <el-icon v-if="!retrying"><Refresh /></el-icon>
            {{ retrying ? '重试中...' : '重试连接' }}
          </el-button>
          <el-button size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
        </div>
        
        <div class="network-status">
          <p class="status-text">
            <el-icon :class="networkStatus.class">
              <component :is="networkStatus.icon" />
            </el-icon>
            {{ networkStatus.text }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { Connection, Refresh, House, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const router = useRouter()
const retrying = ref(false)
const networkStatus = ref({
  text: '检测网络状态中...',
  icon: 'Connection',
  class: 'status-checking'
})

const checkNetworkStatus = () => {
  if (navigator.onLine) {
    networkStatus.value = {
      text: '网络连接正常',
      icon: 'CircleCheck',
      class: 'status-online'
    }
  } else {
    networkStatus.value = {
      text: '网络连接断开',
      icon: 'CircleClose',
      class: 'status-offline'
    }
  }
}

const retry = async () => {
  retrying.value = true
  
  try {
    // 尝试发送一个简单的请求来测试连接
    const response = await fetch('/api/health', {
      method: 'GET',
      timeout: 5000
    })
    
    if (response.ok) {
      ElMessage.success('连接恢复，正在跳转...')
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } else {
      throw new Error('服务器响应异常')
    }
  } catch (error) {
    ElMessage.error('连接仍然异常，请稍后再试')
  } finally {
    retrying.value = false
  }
}

const goHome = () => {
  router.push('/')
}

const handleOnline = () => {
  checkNetworkStatus()
  ElMessage.success('网络连接已恢复')
}

const handleOffline = () => {
  checkNetworkStatus()
  ElMessage.warning('网络连接已断开')
}

onMounted(() => {
  checkNetworkStatus()
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
})

onUnmounted(() => {
  window.removeEventListener('online', handleOnline)
  window.removeEventListener('offline', handleOffline)
})
</script>

<style lang="scss" scoped>
.network-error {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  padding: 20px;

  .error-container {
    text-align: center;
    max-width: 600px;
    width: 100%;
  }

  .error-illustration {
    margin-bottom: 40px;

    .error-icon {
      animation: pulse 2s infinite;
    }
  }

  .error-content {
    .error-title {
      font-size: 32px;
      color: #303133;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .error-description {
      font-size: 16px;
      color: #606266;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .error-suggestion {
      font-size: 14px;
      color: #909399;
      margin-bottom: 12px;
      text-align: left;
      max-width: 300px;
      margin-left: auto;
      margin-right: auto;
    }

    .suggestion-list {
      text-align: left;
      max-width: 300px;
      margin: 0 auto 32px;
      padding-left: 20px;

      li {
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 32px;

      .el-button {
        min-width: 120px;
      }
    }

    .network-status {
      .status-text {
        font-size: 14px;
        color: #606266;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .status-checking {
          color: #E6A23C;
        }

        .status-online {
          color: #67C23A;
        }

        .status-offline {
          color: #F56C6C;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@media (max-width: 768px) {
  .network-error {
    .error-content {
      .error-title {
        font-size: 24px;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 200px;
        }
      }
    }
  }
}
</style>
