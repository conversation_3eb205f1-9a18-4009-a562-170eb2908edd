import{u as se,c as N,E as k,D as ue,k as me,r as y,o as de,g as C,b as p,l as r,d as o,i as B,e as l,f as m,m as b,t as d,j as te,G as ae,h as O,F as ee,v as pe,p as he,q as ce,s as _e,H as ye,I as Z}from"./index-BwbJZous.js";import{a as we,d as Ce}from"./resource-Dv4iJ0T9.js";import{g as ge,a as ie,b as ke,d as be}from"./comment-CFInYsEe.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";function xe(){const g=se(),A=me(),x=N(()=>g.isLoggedIn),R=N(()=>g.user),T=N(()=>{var i;return((i=g.user)==null?void 0:i.role)===1}),n=async(i="此操作",w=!0)=>{try{await ue.confirm(`${i}需要登录，是否前往登录页面？`,"需要登录",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"info",center:!0}),A.push({path:"/login",query:{redirect:A.currentRoute.value.fullPath}})}catch{w||k.info("操作已取消")}};return{isLoggedIn:x,currentUser:R,isAdmin:T,showLoginPrompt:n,checkAuthAndExecute:async(i,w={})=>{const{requireAuth:E=!0,requireAdmin:$=!1,actionName:D="此操作",showPrompt:F=!0}=w;if(!E)return await i();if(!x.value)return F?await n(D):k.warning(`${D}需要登录`),!1;if($&&!T.value)return k.error("权限不足，需要管理员权限"),!1;try{return await i()}catch(I){return console.error("操作执行失败:",I),k.error("操作失败，请稍后重试"),!1}},hasPermission:i=>{switch(i){case"login":return x.value;case"admin":return T.value;default:return!0}},getActionConfig:i=>({download:{requireAuth:!0,actionName:"下载资料",icon:"Download",text:x.value?"下载":"登录后下载"},favorite:{requireAuth:!0,actionName:"收藏资料",icon:"Star",text:x.value?"收藏":"登录后收藏"},comment:{requireAuth:!0,actionName:"发表评论",icon:"ChatDotRound",text:x.value?"发表评论":"登录后评论"},upload:{requireAuth:!0,actionName:"上传资料",icon:"Upload",text:x.value?"上传资料":"登录后上传"},admin:{requireAuth:!0,requireAdmin:!0,actionName:"管理操作",icon:"Setting",text:"管理"}})[i]||{requireAuth:!1,actionName:"操作",text:"操作"}}}const Re=["data-comment-id"],Te={class:"comment-header"},$e={class:"user-info"},Ie={class:"user-details"},Ae={class:"username"},De={class:"comment-time"},qe={key:0,class:"comment-actions"},Se={class:"comment-content"},Le={key:0,class:"comment-replies"},Ee=["data-comment-id"],Ne={class:"reply-header"},Ue={class:"user-info"},Be={class:"user-details"},Pe={class:"username"},ze={class:"comment-time"},Fe={key:0,class:"comment-actions"},Ve={class:"reply-content"},Me={key:1,class:"load-replies"},je={__name:"CommentItem",props:{comment:{type:Object,required:!0},showActions:{type:Boolean,default:!0}},emits:["reply","delete","refresh-replies"],setup(g,{expose:A,emit:x}){const R=g,T=x,n=se(),S=y([]),L=y(!1),U=y(!1),i=N(()=>{var h,P;const c=((h=R.comment.user)==null?void 0:h.nickname)||((P=R.comment.user)==null?void 0:P.username)||"";return c?c.charAt(0).toUpperCase():"U"}),w=N(()=>n.isLoggedIn&&(n.user.id===R.comment.userId||n.isAdmin)),E=N(()=>!U.value&&R.comment.replyCount>0),$=c=>c?c.charAt(0).toUpperCase():"U",D=c=>n.isLoggedIn&&(n.user.id===c.userId||n.isAdmin),F=()=>{T("reply",R.comment)},I=c=>{T("reply",c)},f=()=>{T("delete",R.comment.id)},oe=c=>{T("delete",c.id)},M=async()=>{if(!L.value){L.value=!0;try{const c=await ge(R.comment.id);S.value=c.data,U.value=!0}catch(c){console.error("Failed to load replies:",c)}finally{L.value=!1}}};return A({refreshReplies:async()=>{U.value=!1,await M()}}),de(()=>{M()}),(c,h)=>{var H,G,K;const P=C("el-avatar"),z=C("el-button");return r(),p("div",{class:"comment-item","data-comment-id":g.comment.id},[o("div",Te,[o("div",$e,[l(P,{size:40,src:(H=g.comment.user)==null?void 0:H.avatar},{default:m(()=>[b(d(i.value),1)]),_:1},8,["src"]),o("div",Ie,[o("div",Ae,d(((G=g.comment.user)==null?void 0:G.nickname)||((K=g.comment.user)==null?void 0:K.username)||"匿名用户"),1),o("div",De,d(te(ae)(g.comment.createTime)),1)])]),g.showActions?(r(),p("div",qe,[l(z,{type:"text",size:"small",onClick:F},{default:m(()=>h[0]||(h[0]=[b("回复")])),_:1,__:[0]}),w.value?(r(),O(z,{key:0,type:"text",size:"small",onClick:f},{default:m(()=>h[1]||(h[1]=[b("删除")])),_:1,__:[1]})):B("",!0)])):B("",!0)]),o("div",Se,[o("p",null,d(g.comment.content),1)]),S.value.length>0?(r(),p("div",Le,[(r(!0),p(ee,null,pe(S.value,v=>{var j,Q,J;return r(),p("div",{class:"reply-item",key:v.id,"data-comment-id":v.id},[o("div",Ne,[o("div",Ue,[l(P,{size:30,src:(j=v.user)==null?void 0:j.avatar},{default:m(()=>{var e,t;return[b(d($(((e=v.user)==null?void 0:e.nickname)||((t=v.user)==null?void 0:t.username))),1)]}),_:2},1032,["src"]),o("div",Be,[o("div",Pe,d(((Q=v.user)==null?void 0:Q.nickname)||((J=v.user)==null?void 0:J.username)||"匿名用户"),1),o("div",ze,d(te(ae)(v.createTime)),1)])]),g.showActions?(r(),p("div",Fe,[l(z,{type:"text",size:"small",onClick:e=>I(v)},{default:m(()=>h[2]||(h[2]=[b("回复")])),_:2,__:[2]},1032,["onClick"]),D(v)?(r(),O(z,{key:0,type:"text",size:"small",onClick:e=>oe(v)},{default:m(()=>h[3]||(h[3]=[b("删除")])),_:2,__:[3]},1032,["onClick"])):B("",!0)])):B("",!0)]),o("div",Ve,[o("p",null,d(v.content),1)])],8,Ee)}),128))])):B("",!0),E.value?(r(),p("div",Me,[l(z,{type:"text",onClick:M},{default:m(()=>[b(d(L.value?"加载中...":"查看更多回复"),1)]),_:1})])):B("",!0)],8,Re)}}},Oe=ve(je,[["__scopeId","data-v-54cf9188"]]),He={class:"resource-detail"},Ge={class:"resource-header"},Ke={class:"resource-title"},Qe={class:"resource-meta"},Je={class:"meta-item"},We={class:"meta-item"},Xe={class:"meta-item"},Ye={class:"resource-actions"},Ze={class:"resource-description card"},et={class:"resource-comments card"},tt={key:0,class:"comment-form"},ot={class:"comment-form-actions"},nt={key:1,class:"login-to-comment"},at={class:"comments-list"},st={key:0,class:"pagination"},lt={__name:"ResourceDetail",setup(g){const A=he(),x=me();se();const{isLoggedIn:R,checkAuthAndExecute:T}=xe(),n=y(null),S=y(!1),L=y([]),U=y(!1),i=y(0),w=y(1),E=y(10),$=y(""),D=y(null),F=y(0),I=N(()=>Number(A.params.id)),f=N(()=>A.query.commentId?Number(A.query.commentId):null),oe=N(()=>n.value?["考研","考公","法考","教资","其他"][n.value.examType]||"未知":""),M=async()=>{S.value=!0;try{const e=await we(I.value);n.value=e.data}catch(e){console.error("Failed to fetch resource details:",e),k.error("获取资料详情失败")}finally{S.value=!1}},V=async()=>{U.value=!0;try{const e=await ie(I.value,w.value,E.value);L.value=e.data.records,i.value=e.data.total,F.value++,f.value?(console.log("Found target comment ID in URL:",f.value),await Z(),J()):console.log("No target comment ID in URL, route.query:",A.query)}catch(e){console.error("Failed to fetch comments:",e)}finally{U.value=!1}},c=async()=>await T(async()=>{try{const e=await Ce(I.value);if(!e||!e.data)throw new Error("下载响应为空");const t=e.data;if(!(t instanceof Blob)||t.size===0)throw new Error("下载的文件为空或格式错误");console.log("Downloaded blob size:",t.size,"bytes"),console.log("Downloaded blob type:",t.type);let a=n.value.originalFilename||n.value.name||`resource_${I.value}`;const s=window.URL.createObjectURL(t),u=document.createElement("a");u.href=s,u.download=a,document.body.appendChild(u),u.click(),document.body.removeChild(u),window.URL.revokeObjectURL(s),console.log("Download initiated for:",a,"Size:",t.size,"bytes")}catch(e){console.error("Download failed:",e),k.error("下载失败："+(e.message||"请稍后重试"))}},{actionName:"下载资料"}),h=()=>{var s,u;const t=(s=(n.value.originalFilename||n.value.name).split(".").pop())==null?void 0:s.toLowerCase();if(["pdf","txt","jpg","jpeg","png","gif","mp4","mp3"].includes(t)){const q=`${((u=window.appConfig)==null?void 0:u.apiBaseURL)||"http://localhost:8081"}/api/resources/view/${I.value}`;window.open(q,"_blank")}else k.warning({message:`${t.toUpperCase()} 文件无法在线预览，将在 2 秒后自动下载`,duration:2e3,showClose:!0}),setTimeout(()=>{c()},2e3)},P=async()=>{if($.value.trim())return await T(async()=>{try{const e={resourceId:I.value,content:$.value};D.value&&(e.parentId=D.value.id),await ke(e),k.success(D.value?"回复成功":"评论成功"),await new Promise(t=>setTimeout(t,200)),await V(),$.value="",D.value=null,n.value&&(n.value.commentCount+=1)}catch(e){console.error("Failed to submit comment:",e),k.error("评论失败，请稍后重试")}},{actionName:"发表评论"})},z=e=>{var s,u;D.value=e;const t=((s=e.user)==null?void 0:s.nickname)||((u=e.user)==null?void 0:u.username)||`用户${e.userId}`;$.value=`@${t} `;const a=document.querySelector(".comment-form");a&&a.scrollIntoView({behavior:"smooth"})},H=async e=>{try{await ue.confirm("确定要删除这条评论吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await be(e),k.success("评论已删除"),V(),n.value&&(n.value.commentCount-=1)}catch(t){t!=="cancel"&&(console.error("Failed to delete comment:",t),k.error("删除评论失败"))}},G=e=>{w.value=e,V()},K=async()=>{if(!f.value)return null;console.log("Searching for comment",f.value,"across all pages");const e=Math.ceil(i.value/E.value);console.log("Total pages to search:",e);for(let t=1;t<=e;t++)try{console.log("Searching page",t);const s=(await ie(I.value,t,E.value)).data.records;if(s.find(_=>String(_.id)===String(f.value)))return console.log("Found target comment (top-level) on page",t),{page:t,parentId:null};for(const _ of s)if(_.replyCount>0)try{if((await ge(_.id)).data.find(ne=>String(ne.id)===String(f.value)))return console.log("Found target comment (reply) on page",t,"under parent",_.id),{page:t,parentId:_.id}}catch(q){console.error("Error loading replies for comment",_.id,q)}}catch(a){console.error("Error searching page",t,a)}return console.log("Target comment not found in any page"),null},v=async e=>{console.log("Ensuring replies for parent",e,"are loaded...");const t=document.querySelector(`[data-comment-id="${e}"]`);if(t){const a=t.querySelector(".load-replies .el-button");a&&a.textContent.includes("查看更多回复")?(console.log("Clicking load more replies button for parent",e),a.click(),await new Promise(s=>setTimeout(s,1e3))):console.log("No load more button found for parent",e,"replies might already be loaded")}else console.log("Parent element not found for ID",e)},j=async()=>{console.log("Ensuring all replies are loaded...");const e=document.querySelectorAll(".load-replies .el-button");console.log("Found",e.length,"load more buttons");for(const t of e)t.textContent.includes("查看更多回复")&&(console.log("Clicking load more replies button"),t.click(),await new Promise(a=>setTimeout(a,500)))},Q=async(e=15)=>{for(let t=0;t<e;t++){const a=document.querySelector(`[data-comment-id="${f.value}"]`);if(a)return console.log("Target element found after",t+1,"attempts"),a;await new Promise(s=>setTimeout(s,300))}return null},J=async()=>{if(console.log("scrollToTargetComment called, targetCommentId:",f.value),!f.value){console.log("No target comment ID found");return}let e=document.querySelector(`[data-comment-id="${f.value}"]`);if(console.log("Target element found on current page:",e),e||(console.log("Target not found, trying to load all replies first..."),await j(),await Z(),e=document.querySelector(`[data-comment-id="${f.value}"]`),console.log("Target element found after loading replies:",e)),!e){console.log("Target not found on current page, searching other pages...");const t=await K();if(t){const{page:a,parentId:s}=t;a!==w.value&&(console.log("Switching to page",a),w.value=a,await V(),await Z()),s?(console.log("Target is a reply under parent",s,"ensuring parent replies are loaded"),await v(s)):await j(),await Z(),console.log("After page switch and reply loading, checking DOM...");const u=document.querySelectorAll("[data-comment-id]");console.log("All comment elements found after page switch:",u.length),u.forEach((_,q)=>{const W=_.getAttribute("data-comment-id"),X=_.classList.contains("reply-item");console.log(`  ${q+1}. Comment ID: ${W} (${X?"reply":"top-level"})`)}),e=await Q()}}if(e){console.log("Scrolling to target comment and adding highlight"),e.scrollIntoView({behavior:"smooth",block:"center"}),e.classList.add("highlight-comment"),setTimeout(()=>{e.classList.remove("highlight-comment"),console.log("Highlight removed")},3e3);const t={...A.query};delete t.commentId,x.replace({query:t})}else console.log("Target element still not found after searching all pages"),k.warning("未找到指定的评论，可能已被删除")};return de(()=>{M(),V()}),(e,t)=>{const a=C("el-icon-download"),s=C("el-icon"),u=C("el-icon-chat-dot-round"),_=C("el-icon-clock"),q=C("el-button"),W=C("el-icon-view"),X=C("el-input"),ne=C("router-link"),fe=C("el-pagination"),le=C("el-empty"),re=_e("loading");return ce((r(),p("div",He,[n.value?(r(),p(ee,{key:0},[o("div",Ge,[o("div",Ke,[o("span",{class:ye(["exam-type-tag",`exam-type-${n.value.examType}`])},d(oe.value),3),o("h1",null,d(n.value.name),1)]),o("div",Qe,[o("div",Je,[l(s,null,{default:m(()=>[l(a)]),_:1}),o("span",null,d(n.value.downloadCount)+" 下载",1)]),o("div",We,[l(s,null,{default:m(()=>[l(u)]),_:1}),o("span",null,d(n.value.commentCount)+" 评论",1)]),o("div",Xe,[l(s,null,{default:m(()=>[l(_)]),_:1}),o("span",null,d(te(ae)(n.value.auditTime)),1)])])]),o("div",Ye,[l(q,{type:"primary",onClick:c},{default:m(()=>[l(s,null,{default:m(()=>[l(a)]),_:1}),t[1]||(t[1]=b(" 下载资料 "))]),_:1,__:[1]}),l(q,{type:"success",onClick:h},{default:m(()=>[l(s,null,{default:m(()=>[l(W)]),_:1}),t[2]||(t[2]=b(" 在线查看 "))]),_:1,__:[2]})]),o("div",Ze,[t[3]||(t[3]=o("h3",null,"资料简介",-1)),o("p",null,d(n.value.description||"暂无简介"),1)]),o("div",et,[o("h3",null,"评论区 ("+d(n.value.commentCount)+")",1),te(R)?(r(),p("div",tt,[l(X,{modelValue:$.value,"onUpdate:modelValue":t[0]||(t[0]=Y=>$.value=Y),type:"textarea",rows:3,placeholder:"发表您的评论...",maxlength:"500","show-word-limit":""},null,8,["modelValue"]),o("div",ot,[l(q,{type:"primary",disabled:!$.value.trim(),onClick:P},{default:m(()=>t[4]||(t[4]=[b(" 发表评论 ")])),_:1,__:[4]},8,["disabled"])])])):(r(),p("div",nt,[l(ne,{to:"/login"},{default:m(()=>t[5]||(t[5]=[b("登录")])),_:1,__:[5]}),t[6]||(t[6]=b(" 后参与评论 "))])),ce((r(),p("div",at,[L.value.length>0?(r(),p(ee,{key:0},[(r(!0),p(ee,null,pe(L.value,Y=>(r(),O(Oe,{key:`${Y.id}-${F.value}`,comment:Y,onReply:z,onDelete:H},null,8,["comment"]))),128)),i.value>E.value?(r(),p("div",st,[l(fe,{background:"",layout:"prev, pager, next",total:i.value,"page-size":E.value,"current-page":w.value,onCurrentChange:G},null,8,["total","page-size","current-page"])])):B("",!0)],64)):(r(),O(le,{key:1,description:"暂无评论"}))])),[[re,U.value]])])],64)):S.value?B("",!0):(r(),O(le,{key:1,description:"资料不存在或已被删除"}))])),[[re,S.value]])}}},mt=ve(lt,[["__scopeId","data-v-4979992b"]]);export{mt as default};
