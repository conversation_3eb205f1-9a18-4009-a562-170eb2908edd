package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * User favorite entity
 */
@Data
@Accessors(chain = true)
@TableName("user_favorite")
public class UserFavorite {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * User ID
     */
    private Long userId;
    
    /**
     * Resource ID
     */
    private Long resourceId;
    
    /**
     * Create time
     */
    private LocalDateTime createTime;
    
    /**
     * Logical delete flag
     */
    private Integer deleted;
}
