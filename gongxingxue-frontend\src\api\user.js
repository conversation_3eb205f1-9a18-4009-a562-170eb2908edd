import request from '../utils/request'
import { getToken } from '../utils/auth'

/**
 * Send email verification code
 * @param {string} email - Email address
 * @returns {Promise} - API response
 */
export function sendEmailCode(email) {
  console.log('Send email code request:', { email })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // Encode parameters in URL
    const url = `/api/users/email/send-code?email=${encodeURIComponent(email)}`
    xhr.open('POST', url, true)

    // Set authorization header
    const token = getToken()
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Send email code success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Send email code failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during send email code')
      reject(new Error('Network error during send email code'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * Bind email to user account
 * @param {string} email - Email address
 * @param {string} code - Verification code
 * @returns {Promise} - API response
 */
export function bindEmail(email, code) {
  console.log('Bind email request:', { email, code })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // Encode parameters in URL
    const url = `/api/users/email/bind?email=${encodeURIComponent(email)}&code=${encodeURIComponent(code)}`
    xhr.open('POST', url, true)

    // Set authorization header
    const token = getToken()
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Bind email success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Bind email failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during bind email')
      reject(new Error('Network error during bind email'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * Unbind email from user account
 * @returns {Promise} - API response
 */
export function unbindEmail() {
  console.log('Unbind email request')

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    const url = `/api/users/email/unbind`
    xhr.open('DELETE', url, true)

    // Set authorization header
    const token = getToken()
    if (token) {
      xhr.setRequestHeader('Authorization', `Bearer ${token}`)
    }

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Unbind email success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Unbind email failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during unbind email')
      reject(new Error('Network error during unbind email'))
    }

    // Send the request
    xhr.send()
  })
}
