<template>
  <div class="register-page">
    <div class="register-container">
      <h2 class="title">用户注册</h2>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="0"
        @submit.prevent="handleRegister"
      >
        <el-form-item prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="用户名 (6-20位字符)"
            prefix-icon="el-icon-user"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="密码 (8-16位数字+字母组合)"
            prefix-icon="el-icon-lock"
            show-password
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="确认密码"
            prefix-icon="el-icon-lock"
            show-password
          />
        </el-form-item>

        <el-form-item prop="nickname">
          <el-input
            v-model="registerForm.nickname"
            placeholder="昵称 (选填)"
            prefix-icon="el-icon-user"
          />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="registerForm.captcha"
              placeholder="验证码"
              prefix-icon="el-icon-picture"
              @keyup.enter="handleRegister"
              style="flex: 1;"
            />
            <img
              :src="captchaUrl"
              @click="refreshCaptcha"
              class="captcha-image"
              title="点击刷新验证码"
              alt="验证码"
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="register-button"
            @click="handleRegister"
          >
            注册
          </el-button>
        </el-form-item>
      </el-form>

      <div class="register-footer">
        <span>已有账号？</span>
        <router-link to="/login">立即登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'

const router = useRouter()
const userStore = useUserStore()

// Form data
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  captcha: ''
})

// Form validation rules
const validateUsername = (rule, value, callback) => {
  if (value.length < 6 || value.length > 20) {
    callback(new Error('用户名长度必须在6-20个字符之间'))
  } else {
    callback()
  }
}

const validatePassword = (rule, value, callback) => {
  if (value.length < 8 || value.length > 16) {
    callback(new Error('密码长度必须在8-16个字符之间'))
  } else if (!/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(value)) {
    callback(new Error('密码必须包含字母和数字'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const registerRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { validator: validateUsername, trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  nickname: [
    { max: 20, message: '昵称不能超过20个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 4, message: '验证码为4位字符', trigger: 'blur' }
  ]
}

// Form ref
const registerFormRef = ref(null)

// Loading state
const loading = ref(false)

// Captcha
const captchaUrl = ref('')

// Refresh captcha
const refreshCaptcha = () => {
  captchaUrl.value = `/api/auth/captcha?t=${Date.now()}`
}

// Handle register
const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true

    try {
      await userStore.register(
        registerForm.username,
        registerForm.password,
        registerForm.nickname || undefined,
        registerForm.captcha
      )

      ElMessage.success('注册成功，请登录')
      router.push('/login')
    } catch (error) {
      console.error('Registration failed:', error)

      // 刷新验证码
      refreshCaptcha()
      registerForm.captcha = ''

      ElMessage.error(error.message || '注册失败，请稍后重试')
    } finally {
      loading.value = false
    }
  })
}

// Load captcha on component mount
onMounted(() => {
  refreshCaptcha()
})
</script>

<style lang="scss" scoped>
.register-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 60px - 60px); /* Subtract header and footer height */

  .register-container {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .title {
      text-align: center;
      margin-bottom: 30px;
      color: #303133;
    }

    .register-button {
      width: 100%;
    }

    .captcha-container {
      display: flex;
      align-items: center;
      gap: 10px;

      .captcha-image {
        width: 120px;
        height: 40px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }
      }
    }

    .register-footer {
      margin-top: 20px;
      text-align: center;
      font-size: 14px;
      color: #606266;

      a {
        color: #409eff;
        margin-left: 5px;
      }
    }
  }
}
</style>
