import{b as w,l as v,d as o,i as V,H as T,t as $,u as q,o as L,E as s,k as M,x as N,r as b,h as D,e as l,f as a,g as p,m as S,J as E}from"./index-BC3U7MsG.js";import{_ as h}from"./_plugin-vue_export-helper-DlAUqK2U.js";const P={class:"spinner-container"},H={key:0,class:"loading-text"},I={__name:"LoadingSpinner",props:{fullScreen:{type:Boolean,default:!1},size:{type:String,default:"medium",validator:u=>["small","medium","large"].includes(u)},text:{type:String,default:""}},setup(u){return(x,_)=>(v(),w("div",{class:T(["loading-spinner",{"full-screen":u.fullScreen}])},[o("div",P,[o("div",{class:T(["spinner",u.size])},_[0]||(_[0]=[o("div",{class:"spinner-ring"},null,-1),o("div",{class:"spinner-ring"},null,-1),o("div",{class:"spinner-ring"},null,-1),o("div",{class:"spinner-ring"},null,-1)]),2),u.text?(v(),w("div",H,$(u.text),1)):V("",!0)])],2))}},j=h(I,[["__scopeId","data-v-49702b95"]]),A={class:"upload-resource"},J={class:"upload-form-container"},O="#",W={__name:"UploadResource",setup(u){const x=M(),_=q();L(()=>{if(!_.isLoggedIn){s.warning("请先登录后再上传资料"),x.push("/login?redirect=/upload-resource");return}});const r=N({name:"",examType:null,description:""}),d=b([]),k={name:[{required:!0,message:"请输入资料名称",trigger:"blur"},{max:50,message:"资料名称不能超过50个字符",trigger:"blur"}],examType:[{required:!0,message:"请选择考试类型",trigger:"change"}],description:[{max:200,message:"资料简介不能超过200个字符",trigger:"blur"}],file:[{required:!0,message:"请上传资料文件",trigger:"change"}]},z=b(null),m=b(!1),R=(n,e)=>{if(n.size>31457280){s.error("文件大小不能超过30MB"),d.value=[];return}d.value=[n]},B=()=>{d.value=[]},F=async()=>{if(!r.name){s.error("请输入资料名称");return}if(r.examType===null){s.error("请选择考试类型");return}if(d.value.length===0){s.error("请上传资料文件");return}m.value=!0;try{const n=new FormData;n.append("name",r.name),n.append("examType",r.examType),r.description&&n.append("description",r.description);const e=d.value[0],f=e.raw||e;n.append("file",f);const t=new XMLHttpRequest;t.open("POST","/api/resources/upload",!0);const i=E();i&&t.setRequestHeader("Authorization",`Bearer ${i}`),t.onload=function(){if(t.status>=200&&t.status<300)try{const g=JSON.parse(t.responseText);g.success?(s.success("资料上传成功，请等待管理员审核"),x.push("/my-resources")):s.error(g.message||"上传失败，请稍后重试")}catch{s.error("响应解析失败，请稍后重试")}else s.error("上传失败，请稍后重试");m.value=!1},t.onerror=function(){s.error("网络错误，请稍后重试"),m.value=!1},t.send(n)}catch{s.error("上传失败，请稍后重试"),m.value=!1}};return(n,e)=>{const f=p("el-input"),t=p("el-form-item"),i=p("el-option"),g=p("el-select"),y=p("el-button"),C=p("el-upload"),U=p("el-form");return v(),w("div",A,[m.value?(v(),D(j,{key:0,"full-screen":!0,size:"large",text:"正在上传文件，请稍候..."})):V("",!0),e[8]||(e[8]=o("h2",{class:"page-title"},"上传资料",-1)),o("div",J,[l(U,{ref_key:"uploadFormRef",ref:z,model:r,rules:k,"label-width":"100px"},{default:a(()=>[l(t,{label:"资料名称",prop:"name"},{default:a(()=>[l(f,{modelValue:r.name,"onUpdate:modelValue":e[0]||(e[0]=c=>r.name=c),placeholder:"请输入资料名称（不超过50字）",maxlength:"50","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(t,{label:"考试类型",prop:"examType"},{default:a(()=>[l(g,{modelValue:r.examType,"onUpdate:modelValue":e[1]||(e[1]=c=>r.examType=c),placeholder:"请选择考试类型"},{default:a(()=>[l(i,{label:"考研",value:0}),l(i,{label:"考公",value:1}),l(i,{label:"法考",value:2}),l(i,{label:"教资",value:3}),l(i,{label:"其他",value:4})]),_:1},8,["modelValue"])]),_:1}),l(t,{label:"资料简介",prop:"description"},{default:a(()=>[l(f,{modelValue:r.description,"onUpdate:modelValue":e[2]||(e[2]=c=>r.description=c),type:"textarea",rows:4,placeholder:"请输入资料简介（不超过200字）",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),l(t,{label:"上传文件",prop:"file"},{default:a(()=>[l(C,{class:"upload-file",action:O,"auto-upload":!1,limit:1,"on-change":R,"on-remove":B,"file-list":d.value,multiple:!1,accept:".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar,.7z,.jpg,.jpeg,.png,.gif,.mp4,.mp3,.avi,.mov,.wmv,.flv"},{trigger:a(()=>[l(y,{type:"primary"},{default:a(()=>e[4]||(e[4]=[S("选择文件")])),_:1,__:[4]})]),tip:a(()=>e[5]||(e[5]=[o("div",{class:"el-upload__tip"}," 支持PDF/Word/Excel/PPT/视频/音频等常见格式，单文件不超过30MB ",-1)])),_:1},8,["file-list"])]),_:1}),l(t,null,{default:a(()=>[l(y,{type:"primary",loading:m.value,onClick:F},{default:a(()=>e[6]||(e[6]=[S("上传资料")])),_:1,__:[6]},8,["loading"]),l(y,{onClick:e[3]||(e[3]=c=>n.$router.push("/"))},{default:a(()=>e[7]||(e[7]=[S("取消")])),_:1,__:[7]})]),_:1})]),_:1},8,["model"])]),e[9]||(e[9]=o("div",{class:"upload-notice"},[o("h3",null,"上传须知"),o("ul",null,[o("li",null,"上传的资料需经过管理员审核后才能显示在首页"),o("li",null,"请勿上传侵权、违法或低质量的资料"),o("li",null,"资料审核通常在1-2个工作日内完成"),o("li",null,'您可以在"我的资料"中查看资料审核状态')])],-1))])}}},K=h(W,[["__scopeId","data-v-550358c8"]]);export{K as default};
