<template>
  <div class="search-results">
    <h2 class="page-title">搜索结果</h2>

    <div class="search-info">
      <template v-if="keyword">
        <span>关键词: <strong>{{ keyword }}</strong></span>
      </template>
      <template v-if="examType !== null">
        <span>考试类型: <strong>{{ examTypeText }}</strong></span>
      </template>
      <span>共找到 <strong>{{ total }}</strong> 条结果</span>
    </div>

    <!-- 高级筛选面板 -->
    <div class="advanced-filters">
      <el-card>
        <template #header>
          <div class="filter-header">
            <span>筛选条件</span>
            <el-button
              type="text"
              @click="toggleAdvancedFilters"
              class="toggle-btn"
            >
              {{ showAdvancedFilters ? '收起' : '展开' }}
              <el-icon>
                <ArrowDown v-if="!showAdvancedFilters" />
                <ArrowUp v-else />
              </el-icon>
            </el-button>
          </div>
        </template>

        <!-- 基础筛选 -->
        <div class="basic-filters">
          <div class="filter-row">
            <div class="filter-item">
              <label>考试类型：</label>
              <el-radio-group v-model="selectedExamType" @change="handleFilterChange">
                <el-radio-button :label="null">全部</el-radio-button>
                <el-radio-button :label="0">考研</el-radio-button>
                <el-radio-button :label="1">考公</el-radio-button>
                <el-radio-button :label="2">法考</el-radio-button>
                <el-radio-button :label="3">教资</el-radio-button>
                <el-radio-button :label="4">其他</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <label>排序方式：</label>
              <el-radio-group v-model="sortType" @change="handleFilterChange">
                <el-radio-button :label="'createTime'">最新上传</el-radio-button>
                <el-radio-button :label="'downloadCount'">下载最多</el-radio-button>
                <el-radio-button :label="'favoriteCount'">收藏最多</el-radio-button>
                <el-radio-button :label="'commentCount'">评论最多</el-radio-button>
                <el-radio-button :label="'relevance'">综合热度</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </div>

        <!-- 高级筛选 -->
        <div v-show="showAdvancedFilters" class="advanced-filters-content">
          <div class="filter-row">
            <div class="filter-item">
              <label>文件类型：</label>
              <el-radio-group v-model="selectedFileType" @change="handleFilterChange">
                <el-radio-button :label="null">全部</el-radio-button>
                <el-radio-button label="PDF">PDF</el-radio-button>
                <el-radio-button label="DOC">Word</el-radio-button>
                <el-radio-button label="PPT">PPT</el-radio-button>
                <el-radio-button label="XLS">Excel</el-radio-button>
                <el-radio-button label="IMAGE">图片</el-radio-button>
                <el-radio-button label="OTHER">其他</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <label>上传时间：</label>
              <el-radio-group v-model="selectedTimeRange" @change="handleFilterChange">
                <el-radio-button :label="null">全部</el-radio-button>
                <el-radio-button label="today">今天</el-radio-button>
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="quarter">三个月内</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-item">
              <label>下载量：</label>
              <el-radio-group v-model="selectedDownloadRange" @change="handleFilterChange">
                <el-radio-button :label="null">全部</el-radio-button>
                <el-radio-button label="0-10">10次以下</el-radio-button>
                <el-radio-button label="10-50">10-50次</el-radio-button>
                <el-radio-button label="50-100">50-100次</el-radio-button>
                <el-radio-button label="100+">100次以上</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="filter-actions">
            <el-button @click="resetFilters">重置筛选</el-button>
            <el-button type="primary" @click="applyFilters">应用筛选</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <div class="resources-list" v-loading="loading">
      <template v-if="resources.length > 0">
        <resource-card
          v-for="resource in resources"
          :key="resource.id"
          :resource="resource"
          @click="viewResource(resource.id)"
        />

        <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </template>

      <el-empty v-else description="未找到相关资料" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getPublicResources } from '../api/resource'
import ResourceCard from '../components/ResourceCard.vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// Data
const resources = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// Filter states
const selectedExamType = ref(null)
const sortType = ref('createTime') // createTime, downloadCount, favoriteCount, commentCount, relevance
const selectedFileType = ref(null)
const selectedTimeRange = ref(null)
const selectedDownloadRange = ref(null)
const showAdvancedFilters = ref(false)

// Computed
const keyword = computed(() => route.query.keyword || '')
const examType = computed(() => {
  const type = route.query.examType
  return type !== undefined ? (type === 'null' ? null : Number(type)) : null
})
const examTypeText = computed(() => {
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return selectedExamType.value !== null ? examTypes[selectedExamType.value] : '全部'
})

// Fetch resources
const fetchResources = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      sortType: sortType.value,
      keyword: keyword.value
    }

    // Add filter parameters
    if (selectedExamType.value !== null) {
      params.examType = selectedExamType.value
    }

    if (selectedFileType.value) {
      params.fileType = selectedFileType.value
    }

    if (selectedTimeRange.value) {
      params.timeRange = selectedTimeRange.value
    }

    if (selectedDownloadRange.value) {
      params.downloadRange = selectedDownloadRange.value
    }

    const response = await getPublicResources(params)
    resources.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to fetch resources:', error)
  } finally {
    loading.value = false
  }
}

// Handle filter change
const handleFilterChange = () => {
  currentPage.value = 1
  fetchResources()
}

// Toggle advanced filters
const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

// Reset all filters
const resetFilters = () => {
  selectedExamType.value = null
  sortType.value = 'createTime'
  selectedFileType.value = null
  selectedTimeRange.value = null
  selectedDownloadRange.value = null
  currentPage.value = 1
  fetchResources()
}

// Apply filters (same as handleFilterChange, but with user feedback)
const applyFilters = () => {
  currentPage.value = 1
  fetchResources()
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchResources()
}

// View resource details
const viewResource = (id) => {
  router.push({ name: 'ResourceDetail', params: { id } })
}



// Initialize from route query
const initFromQuery = () => {
  if (examType.value !== null) {
    selectedExamType.value = examType.value
  }
}

// Watch for route query changes
watch(
  () => route.query,
  () => {
    initFromQuery()
    fetchResources()
  }
)

// Initialize on component mount
onMounted(() => {
  initFromQuery()
  fetchResources()
})
</script>

<style lang="scss" scoped>
.search-results {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .search-info {
    margin-bottom: 20px;
    color: #606266;

    span {
      margin-right: 20px;

      strong {
        color: #409eff;
      }
    }
  }

  .advanced-filters {
    margin-bottom: 20px;

    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .toggle-btn {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }

    .basic-filters,
    .advanced-filters-content {
      .filter-row {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .filter-item {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 10px;

          label {
            font-weight: 500;
            color: #303133;
            min-width: 80px;
            flex-shrink: 0;
          }

          .el-radio-group {
            flex: 1;

            // 修复按钮边框显示问题
            .el-radio-button {
              &:not(:first-child) {
                margin-left: -1px;
              }

              .el-radio-button__inner {
                border: 1px solid #dcdfe6;
                border-radius: 0;

                &:hover {
                  border-color: #409eff;
                  z-index: 1;
                  position: relative;
                }
              }

              &:first-child .el-radio-button__inner {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
              }

              &:last-child .el-radio-button__inner {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
              }

              &.is-active .el-radio-button__inner {
                border-color: #409eff;
                background-color: #409eff;
                color: #fff;
                z-index: 2;
                position: relative;
              }
            }
          }
        }
      }
    }

    .advanced-filters-content {
      border-top: 1px solid #ebeef5;
      padding-top: 15px;
      margin-top: 15px;
    }

    .filter-actions {
      margin-top: 20px;
      text-align: right;

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .resources-list {
    min-height: 300px;

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .search-results {
    .search-info {
      display: flex;
      flex-direction: column;

      span {
        margin-bottom: 5px;
      }
    }

    .advanced-filters {
      .basic-filters,
      .advanced-filters-content {
        .filter-row {
          .filter-item {
            flex-direction: column;
            align-items: flex-start;

            label {
              min-width: auto;
              margin-bottom: 8px;
              font-size: 14px;
            }

            .el-radio-group {
              width: 100%;
              display: flex;
              flex-wrap: wrap;
              gap: 5px;

              .el-radio-button {
                margin-bottom: 5px;
                flex: 0 0 auto;

                // 移动端按钮边框修复
                &:not(:first-child) {
                  margin-left: 0; // 移动端不需要负边距
                }

                .el-radio-button__inner {
                  border: 1px solid #dcdfe6;
                  border-radius: 4px; // 移动端每个按钮都有圆角
                  min-width: 60px;

                  &:hover {
                    border-color: #409eff;
                  }
                }

                &.is-active .el-radio-button__inner {
                  border-color: #409eff;
                  background-color: #409eff;
                  color: #fff;
                }
              }
            }
          }
        }
      }

      .filter-actions {
        text-align: center;
        margin-top: 15px;

        .el-button {
          margin: 5px;
          min-width: 80px;
        }
      }
    }
  }
}
</style>
