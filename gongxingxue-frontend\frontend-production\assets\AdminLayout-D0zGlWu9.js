import{u as z,c as s,p as D,b as H,e,f as o,g as n,k as P,l as w,d as l,h as T,i as j,m as _,t as d,E as q}from"./index-BwbJZous.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";const J={class:"admin-layout"},K={class:"header-content"},O={class:"breadcrumb"},Q={class:"user-info"},W={class:"avatar-container"},X={class:"username"},Y={__name:"AdminLayout",setup(Z){const m=D(),i=P(),u=z(),p=s(()=>m),x=s(()=>m.path),g=s(()=>{var a;return((a=u.user)==null?void 0:a.avatar)||""}),f=s(()=>{var a,t;return((a=u.user)==null?void 0:a.nickname)||((t=u.user)==null?void 0:t.username)||""}),C=s(()=>{const a=f.value;return a?a.charAt(0).toUpperCase():"A"}),h=a=>{switch(a){case"profile":i.push({name:"Profile"});break;case"logout":u.logout(),q.success("已成功退出登录"),i.push({name:"Home"});break}};return(a,t)=>{const y=n("el-icon-menu"),r=n("el-icon"),c=n("el-menu-item"),A=n("el-icon-document"),N=n("el-icon-user"),B=n("el-icon-back"),E=n("el-menu"),V=n("el-aside"),v=n("el-breadcrumb-item"),R=n("el-breadcrumb"),S=n("el-avatar"),b=n("el-dropdown-item"),F=n("el-dropdown-menu"),I=n("el-dropdown"),L=n("el-header"),M=n("router-view"),U=n("el-main"),k=n("el-container");return w(),H("div",J,[e(k,null,{default:o(()=>[e(V,{width:"200px"},{default:o(()=>[t[4]||(t[4]=l("div",{class:"logo"},[l("h2",null,"管理后台")],-1)),e(E,{"default-active":x.value,class:"el-menu-vertical","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF",router:""},{default:o(()=>[e(c,{index:"/admin"},{default:o(()=>[e(r,null,{default:o(()=>[e(y)]),_:1}),t[0]||(t[0]=l("span",null,"控制台",-1))]),_:1,__:[0]}),e(c,{index:"/admin/resources"},{default:o(()=>[e(r,null,{default:o(()=>[e(A)]),_:1}),t[1]||(t[1]=l("span",null,"资料管理",-1))]),_:1,__:[1]}),e(c,{index:"/admin/users"},{default:o(()=>[e(r,null,{default:o(()=>[e(N)]),_:1}),t[2]||(t[2]=l("span",null,"用户管理",-1))]),_:1,__:[2]}),e(c,{index:"/"},{default:o(()=>[e(r,null,{default:o(()=>[e(B)]),_:1}),t[3]||(t[3]=l("span",null,"返回前台",-1))]),_:1,__:[3]})]),_:1},8,["default-active"])]),_:1,__:[4]}),e(k,null,{default:o(()=>[e(L,null,{default:o(()=>[l("div",K,[l("div",O,[e(R,{separator:"/"},{default:o(()=>[e(v,{to:{path:"/admin"}},{default:o(()=>t[5]||(t[5]=[_("管理后台")])),_:1,__:[5]}),p.value.meta.title?(w(),T(v,{key:0},{default:o(()=>[_(d(p.value.meta.title),1)]),_:1})):j("",!0)]),_:1})]),l("div",Q,[e(I,{trigger:"click",onCommand:h},{dropdown:o(()=>[e(F,null,{default:o(()=>[e(b,{command:"profile"},{default:o(()=>t[6]||(t[6]=[_("个人中心")])),_:1,__:[6]}),e(b,{command:"logout"},{default:o(()=>t[7]||(t[7]=[_("退出登录")])),_:1,__:[7]})]),_:1})]),default:o(()=>[l("div",W,[e(S,{size:32,src:g.value},{default:o(()=>[_(d(C.value),1)]),_:1},8,["src"]),l("span",X,d(f.value),1)])]),_:1})])])]),_:1}),e(U,null,{default:o(()=>[e(M)]),_:1})]),_:1})]),_:1})])}}},oe=G(Y,[["__scopeId","data-v-e223a6e3"]]);export{oe as default};
