package com.gongxingxue.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gongxingxue.common.Result;
import com.gongxingxue.entity.Resource;
import com.gongxingxue.service.UserFavoriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户收藏管理控制器
 */
@RestController
@RequestMapping("/favorites")
@RequiredArgsConstructor
@Api(tags = "收藏管理", description = "用户收藏资料相关接口")
public class UserFavoriteController {

    private final UserFavoriteService userFavoriteService;

    /**
     * 添加收藏
     */
    @PostMapping("/{resourceId}")
    @ApiOperation(value = "添加收藏", notes = "将指定资料添加到用户的收藏列表中，需要用户登录")
    @ApiResponses({
        @ApiResponse(code = 200, message = "收藏成功"),
        @ApiResponse(code = 400, message = "收藏失败，资源可能不存在或已收藏"),
        @ApiResponse(code = 401, message = "用户未登录")
    })
    public Result<Void> addFavorite(
            @ApiParam(value = "资料ID", required = true, example = "1")
            @PathVariable Long resourceId,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = userFavoriteService.addFavorite(userId, resourceId);

        if (success) {
            return Result.success("收藏成功", null);
        } else {
            return Result.error("收藏失败，资源可能不存在或已收藏");
        }
    }

    /**
     * Remove favorite
     */
    @DeleteMapping("/{resourceId}")
    @ApiOperation("Remove favorite")
    public Result<Void> removeFavorite(@PathVariable Long resourceId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = userFavoriteService.removeFavorite(userId, resourceId);

        if (success) {
            return Result.success("取消收藏成功", null);
        } else {
            return Result.error("取消收藏失败，可能未收藏该资源");
        }
    }

    /**
     * 切换收藏状态
     */
    @PutMapping("/{resourceId}/toggle")
    @ApiOperation(value = "切换收藏状态", notes = "如果已收藏则取消收藏，如果未收藏则添加收藏")
    @ApiResponses({
        @ApiResponse(code = 200, message = "操作成功，返回当前收藏状态"),
        @ApiResponse(code = 400, message = "操作失败"),
        @ApiResponse(code = 401, message = "用户未登录")
    })
    public Result<Boolean> toggleFavorite(
            @ApiParam(value = "资料ID", required = true, example = "1")
            @PathVariable Long resourceId,
            HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");

        try {
            boolean isFavorited = userFavoriteService.toggleFavorite(userId, resourceId);
            String message = isFavorited ? "收藏成功" : "取消收藏成功";
            return Result.success(message, isFavorited);
        } catch (Exception e) {
            return Result.error("操作失败：" + e.getMessage());
        }
    }

    /**
     * Check if favorited
     */
    @GetMapping("/check/{resourceId}")
    @ApiOperation("Check if favorited")
    public Result<Boolean> checkFavorited(@PathVariable Long resourceId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean isFavorited = userFavoriteService.isFavorited(userId, resourceId);
        return Result.success(isFavorited);
    }

    /**
     * Get my favorites
     */
    @GetMapping("/my")
    @ApiOperation("Get my favorites")
    public Result<Page<Resource>> getMyFavorites(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {

        Long userId = (Long) request.getAttribute("userId");
        Page<Resource> favorites = userFavoriteService.getUserFavorites(userId, page, size);
        return Result.success(favorites);
    }
}
