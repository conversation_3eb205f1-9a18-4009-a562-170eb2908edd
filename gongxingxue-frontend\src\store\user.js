import { defineStore } from 'pinia'
import { login, register, getCurrentUser } from '../api/auth'
import { setToken, removeToken, getToken } from '../utils/auth'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(), // 从localStorage初始化token
    user: null
  }),
  getters: {
    isLoggedIn: (state) => !!state.token,
    isAdmin: (state) => state.user && state.user.role === 1
  },
  actions: {
    async login(username, password, captcha) {
      try {
        console.log('User store: login attempt')
        const response = await login(username, password, captcha)
        console.log('User store: login response', response)

        if (!response.success) {
          throw new Error(response.message || '登录失败')
        }

        const { token, user } = response.data

        if (!token || !user) {
          throw new Error('登录响应缺少必要的数据')
        }

        console.log('User store: setting token and user')
        this.token = token
        this.user = user

        setToken(token)

        return Promise.resolve(user)
      } catch (error) {
        console.error('User store: login error', error)
        return Promise.reject(error)
      }
    },

    async register(username, password, nickname, captcha) {
      try {
        await register(username, password, nickname, captcha)
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },

    async fetchCurrentUser() {
      try {
        const response = await getCurrentUser()
        this.user = response.data
        return Promise.resolve(this.user)
      } catch (error) {
        this.logout()
        return Promise.reject(error)
      }
    },

    logout() {
      this.token = null
      this.user = null
      removeToken()
    }
  },
  persist: {
    key: 'user-store',
    storage: localStorage,
    paths: ['token']
  }
})
