#!/bin/bash

# =============================================================================
# 共行学项目 - 一键部署脚本（基于.env.prod配置文件）
# 适用于宝塔面板部署
# =============================================================================

echo "🚀 开始一键部署共行学项目..."

# =============================================================================
# 配置参数
# =============================================================================
APP_NAME="gongxingxue"
APP_DIR="/app"
JAR_FILE="gongxingxue-backend-0.0.1-SNAPSHOT.jar"
ENV_FILE=".env.prod"
FRONTEND_DIR="/www/wwwroot/************"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📋 部署配置："
echo "  - 项目根目录: $PROJECT_ROOT"
echo "  - 应用目录: $APP_DIR"
echo "  - 前端目录: $FRONTEND_DIR"
echo "  - 配置文件: $ENV_FILE"

# =============================================================================
# 检查必要文件
# =============================================================================
echo "📋 检查必要文件..."

# 检查配置文件
if [ ! -f "$PROJECT_ROOT/$ENV_FILE" ]; then
    echo "❌ 错误: 环境变量配置文件 $PROJECT_ROOT/$ENV_FILE 不存在"
    echo "请确保 .env.prod 文件在项目根目录"
    exit 1
fi

# 检查JAR文件
if [ ! -f "$SCRIPT_DIR/target/$JAR_FILE" ]; then
    echo "⚠️ JAR文件不存在，开始构建..."
    cd "$SCRIPT_DIR"

    # 构建应用
    echo "🔨 构建后端应用..."
    mvn clean package -DskipTests

    if [ $? -ne 0 ]; then
        echo "❌ 后端构建失败"
        exit 1
    fi

    if [ ! -f "target/$JAR_FILE" ]; then
        echo "❌ 构建完成但JAR文件不存在"
        exit 1
    fi
fi

# 检查前端文件
if [ ! -f "$PROJECT_ROOT/gongxingxue-frontend/dist/index.html" ]; then
    echo "⚠️ 前端文件不存在，开始构建..."
    cd "$PROJECT_ROOT/gongxingxue-frontend"

    # 构建前端
    echo "🔨 构建前端应用..."
    npm run build

    if [ $? -ne 0 ]; then
        echo "❌ 前端构建失败"
        exit 1
    fi
fi

echo "✅ 必要文件检查完成"

# =============================================================================
# 停止旧应用
# =============================================================================
echo "🛑 停止旧应用..."

# 查找并停止Java进程
JAVA_PID=$(ps aux | grep "gongxingxue-backend" | grep -v grep | awk '{print $2}')
if [ -n "$JAVA_PID" ]; then
    echo "停止进程 PID: $JAVA_PID"
    kill $JAVA_PID
    sleep 5

    # 检查是否还在运行
    if ps -p $JAVA_PID > /dev/null 2>&1; then
        echo "强制停止进程"
        kill -9 $JAVA_PID
    fi
    echo "✅ 旧应用已停止"
else
    echo "✅ 没有运行中的应用"
fi

# =============================================================================
# 加载环境变量
# =============================================================================
echo "📝 加载环境变量..."

# 加载.env.prod文件
set -a  # 自动导出所有变量
source "$PROJECT_ROOT/$ENV_FILE"
set +a

echo "✅ 环境变量加载完成"
echo "  - 数据库: $DB_NAME"
echo "  - 用户名: $DB_USERNAME"
echo "  - 服务器端口: $SERVER_PORT"
echo "  - 前端URL: $FRONTEND_URL"

# =============================================================================
# 检查数据库连接
# =============================================================================
echo "🗄️ 检查数据库连接..."

mysql -u "$DB_USERNAME" -p"$DB_PASSWORD" "$DB_NAME" -e "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
    echo "请检查数据库配置："
    echo "  数据库名: $DB_NAME"
    echo "  用户名: $DB_USERNAME"
    echo "  密码: $DB_PASSWORD"
    exit 1
fi

# =============================================================================
# 部署后端
# =============================================================================
echo "📦 部署后端..."

# 创建应用目录
mkdir -p "$APP_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$FILE_UPLOAD_DIR"

# 复制JAR文件
cp "$SCRIPT_DIR/target/$JAR_FILE" "$APP_DIR/"
if [ $? -eq 0 ]; then
    echo "✅ JAR文件复制成功"
else
    echo "❌ JAR文件复制失败"
    exit 1
fi

# 设置目录权限
chmod 755 "$APP_DIR"
chmod 755 "$LOG_DIR"
chmod 755 "$FILE_UPLOAD_DIR"

# =============================================================================
# 部署前端
# =============================================================================
echo "🌐 部署前端..."

# 检查前端目录
if [ ! -d "$FRONTEND_DIR" ]; then
    echo "⚠️ 前端目录不存在，创建目录: $FRONTEND_DIR"
    mkdir -p "$FRONTEND_DIR"
fi

# 备份旧的前端文件
if [ -f "$FRONTEND_DIR/index.html" ]; then
    echo "📦 备份旧的前端文件..."
    mv "$FRONTEND_DIR" "$FRONTEND_DIR.backup.$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$FRONTEND_DIR"
fi

# 复制前端文件
cp -r "$PROJECT_ROOT/gongxingxue-frontend/dist/"* "$FRONTEND_DIR/"
if [ $? -eq 0 ]; then
    echo "✅ 前端文件复制成功"
else
    echo "❌ 前端文件复制失败"
    exit 1
fi

# =============================================================================
# 启动应用
# =============================================================================
echo "🚀 启动应用..."

cd "$APP_DIR"
nohup java $JAVA_OPTS -jar "$JAR_FILE" > app.log 2>&1 &
APP_PID=$!

echo "✅ 应用启动完成！"
echo "  - PID: $APP_PID"
echo "  - 日志文件: $APP_DIR/app.log"

# =============================================================================
# 等待并验证启动
# =============================================================================
echo "⏳ 等待应用启动 (30秒)..."
sleep 30

# 检查应用是否启动成功
if ps -p $APP_PID > /dev/null 2>&1; then
    echo "✅ 应用启动成功!"

    # 测试API
    echo "🧪 测试API..."
    API_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://$SERVER_IP:$SERVER_PORT/api/resources/public")
    if [ "$API_RESPONSE" = "200" ]; then
        echo "✅ API测试成功!"
    else
        echo "⚠️ API测试失败 (HTTP $API_RESPONSE)，请检查日志"
    fi
else
    echo "❌ 应用启动失败，请检查日志"
    tail -20 "$APP_DIR/app.log"
    exit 1
fi

# =============================================================================
# 部署完成
# =============================================================================
echo ""
echo "� 部署完成！"
echo ""
echo "📋 访问地址："
echo "   前端地址: $FRONTEND_URL/"
echo "   后端地址: $FRONTEND_URL:$SERVER_PORT/"
echo "   API测试: $FRONTEND_URL:$SERVER_PORT/api/resources/public"
echo ""
echo "🔍 管理命令："
echo "   查看进程: ps aux | grep java"
echo "   查看日志: tail -f $APP_DIR/app.log"
echo "   停止应用: kill $APP_PID"
echo ""
echo "📁 重要目录："
echo "   应用目录: $APP_DIR"
echo "   日志目录: $LOG_DIR"
echo "   上传目录: $FILE_UPLOAD_DIR"
echo "   前端目录: $FRONTEND_DIR"
echo ""
