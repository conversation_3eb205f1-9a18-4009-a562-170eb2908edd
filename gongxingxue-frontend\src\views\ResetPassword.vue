<template>
  <div class="reset-password-page">
    <div class="reset-password-container">
      <h2 class="title">重置密码</h2>

      <div v-if="!tokenValid" class="error-message">
        <el-alert
          title="链接无效或已过期"
          type="error"
          description="密码重置链接无效或已过期，请重新申请密码重置。"
          show-icon
          :closable="false"
        />
        <div style="margin-top: 20px; text-align: center;">
          <el-button type="primary" @click="$router.push('/forgot-password')">
            重新申请
          </el-button>
        </div>
      </div>

      <el-form
        v-else
        ref="resetPasswordFormRef"
        :model="resetPasswordForm"
        :rules="resetPasswordRules"
        label-width="0"
        @submit.prevent="handleResetPassword"
      >
        <el-form-item prop="newPassword">
          <el-input
            v-model="resetPasswordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            prefix-icon="el-icon-lock"
            show-password
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            prefix-icon="el-icon-lock"
            show-password
            :disabled="loading"
            @keyup.enter="handleResetPassword"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="reset-button"
            @click="handleResetPassword"
          >
            重置密码
          </el-button>
        </el-form-item>
      </el-form>

      <div class="reset-password-footer">
        <router-link to="/login">返回登录</router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { verifyResetToken, resetPassword } from '../api/auth'

const router = useRouter()
const route = useRoute()

// Form data
const resetPasswordForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// Form validation rules
const validateConfirmPassword = (rule, value, callback) => {
  if (value !== resetPasswordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const resetPasswordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, max: 16, message: '密码长度必须在8-16个字符之间', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// Form ref
const resetPasswordFormRef = ref(null)

// Loading state
const loading = ref(false)
const tokenValid = ref(true)

// Get reset token from URL
const resetToken = route.query.token

// Verify reset token on component mount
onMounted(async () => {
  if (!resetToken) {
    tokenValid.value = false
    return
  }

  try {
    await verifyResetToken(resetToken)
    tokenValid.value = true
  } catch (error) {
    console.error('Token verification failed:', error)
    tokenValid.value = false
  }
})

// Handle reset password
const handleResetPassword = async () => {
  if (!resetPasswordFormRef.value) return

  await resetPasswordFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true

    try {
      await resetPassword(resetToken, resetPasswordForm.newPassword)
      
      ElMessage.success('密码重置成功，请使用新密码登录')
      
      // 跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 2000)
    } catch (error) {
      console.error('Reset password failed:', error)
      ElMessage.error(error.message || '密码重置失败，请稍后重试')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
.reset-password-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 60px - 60px); /* Subtract header and footer height */

  .reset-password-container {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .title {
      text-align: center;
      margin-bottom: 30px;
      color: #303133;
    }

    .reset-button {
      width: 100%;
    }

    .error-message {
      text-align: center;
    }

    .reset-password-footer {
      margin-top: 20px;
      text-align: center;
      font-size: 14px;
      color: #606266;

      a {
        color: #409eff;
      }
    }
  }
}
</style>
