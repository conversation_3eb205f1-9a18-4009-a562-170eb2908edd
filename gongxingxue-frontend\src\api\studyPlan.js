import request from '../utils/request'

/**
 * Create study plan
 * @param {Object} data - Study plan data
 * @returns {Promise} - API response
 */
export function createStudyPlan(data) {
  return request({
    url: '/study-plans',
    method: 'post',
    params: data
  })
}

/**
 * Get my study plans
 * @returns {Promise} - API response
 */
export function getMyStudyPlans() {
  return request({
    url: '/study-plans',
    method: 'get'
  })
}

/**
 * Get study plan by ID
 * @param {number} id - Study plan ID
 * @returns {Promise} - API response
 */
export function getStudyPlanById(id) {
  return request({
    url: `/study-plans/${id}`,
    method: 'get'
  })
}

/**
 * Update study plan
 * @param {number} id - Study plan ID
 * @param {Object} data - Study plan data
 * @returns {Promise} - API response
 */
export function updateStudyPlan(id, data) {
  return request({
    url: `/study-plans/${id}`,
    method: 'put',
    params: data
  })
}

/**
 * Delete study plan
 * @param {number} id - Study plan ID
 * @returns {Promise} - API response
 */
export function deleteStudyPlan(id) {
  return request({
    url: `/study-plans/${id}`,
    method: 'delete'
  })
}

/**
 * Add task to plan
 * @param {number} planId - Study plan ID
 * @param {Object} data - Task data
 * @returns {Promise} - API response
 */
export function addTask(planId, data) {
  return request({
    url: `/study-plans/${planId}/tasks`,
    method: 'post',
    params: data
  })
}

/**
 * Get tasks by plan ID
 * @param {number} planId - Study plan ID
 * @returns {Promise} - API response
 */
export function getTasksByPlanId(planId) {
  return request({
    url: `/study-plans/${planId}/tasks`,
    method: 'get'
  })
}

/**
 * Complete task
 * @param {number} taskId - Task ID
 * @returns {Promise} - API response
 */
export function completeTask(taskId) {
  return request({
    url: `/study-plans/tasks/${taskId}/complete`,
    method: 'put'
  })
}

/**
 * Delete task
 * @param {number} taskId - Task ID
 * @returns {Promise} - API response
 */
export function deleteTask(taskId) {
  return request({
    url: `/study-plans/tasks/${taskId}`,
    method: 'delete'
  })
}
