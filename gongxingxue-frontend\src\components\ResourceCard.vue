<template>
  <div class="resource-card" @click="$emit('click')">
    <div class="resource-info">
      <div class="resource-header">
        <span class="exam-type-tag" :class="`exam-type-${resource.examType}`">{{ examTypeText }}</span>
        <h3 class="resource-title">{{ resource.name }}</h3>
      </div>

      <div class="resource-description">
        {{ truncatedDescription }}
      </div>

      <div class="resource-meta">
        <span class="file-info">{{ getFileTypeDisplay(resource.originalFilename) }} {{ formatFileSize(resource.fileSize) }}</span>
        <span class="separator">•</span>
        <span class="uploader">{{ resource.username || '匿名用户' }}</span>
        <span class="separator">•</span>
        <span class="download-count">{{ resource.downloadCount || 0 }}下载</span>
        <span class="separator">•</span>
        <span class="comment-count">{{ resource.commentCount || 0 }}评论</span>
        <span class="separator">•</span>
        <span class="favorite-count">{{ resource.favoriteCount || 0 }}收藏</span>
        <span class="separator">•</span>
        <span class="upload-time">{{ formatDate(resource.createTime) }}</span>
      </div>
    </div>

    <div class="resource-actions">
      <favorite-button
        :resource-id="resource.id"
        :favorite-count="resource.favoriteCount || 0"
        size="small"
        :show-count="false"
        @update:count="updateFavoriteCount"
        @click.stop
      />
      <el-button type="primary" size="small" @click.stop="handleDownload">
        <el-icon><el-icon-download /></el-icon>
        下载
      </el-button>
      <slot name="actions"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { formatDate, isLoggedIn, getToken } from '../utils/auth'
import { downloadResource } from '../api/resource'
import FavoriteButton from './FavoriteButton.vue'

const router = useRouter()

// Props
const props = defineProps({
  resource: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['click', 'update:favoriteCount'])

// Computed
const examTypeText = computed(() => {
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return examTypes[props.resource.examType] || '未知'
})

const truncatedDescription = computed(() => {
  const description = props.resource.description || ''
  return description.length > 100 ? description.substring(0, 100) + '...' : description
})

// Get file type display
const getFileTypeDisplay = (filename) => {
  if (!filename) return '未知'

  const extension = filename.split('.').pop()?.toUpperCase()

  switch (extension) {
    case 'PDF':
      return 'PDF'
    case 'DOC':
    case 'DOCX':
      return 'DOC'
    case 'PPT':
    case 'PPTX':
      return 'PPT'
    case 'XLS':
    case 'XLSX':
      return 'XLS'
    case 'TXT':
      return 'TXT'
    case 'JPG':
    case 'JPEG':
    case 'PNG':
    case 'GIF':
    case 'BMP':
      return '图片'
    default:
      return '其他'
  }
}

// Format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// Methods
const handleDownload = async (event) => {
  event.stopPropagation()

  // Check if user is logged in
  if (!isLoggedIn()) {
    ElMessage.warning('请先登录后再下载资料')
    router.push('/login')
    return
  }

  try {
    console.log('开始下载资料，ID:', props.resource.id)
    console.log('资料信息:', {
      id: props.resource.id,
      name: props.resource.name,
      originalFilename: props.resource.originalFilename,
      filePath: props.resource.filePath
    })

    // 🎯 使用统一的下载API，自动处理认证
    const response = await downloadResource(props.resource.id)
    console.log('下载API响应:', response)

    // 验证响应
    if (!response || !response.data) {
      throw new Error('下载响应为空')
    }

    // response.data 是 blob 数据
    const blob = response.data

    // 验证blob是否有效
    if (!(blob instanceof Blob) || blob.size === 0) {
      throw new Error('下载的文件为空或格式错误')
    }

    console.log('Downloaded blob size:', blob.size, 'bytes')
    console.log('Downloaded blob type:', blob.type)

    // 获取文件名
    let filename = props.resource.originalFilename || props.resource.name || `resource_${props.resource.id}`

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL对象
    window.URL.revokeObjectURL(url)

    // 不显示额外提示，让浏览器自己处理下载体验
    // 用户可以在浏览器的下载栏中看到下载进度和结果
    console.log('Download initiated for:', filename, 'Size:', blob.size, 'bytes')
  } catch (error) {
    console.error('Download failed:', error)
    ElMessage.error('下载失败：' + (error.message || '请稍后重试'))
  }
}

// Update favorite count
const updateFavoriteCount = (newCount) => {
  emit('update:favoriteCount', newCount)
}

</script>

<style lang="scss" scoped>
.resource-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
  cursor: pointer;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  }

  .resource-info {
    flex: 1;
    min-width: 0; // 允许内容截断

    .resource-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .exam-type-tag {
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        color: #fff;
        margin-right: 10px;
        flex-shrink: 0;

        &.exam-type-0 {
          background-color: #409eff; // 考研 - 蓝色
        }

        &.exam-type-1 {
          background-color: #67c23a; // 考公 - 绿色
        }

        &.exam-type-2 {
          background-color: #e6a23c; // 法考 - 橙色
        }

        &.exam-type-3 {
          background-color: #f56c6c; // 教资 - 红色
        }

        &.exam-type-4 {
          background-color: #b794f6; // 其他 - 浅紫色
        }
      }

      .resource-title {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        flex: 1;
        min-width: 0; // 允许文字截断
      }
    }

    .resource-description {
      color: #606266;
      font-size: 14px;
      margin-bottom: 10px;
      line-height: 1.5;
    }

    .resource-meta {
      display: flex;
      align-items: center;
      color: #909399;
      font-size: 12px;
      line-height: 1;
      flex-wrap: wrap;

      .file-info {
        font-weight: 500;
        color: #606266;
      }

      .separator {
        margin: 0 6px;
        color: #dcdfe6;
      }

      .uploader {
        color: #909399;
      }

      .download-count,
      .comment-count,
      .favorite-count {
        color: #909399;
      }

      .upload-time {
        color: #c0c4cc;
      }
    }
  }

  .resource-actions {
    margin-left: 15px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .resource-card {
    flex-direction: column;
    align-items: flex-start;

    .resource-info {
      width: 100%;

      .resource-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;

        .separator {
          display: none;
        }
      }
    }

    .resource-actions {
      margin-left: 0;
      margin-top: 10px;
      align-self: flex-end;
    }
  }
}
</style>
