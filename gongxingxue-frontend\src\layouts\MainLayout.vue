<template>
  <div class="main-layout">
    <header class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <router-link to="/">
              <h1>考研/考公资料共享平台</h1>
            </router-link>
          </div>

          <div class="search-bar">
            <el-select v-model="searchExamType" placeholder="考试类型" class="exam-type-select">
              <el-option label="全部" :value="null" />
              <el-option label="考研" :value="0" />
              <el-option label="考公" :value="1" />
              <el-option label="法考" :value="2" />
              <el-option label="教资" :value="3" />
              <el-option label="其他" :value="4" />
            </el-select>
            <el-input v-model="searchKeyword" placeholder="搜索资料..." class="search-input" />
            <el-button type="primary" @click="handleSearch">搜索</el-button>
          </div>

          <div class="nav-links">
            <!-- 发布资料按钮始终显示，未登录时点击会跳转到登录页面 -->
            <el-button type="success" @click="handleUploadClick" class="nav-link">
              发布资料
            </el-button>

            <!-- 学习规划按钮只有登录后才显示 -->
            <router-link to="/study-plan" class="nav-link" v-if="isLoggedIn">
              <el-button>学习规划</el-button>
            </router-link>

            <!-- 消息中心按钮只有登录后才显示 -->
            <div class="message-icon" v-if="isLoggedIn" @click="goToMessages">
              <el-badge :value="unreadCount" :hidden="unreadCount === 0" :max="99">
                <el-icon :size="20" class="message-bell">
                  <Bell />
                </el-icon>
              </el-badge>
            </div>

            <template v-if="isLoggedIn">
              <el-dropdown trigger="click" @command="handleCommand">
                <div class="user-avatar">
                  <el-avatar :size="32" :src="userAvatar">{{ userInitial }}</el-avatar>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="myResources">我的资料</el-dropdown-item>
                    <el-dropdown-item command="myFavorites">我的收藏</el-dropdown-item>
                    <el-dropdown-item command="myComments">我的评论</el-dropdown-item>
                    <el-dropdown-item command="admin" v-if="isAdmin">后台管理</el-dropdown-item>
                    <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>

            <template v-else>
              <router-link to="/login" class="nav-link">
                <el-button>登录</el-button>
              </router-link>
              <router-link to="/register" class="nav-link">
                <el-button>注册</el-button>
              </router-link>
            </template>
          </div>
        </div>
      </div>
    </header>

    <main class="page-container">
      <div class="container">
        <router-view />
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>&copy; {{ currentYear }} 考研/考公资料共享平台 - 版权所有</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import { ElMessage } from 'element-plus'
import { Bell } from '@element-plus/icons-vue'
import { getUnreadCount } from '../api/message'

const router = useRouter()
const userStore = useUserStore()

// Search
const searchExamType = ref(null)
const searchKeyword = ref('')

// Messages
const unreadCount = ref(0)

// User info
const isLoggedIn = computed(() => userStore.isLoggedIn)
const isAdmin = computed(() => userStore.isAdmin)
const userAvatar = computed(() => userStore.user?.avatar || '')
const userInitial = computed(() => {
  const nickname = userStore.user?.nickname || ''
  return nickname ? nickname.charAt(0).toUpperCase() : 'U'
})

// Current year for footer
const currentYear = new Date().getFullYear()

// Handle search
const handleSearch = () => {
  router.push({
    name: 'SearchResults',
    query: {
      examType: searchExamType.value,
      keyword: searchKeyword.value
    }
  })
}

// Handle upload button click
const handleUploadClick = () => {
  if (isLoggedIn.value) {
    // 已登录，直接跳转到上传页面
    router.push('/upload')
  } else {
    // 未登录，提示并跳转到登录页面
    ElMessage.warning('请先登录后再发布资料')
    router.push('/login?redirect=/upload')
  }
}

// Handle messages click
const goToMessages = () => {
  router.push('/messages')
}

// Fetch unread message count
const fetchUnreadCount = async () => {
  if (!isLoggedIn.value) {
    unreadCount.value = 0
    return
  }

  try {
    const response = await getUnreadCount()
    if (response.success) {
      unreadCount.value = response.data || 0
    }
  } catch (error) {
    // Silently fail for unread count
    unreadCount.value = 0
  }
}

// Handle dropdown menu commands
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push({ name: 'Profile' })
      break
    case 'myResources':
      router.push({ name: 'MyResources' })
      break
    case 'myFavorites':
      router.push({ name: 'MyFavorites' })
      break
    case 'myComments':
      router.push({ name: 'MyComments' })
      break
    case 'admin':
      router.push({ name: 'AdminDashboard' })
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已成功退出登录')
      router.push({ name: 'Home' })
      break
  }
}

// Listen for message read events
const handleMessageRead = () => {
  fetchUnreadCount()
}

// Watch login status and fetch unread count
watch(isLoggedIn, (newValue) => {
  if (newValue) {
    fetchUnreadCount()
  } else {
    unreadCount.value = 0
  }
}, { immediate: true })

// Lifecycle
onMounted(() => {
  // Setup event listener for message read events
  window.addEventListener('messageRead', handleMessageRead)

  // 只有在已登录时才获取未读数量
  if (isLoggedIn.value) {
    fetchUnreadCount()
  }

  // 设置定时器，但只在登录状态下执行
  const intervalId = setInterval(() => {
    if (isLoggedIn.value) {
      fetchUnreadCount()
    }
  }, 30000)

  // 清理定时器
  onUnmounted(() => {
    clearInterval(intervalId)
  })
})

onUnmounted(() => {
  window.removeEventListener('messageRead', handleMessageRead)
})
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;

    .logo {
      h1 {
        font-size: 1.5rem;
        color: #409eff;
        margin: 0;
      }
    }

    .search-bar {
      display: flex;
      align-items: center;
      flex: 1;
      max-width: 600px;
      margin: 0 20px;

      .exam-type-select {
        width: 120px;
        margin-right: 10px;
      }

      .search-input {
        flex: 1;
        margin-right: 10px;
      }
    }

    .nav-links {
      display: flex;
      align-items: center;

      .nav-link {
        margin-left: 10px;
      }

      .el-button {
        margin-left: 10px;
      }

      .message-icon {
        margin-left: 15px;
        cursor: pointer;
        display: flex;
        align-items: center;

        .message-bell {
          color: #606266;
          transition: color 0.3s;

          &:hover {
            color: #409eff;
          }
        }
      }

      .user-avatar {
        margin-left: 15px;
        cursor: pointer;
      }
    }
  }
}

.page-container {
  flex: 1;
  padding: 20px 0;
}

.footer {
  background-color: #f5f7fa;
  padding: 20px 0;
  text-align: center;
  color: #606266;
}

@media (max-width: 768px) {
  .header {
    .header-content {
      flex-direction: column;
      height: auto;
      padding: 10px 0;

      .logo {
        margin-bottom: 10px;
      }

      .search-bar {
        width: 100%;
        max-width: none;
        margin: 10px 0;
      }

      .nav-links {
        width: 100%;
        justify-content: center;
        margin-top: 10px;
      }
    }
  }
}
</style>
