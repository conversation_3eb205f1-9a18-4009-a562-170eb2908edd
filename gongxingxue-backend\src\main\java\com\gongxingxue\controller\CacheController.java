package com.gongxingxue.controller;

import com.gongxingxue.common.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存管理控制器
 *
 * 提供缓存管理相关的API接口
 * 注意：这些接口应该只对管理员开放
 */
@RestController
@RequestMapping("/admin/cache")
@RequiredArgsConstructor
@Api(tags = "缓存管理API")
public class CacheController {

    private final CacheManager cacheManager;

    /**
     * 清除所有缓存
     */
    @DeleteMapping("/clear")
    @ApiOperation("清除所有缓存")
    public Result<String> clearAllCache() {
        try {
            // 清除所有缓存
            cacheManager.getCacheNames().forEach(cacheName -> {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    cache.clear();
                }
            });

            return Result.success("所有缓存已清除");
        } catch (Exception e) {
            return Result.error("清除缓存失败：" + e.getMessage());
        }
    }

    /**
     * 清除指定缓存
     */
    @DeleteMapping("/clear/{cacheName}")
    @ApiOperation("清除指定缓存")
    public Result<String> clearCache(@PathVariable String cacheName) {
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                return Result.success("缓存 " + cacheName + " 已清除");
            } else {
                return Result.error("缓存 " + cacheName + " 不存在");
            }
        } catch (Exception e) {
            return Result.error("清除缓存失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/stats")
    @ApiOperation("获取缓存统计信息")
    public Result<Map<String, Object>> getCacheStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 获取所有缓存名称
            Collection<String> cacheNames = cacheManager.getCacheNames();
            stats.put("cacheNames", cacheNames);
            stats.put("cacheCount", cacheNames.size());

            // 获取每个缓存的信息
            Map<String, Object> cacheDetails = new HashMap<>();
            for (String cacheName : cacheNames) {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    Map<String, Object> cacheInfo = new HashMap<>();
                    cacheInfo.put("name", cacheName);
                    cacheInfo.put("type", cache.getClass().getSimpleName());

                    // 如果是ConcurrentMapCache，可以获取更多信息
                    if (cache.getNativeCache() instanceof java.util.concurrent.ConcurrentHashMap) {
                        java.util.concurrent.ConcurrentHashMap<?, ?> nativeCache = (java.util.concurrent.ConcurrentHashMap<?, ?>) cache.getNativeCache();
                        cacheInfo.put("size", nativeCache.size());
                        cacheInfo.put("isEmpty", nativeCache.isEmpty());
                    }

                    cacheDetails.put(cacheName, cacheInfo);
                }
            }
            stats.put("cacheDetails", cacheDetails);

            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取缓存统计失败：" + e.getMessage());
        }
    }

    /**
     * 预热缓存
     */
    @PostMapping("/warmup")
    @ApiOperation("预热缓存")
    public Result<String> warmupCache() {
        try {
            // 这里可以添加缓存预热逻辑
            // 比如预加载热门资源、用户信息等

            return Result.success("缓存预热完成");
        } catch (Exception e) {
            return Result.error("缓存预热失败：" + e.getMessage());
        }
    }
}
