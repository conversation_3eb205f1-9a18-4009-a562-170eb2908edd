import request from '../utils/request'

/**
 * User login
 * @param {string} username - Username
 * @param {string} password - Password
 * @param {string} captcha - Captcha code
 * @returns {Promise} - API response
 */
export function login(username, password, captcha) {
  console.log('Login request:', { username, password, captcha })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // Encode parameters in URL
    const url = `/api/auth/login?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&captcha=${encodeURIComponent(captcha)}`
    xhr.open('POST', url, true)

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Login success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('<PERSON><PERSON> failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)
        reject(new Error(`Login failed: ${xhr.status} ${xhr.statusText}`))
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during login')
      reject(new Error('Network error during login'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * User registration
 * @param {string} username - Username
 * @param {string} password - Password
 * @param {string} nickname - Nickname (optional)
 * @param {string} captcha - Captcha code
 * @returns {Promise} - API response
 */
export function register(username, password, nickname, captcha) {
  console.log('Register request:', { username, password, nickname, captcha })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // Encode parameters in URL
    let url = `/api/auth/register?username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&captcha=${encodeURIComponent(captcha)}`
    if (nickname) {
      url += `&nickname=${encodeURIComponent(nickname)}`
    }

    xhr.open('POST', url, true)

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Register success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Register failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)
        reject(new Error(`Register failed: ${xhr.status} ${xhr.statusText}`))
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during registration')
      reject(new Error('Network error during registration'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * Get current user info
 * @returns {Promise} - API response
 */
export function getCurrentUser() {
  return request({
    url: '/users/current',
    method: 'get'
  })
}

/**
 * Update user profile
 * @param {Object} data - Profile data
 * @returns {Promise} - API response
 */
export function updateProfile(data) {
  return request({
    url: '/users/profile',
    method: 'put',
    params: data
  })
}

/**
 * Change password
 * @param {string} oldPassword - Old password
 * @param {string} newPassword - New password
 * @returns {Promise} - API response
 */
export function changePassword(oldPassword, newPassword) {
  return request({
    url: '/users/password',
    method: 'put',
    params: {
      oldPassword,
      newPassword
    }
  })
}

/**
 * Send password reset email
 * @param {string} email - Email address
 * @returns {Promise} - API response
 */
export function sendPasswordResetEmail(email) {
  console.log('Send password reset email request:', { email })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    // Encode parameters in URL
    const url = `/api/auth/forgot-password?email=${encodeURIComponent(email)}`
    xhr.open('POST', url, true)

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Send password reset email response:', response)

          // 检查后端返回的success字段
          if (response.success) {
            resolve(response)
          } else {
            // 后端返回的业务错误
            reject(new Error(response.message || '发送重置邮件失败'))
          }
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Send password reset email failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during send password reset email')
      reject(new Error('Network error during send password reset email'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * Verify reset token
 * @param {string} token - Reset token
 * @returns {Promise} - API response
 */
export function verifyResetToken(token) {
  console.log('Verify reset token request:', { token })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    const url = `/api/auth/verify-reset-token?token=${encodeURIComponent(token)}`
    xhr.open('GET', url, true)

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Verify reset token success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Verify reset token failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during verify reset token')
      reject(new Error('Network error during verify reset token'))
    }

    // Send the request
    xhr.send()
  })
}

/**
 * Reset password
 * @param {string} token - Reset token
 * @param {string} newPassword - New password
 * @returns {Promise} - API response
 */
export function resetPassword(token, newPassword) {
  console.log('Reset password request:', { token, newPassword: '***' })

  // Use XMLHttpRequest directly for better control and debugging
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()

    const url = `/api/auth/reset-password?token=${encodeURIComponent(token)}&newPassword=${encodeURIComponent(newPassword)}`
    xhr.open('POST', url, true)

    // Handle response
    xhr.onload = function() {
      if (xhr.status >= 200 && xhr.status < 300) {
        try {
          const response = JSON.parse(xhr.responseText)
          console.log('Reset password success response:', response)
          resolve(response)
        } catch (e) {
          console.error('Error parsing response:', e)
          reject(new Error('Invalid response format'))
        }
      } else {
        console.error('Reset password failed with status:', xhr.status)
        console.error('Response text:', xhr.responseText)

        try {
          const errorResponse = JSON.parse(xhr.responseText)
          reject(new Error(errorResponse.message || `Request failed: ${xhr.status} ${xhr.statusText}`))
        } catch (e) {
          reject(new Error(`Request failed: ${xhr.status} ${xhr.statusText}`))
        }
      }
    }

    // Handle errors
    xhr.onerror = function() {
      console.error('Network error during reset password')
      reject(new Error('Network error during reset password'))
    }

    // Send the request
    xhr.send()
  })
}
