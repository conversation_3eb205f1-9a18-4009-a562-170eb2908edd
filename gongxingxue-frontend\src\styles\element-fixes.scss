/* Element Plus 组件样式修复 */

/* 修复 el-radio-button 边框显示问题 */
.el-radio-group {
  .el-radio-button {
    // 确保按钮之间的边框连接
    &:not(:first-child) {
      margin-left: -1px;
    }
    
    .el-radio-button__inner {
      border: 1px solid #dcdfe6;
      border-radius: 0;
      position: relative;
      
      // 悬停效果
      &:hover {
        border-color: #409eff;
        z-index: 1;
      }
      
      // 聚焦效果
      &:focus {
        border-color: #409eff;
        z-index: 1;
      }
    }
    
    // 第一个按钮的左边圆角
    &:first-child .el-radio-button__inner {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    
    // 最后一个按钮的右边圆角
    &:last-child .el-radio-button__inner {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    
    // 选中状态
    &.is-active .el-radio-button__inner {
      border-color: #409eff;
      background-color: #409eff;
      color: #fff;
      z-index: 2;
    }
    
    // 禁用状态
    &.is-disabled .el-radio-button__inner {
      border-color: #e4e7ed;
      background-color: #f5f7fa;
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .el-radio-button {
      // 移动端不使用负边距
      &:not(:first-child) {
        margin-left: 0;
      }
      
      .el-radio-button__inner {
        // 移动端每个按钮都有完整圆角
        border-radius: 4px !important;
        min-width: 60px;
        padding: 8px 12px;
        font-size: 14px;
        
        // 确保边框完整显示
        border: 1px solid #dcdfe6 !important;
      }
      
      &.is-active .el-radio-button__inner {
        border-color: #409eff !important;
        background-color: #409eff !important;
        color: #fff !important;
      }
    }
  }
}

/* 修复按钮组在卡片中的显示 */
.el-card .el-radio-group {
  .el-radio-button .el-radio-button__inner {
    // 确保在卡片背景下边框清晰可见
    border-color: #dcdfe6;
    background-color: #fff;
  }
}

/* 修复按钮文字对齐 */
.el-radio-button__inner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  white-space: nowrap;
}

/* 确保按钮组在不同容器中的一致性 */
.filter-item .el-radio-group,
.search-filters .el-radio-group,
.advanced-filters .el-radio-group {
  .el-radio-button .el-radio-button__inner {
    border: 1px solid #dcdfe6;
    
    &:hover {
      border-color: #409eff;
      z-index: 1;
      position: relative;
    }
  }
  
  .el-radio-button.is-active .el-radio-button__inner {
    border-color: #409eff;
    background-color: #409eff;
    color: #fff;
    z-index: 2;
    position: relative;
  }
}
