<template>
  <div class="comment-item" :data-comment-id="comment.id">
    <div class="comment-header">
      <div class="user-info">
        <el-avatar :size="40" :src="comment.user?.avatar">
          {{ userInitial }}
        </el-avatar>
        <div class="user-details">
          <div class="username">{{ comment.user?.nickname || comment.user?.username || '匿名用户' }}</div>
          <div class="comment-time">{{ formatDate(comment.createTime) }}</div>
        </div>
      </div>

      <div class="comment-actions" v-if="showActions">
        <el-button type="text" size="small" @click="handleReply">回复</el-button>
        <el-button type="text" size="small" @click="handleDelete" v-if="canDelete">删除</el-button>
      </div>
    </div>

    <div class="comment-content">
      <p>{{ comment.content }}</p>
    </div>

    <div class="comment-replies" v-if="replies.length > 0">
      <div class="reply-item" v-for="reply in replies" :key="reply.id" :data-comment-id="reply.id">
        <div class="reply-header">
          <div class="user-info">
            <el-avatar :size="30" :src="reply.user?.avatar">
              {{ getInitial(reply.user?.nickname || reply.user?.username) }}
            </el-avatar>
            <div class="user-details">
              <div class="username">{{ reply.user?.nickname || reply.user?.username || '匿名用户' }}</div>
              <div class="comment-time">{{ formatDate(reply.createTime) }}</div>
            </div>
          </div>

          <div class="comment-actions" v-if="showActions">
            <el-button type="text" size="small" @click="handleReplyToReply(reply)">回复</el-button>
            <el-button type="text" size="small" @click="handleDeleteReply(reply)" v-if="canDeleteReply(reply)">删除</el-button>
          </div>
        </div>

        <div class="reply-content">
          <p>{{ reply.content }}</p>
        </div>
      </div>
    </div>

    <div class="load-replies" v-if="hasMoreReplies">
      <el-button type="text" @click="loadReplies">
        {{ loadingReplies ? '加载中...' : '查看更多回复' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '../store/user'
import { getRepliesByParentId } from '../api/comment'
import { formatDate } from '../utils/auth'

const props = defineProps({
  comment: {
    type: Object,
    required: true
  },
  showActions: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['reply', 'delete', 'refresh-replies'])

const userStore = useUserStore()
const replies = ref([])
const loadingReplies = ref(false)
const repliesLoaded = ref(false)

// Computed
const userInitial = computed(() => {
  const name = props.comment.user?.nickname || props.comment.user?.username || ''
  return name ? name.charAt(0).toUpperCase() : 'U'
})

const canDelete = computed(() => {
  return userStore.isLoggedIn &&
    (userStore.user.id === props.comment.userId || userStore.isAdmin)
})

const hasMoreReplies = computed(() => {
  return !repliesLoaded.value && props.comment.replyCount > 0
})

// Methods
const getInitial = (name) => {
  return name ? name.charAt(0).toUpperCase() : 'U'
}

const canDeleteReply = (reply) => {
  return userStore.isLoggedIn &&
    (userStore.user.id === reply.userId || userStore.isAdmin)
}

const handleReply = () => {
  emit('reply', props.comment)
}

const handleReplyToReply = (reply) => {
  emit('reply', reply)
}

const handleDelete = () => {
  emit('delete', props.comment.id)
}

const handleDeleteReply = (reply) => {
  emit('delete', reply.id)
}

const loadReplies = async () => {
  if (loadingReplies.value) return

  loadingReplies.value = true
  try {
    const response = await getRepliesByParentId(props.comment.id)
    replies.value = response.data
    repliesLoaded.value = true
  } catch (error) {
    console.error('Failed to load replies:', error)
  } finally {
    loadingReplies.value = false
  }
}

// Method to refresh replies (can be called from parent)
const refreshReplies = async () => {
  repliesLoaded.value = false
  await loadReplies()
}

// Expose the refresh method to parent components
defineExpose({
  refreshReplies
})

// Load replies if comment has replies
onMounted(() => {
  // Always try to load replies, the backend will return empty array if no replies
  loadReplies()
})


</script>

<style lang="scss" scoped>
.comment-item {
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  .comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .user-info {
      display: flex;
      align-items: center;

      .user-details {
        margin-left: 10px;

        .username {
          font-size: 14px;
          font-weight: 500;
          color: #909399;
        }

        .comment-time {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }

  .comment-content {
    margin-left: 50px;
    margin-bottom: 10px;

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: #606266;
    }
  }

  .comment-replies {
    margin-left: 50px;
    background-color: #f5f7fa;
    border-radius: 4px;
    padding: 10px;

    .reply-item {
      padding: 10px 0;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .reply-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        .user-info {
          display: flex;
          align-items: center;

          .user-details {
            margin-left: 10px;

            .username {
              font-size: 13px;
              font-weight: 500;
              color: #909399;
            }

            .comment-time {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }

      .reply-content {
        margin-left: 40px;

        p {
          margin: 0;
          font-size: 13px;
          line-height: 1.6;
          color: #606266;
        }
      }
    }
  }

  .load-replies {
    margin-left: 50px;
    margin-top: 10px;
    text-align: center;
  }
}
</style>
