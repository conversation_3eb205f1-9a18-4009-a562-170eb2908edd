<template>
  <div class="admin-users">
    <h2 class="page-title">用户管理</h2>

    <div class="users-table">
      <div class="table-filters">
        <el-form :inline="true" :model="filters">
          <el-form-item label="用户名">
            <el-input v-model="filters.username" placeholder="用户名" />
          </el-form-item>

          <el-form-item label="注册时间">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <el-table :data="users" style="width: 100%" v-loading="loading">
        <el-table-column prop="username" label="用户名" min-width="120" />

        <el-table-column prop="nickname" label="昵称" min-width="120">
          <template #default="{ row }">
            {{ row.nickname || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="row.role === 1 ? 'danger' : ''">
              {{ row.role === 1 ? '管理员' : '普通用户' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="email" label="邮箱" min-width="150">
          <template #default="{ row }">
            {{ row.email || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="phone" label="手机号" width="120">
          <template #default="{ row }">
            {{ row.phone || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="registerIp" label="注册IP" width="120">
          <template #default="{ row }">
            {{ formatIpAddress(row.registerIp) }}
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="lastLoginTime" label="最后登录时间" width="180">
          <template #default="{ row }">
            {{ row.lastLoginTime ? formatDate(row.lastLoginTime) : '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 0 ? 'success' : 'danger'">
              {{ row.status === 0 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 0"
              type="danger"
              size="small"
              @click="handleDisableUser(row)"
              :disabled="row.role === 1"
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="success"
              size="small"
              @click="handleEnableUser(row)"
            >
              启用
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="total > 0"
        background
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
        class="pagination"
      />

      <el-empty v-if="users.length === 0" description="暂无用户数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, updateUserStatus } from '../../api/admin'
import { formatDate } from '../../utils/auth'
import { formatIpAddress } from '../../utils/ip'

// Data
const users = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// Filters
const filters = reactive({
  username: '',
  dateRange: []
})

// Fetch users
const fetchUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      username: filters.username || undefined
    }

    // Add date range if selected
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.startTime = filters.dateRange[0]
      params.endTime = filters.dateRange[1]
    }

    const response = await getUserList(params)
    users.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchUsers()
}

// Handle search
const handleSearch = () => {
  currentPage.value = 1
  fetchUsers()
}

// Reset filters
const resetFilters = () => {
  filters.username = ''
  filters.dateRange = []
  currentPage.value = 1
  fetchUsers()
}

// Handle disable user
const handleDisableUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用用户 "${user.username}" 吗？禁用后该用户将无法登录。`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await updateUserStatus(user.id, 1) // 1 = disabled
    ElMessage.success('用户已禁用')

    // Refresh users
    fetchUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to disable user:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Handle enable user
const handleEnableUser = async (user) => {
  try {
    await updateUserStatus(user.id, 0) // 0 = normal
    ElMessage.success('用户已启用')

    // Refresh users
    fetchUsers()
  } catch (error) {
    console.error('Failed to enable user:', error)
    ElMessage.error('操作失败')
  }
}



// Fetch data on component mount
onMounted(() => {
  fetchUsers()
})
</script>

<style lang="scss" scoped>
.admin-users {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .users-table {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .table-filters {
      margin-bottom: 20px;
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
