<template>
  <div class="home-page">
    <div class="filter-section">
      <!-- 考试类型筛选 -->
      <div class="exam-types">
        <span class="filter-label">考试类型：</span>
        <el-radio-group v-model="selectedExamType" @change="handleExamTypeChange">
          <el-radio-button :label="null">全部</el-radio-button>
          <el-radio-button :label="0">考研</el-radio-button>
          <el-radio-button :label="1">考公</el-radio-button>
          <el-radio-button :label="2">法考</el-radio-button>
          <el-radio-button :label="3">教资</el-radio-button>
          <el-radio-button :label="4">其他</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 排序选项 -->
      <div class="sort-section">
        <span class="filter-label">排序：</span>
        <el-select v-model="sortType" @change="handleSortChange" placeholder="选择排序方式" style="width: 140px">
          <el-option label="最新上传" value="createTime" />
          <el-option label="下载最多" value="downloadCount" />
          <el-option label="收藏最多" value="favoriteCount" />
          <el-option label="评论最多" value="commentCount" />
          <el-option label="综合热度" value="relevance" />
        </el-select>
      </div>
    </div>

    <div class="resources-list" v-loading="loading">
      <template v-if="resources.length > 0">
        <resource-card
          v-for="resource in resources"
          :key="resource.id"
          :resource="resource"
          @click="viewResource(resource.id)"
        />

        <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </template>

      <el-empty v-else description="暂无资料" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { getPublicResources } from '../api/resource'
import ResourceCard from '../components/ResourceCard.vue'

const router = useRouter()

// Data
const resources = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedExamType = ref(null)
const sortType = ref('createTime') // createTime: latest, relevance: popular

// Fetch resources
const fetchResources = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      sortType: sortType.value
    }

    if (selectedExamType.value !== null) {
      params.examType = selectedExamType.value
    }

    const response = await getPublicResources(params)
    resources.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to fetch resources:', error)
  } finally {
    loading.value = false
  }
}

// Handle exam type change
const handleExamTypeChange = () => {
  currentPage.value = 1
  fetchResources()
}

// Handle sort change
const handleSortChange = () => {
  currentPage.value = 1
  fetchResources()
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchResources()
}

// View resource details
const viewResource = (id) => {
  router.push({ name: 'ResourceDetail', params: { id } })
}

// Watch for route query changes
watch(
  () => router.currentRoute.value.query,
  (query) => {
    if (query.examType !== undefined) {
      selectedExamType.value = query.examType !== null ? Number(query.examType) : null
    }
    fetchResources()
  },
  { immediate: true }
)

// Fetch resources on component mount
onMounted(() => {
  fetchResources()
})
</script>

<style lang="scss" scoped>
.home-page {
  .filter-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;

    .exam-types {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      flex-wrap: wrap;
      gap: 8px;

      .filter-label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        white-space: nowrap;
      }
    }

    .sort-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .filter-label {
        font-weight: 500;
        color: #606266;
        white-space: nowrap;
      }
    }
  }

  .resources-list {
    min-height: 300px;

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .home-page {
    .filter-section {
      padding: 12px;

      .exam-types {
        margin-bottom: 16px;

        .filter-label {
          width: 100%;
          margin-bottom: 8px;
        }
      }

      .sort-section {
        .filter-label {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
