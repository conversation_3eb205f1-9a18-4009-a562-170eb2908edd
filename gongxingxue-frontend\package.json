{"name": "gongxingxue-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "element-plus": "^2.9.10", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.3.0", "sass": "^1.89.0", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.3.5"}}