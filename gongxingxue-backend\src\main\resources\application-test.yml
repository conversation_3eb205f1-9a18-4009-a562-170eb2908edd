# 测试环境配置 - 参照生产环境设置
spring:
  # 数据库配置 - 测试环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    hikari:
      # 测试环境连接池配置（中等规模）
      minimum-idle: ${DB_MIN_IDLE:10}
      maximum-pool-size: ${DB_MAX_POOL_SIZE:30}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
      pool-name: GongxingxueHikariPool-Test

  # 邮件配置 - 测试环境（使用真实邮箱配置进行测试）
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:465}
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:kwrhhpdvjbvldgch}
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            required: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
            port: 465

# 文件存储配置 - 测试环境
file:
  upload-dir: ${FILE_UPLOAD_DIR:../uploads/test}

# JWT配置 - 测试环境
jwt:
  secret: ${JWT_SECRET:gongxingxue_test_secret_key_for_jwt_token_generation_32chars}
  expiration: ${JWT_EXPIRATION:7200000}  # 2小时（测试环境适中）

# Swagger配置 - 测试环境启用
swagger:
  enabled: ${SWAGGER_ENABLED:true}
  title: Gongxingxue API - 测试环境
  description: API Documentation for Exam Preparation Resource Sharing Platform (Testing)
  version: 1.0.0-TEST
  contact:
    name: Gongxingxue Test Team
    email: ${CONTACT_EMAIL:<EMAIL>}

# 日志配置 - 测试环境
logging:
  level:
    com.gongxingxue: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    '[org.springframework.mail]': DEBUG  # 邮件调试日志
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:./logs/test-application.log}
    max-size: 50MB
    max-history: 10
  charset:
    console: UTF-8
    file: UTF-8

# 服务器配置 - 测试环境
server:
  port: ${SERVER_PORT:8081}  # 与生产环境保持一致，通过IP区分环境
  servlet:
    context-path: /api
  # 测试环境错误信息配置（显示详细错误便于调试）
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: on-param
    include-exception: true

# CORS配置 - 测试环境
cors:
  allowed:
    origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000}

# 应用配置 - 测试环境
app:
  frontend:
    url: ${FRONTEND_URL:http://localhost:3000}
