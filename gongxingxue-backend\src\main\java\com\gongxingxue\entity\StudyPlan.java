package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Study plan entity
 */
@Data
@Accessors(chain = true)
@TableName("study_plan")
public class StudyPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * User ID
     */
    private Long userId;

    /**
     * Plan name
     */
    private String name;

    /**
     * Plan description
     */
    private String description;

    /**
     * Plan type: 0=daily, 1=weekly, 2=monthly
     */
    private Integer planType;

    /**
     * Start date
     */
    private LocalDateTime startDate;

    /**
     * End date
     */
    private LocalDateTime endDate;

    /**
     * Creation time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * Logical delete flag: 0=not deleted, 1=deleted
     */
    @TableLogic
    private Integer deleted;
}
