package com.gongxingxue.controller;

import com.gongxingxue.common.Result;
import com.gongxingxue.entity.StudyPlan;
import com.gongxingxue.entity.StudyTask;
import com.gongxingxue.service.StudyPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Study plan controller
 */
@RestController
@RequestMapping("/study-plans")
@RequiredArgsConstructor
@Api(tags = "Study Plan API")
public class StudyPlanController {

    private final StudyPlanService studyPlanService;

    /**
     * Create study plan
     */
    @PostMapping
    @ApiOperation("Create study plan")
    public Result<StudyPlan> createStudyPlan(
            @RequestParam @NotBlank String name,
            @RequestParam(required = false) String description,
            @RequestParam @NotNull Integer planType,
            @RequestParam @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        StudyPlan plan = studyPlanService.createStudyPlan(name, description, planType, startDate, endDate, userId);
        
        return Result.success(plan);
    }

    /**
     * Get my study plans
     */
    @GetMapping
    @ApiOperation("Get my study plans")
    public Result<List<StudyPlan>> getMyStudyPlans(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        List<StudyPlan> plans = studyPlanService.getStudyPlansByUserId(userId);
        
        return Result.success(plans);
    }

    /**
     * Get study plan by ID
     */
    @GetMapping("/{id}")
    @ApiOperation("Get study plan by ID")
    public Result<StudyPlan> getStudyPlanById(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        StudyPlan plan = studyPlanService.getStudyPlanById(id, userId);
        
        if (plan != null) {
            return Result.success(plan);
        } else {
            return Result.error("Study plan not found or access denied");
        }
    }

    /**
     * Update study plan
     */
    @PutMapping("/{id}")
    @ApiOperation("Update study plan")
    public Result<Void> updateStudyPlan(
            @PathVariable Long id,
            @RequestParam @NotBlank String name,
            @RequestParam(required = false) String description,
            @RequestParam @NotNull Integer planType,
            @RequestParam @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            HttpServletRequest request) {
        
        Long userId = (Long) request.getAttribute("userId");
        boolean success = studyPlanService.updateStudyPlan(id, name, description, planType, startDate, endDate, userId);
        
        if (success) {
            return Result.success("Study plan updated successfully", null);
        } else {
            return Result.error("Failed to update study plan or study plan not found");
        }
    }

    /**
     * Delete study plan
     */
    @DeleteMapping("/{id}")
    @ApiOperation("Delete study plan")
    public Result<Void> deleteStudyPlan(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = studyPlanService.deleteStudyPlan(id, userId);
        
        if (success) {
            return Result.success("Study plan deleted successfully", null);
        } else {
            return Result.error("Failed to delete study plan or study plan not found");
        }
    }

    /**
     * Add task to plan
     */
    @PostMapping("/{planId}/tasks")
    @ApiOperation("Add task to plan")
    public Result<StudyTask> addTask(
            @PathVariable Long planId,
            @RequestParam @NotBlank String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long resourceId,
            @RequestParam @NotNull @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime scheduledDate,
            HttpServletRequest request) {
        
        try {
            Long userId = (Long) request.getAttribute("userId");
            StudyTask task = studyPlanService.addTask(planId, name, description, resourceId, scheduledDate, userId);
            
            return Result.success(task);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Get tasks by plan ID
     */
    @GetMapping("/{planId}/tasks")
    @ApiOperation("Get tasks by plan ID")
    public Result<List<StudyTask>> getTasksByPlanId(@PathVariable Long planId, HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            List<StudyTask> tasks = studyPlanService.getTasksByPlanId(planId, userId);
            
            return Result.success(tasks);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Complete task
     */
    @PutMapping("/tasks/{taskId}/complete")
    @ApiOperation("Complete task")
    public Result<Void> completeTask(@PathVariable Long taskId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = studyPlanService.completeTask(taskId, userId);
        
        if (success) {
            return Result.success("Task completed successfully", null);
        } else {
            return Result.error("Failed to complete task or task not found");
        }
    }

    /**
     * Delete task
     */
    @DeleteMapping("/tasks/{taskId}")
    @ApiOperation("Delete task")
    public Result<Void> deleteTask(@PathVariable Long taskId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = studyPlanService.deleteTask(taskId, userId);
        
        if (success) {
            return Result.success("Task deleted successfully", null);
        } else {
            return Result.error("Failed to delete task or task not found");
        }
    }
}
