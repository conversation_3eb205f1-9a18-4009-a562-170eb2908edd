import request from '../utils/request'

/**
 * Add comment
 * @param {Object} data - Comment data
 * @returns {Promise} - API response
 */
export function addComment(data) {
  return request({
    url: '/comments',
    method: 'post',
    data: data
  })
}

/**
 * Get comments by resource ID
 * @param {number} resourceId - Resource ID
 * @param {number} page - Page number
 * @param {number} size - Page size
 * @returns {Promise} - API response
 */
export function getCommentsByResourceId(resourceId, page = 1, size = 10) {
  return request({
    url: `/comments/resource/${resourceId}`,
    method: 'get',
    params: {
      page,
      size
    }
  })
}

/**
 * Get replies by parent comment ID
 * @param {number} parentId - Parent comment ID
 * @returns {Promise} - API response
 */
export function getRepliesByParentId(parentId) {
  return request({
    url: `/comments/replies/${parentId}`,
    method: 'get'
  })
}

/**
 * Get my comments
 * @param {number} page - Page number
 * @param {number} size - Page size
 * @returns {Promise} - API response
 */
export function getMyComments(page = 1, size = 10) {
  return request({
    url: '/comments/my',
    method: 'get',
    params: {
      page,
      size
    }
  })
}

/**
 * Delete comment
 * @param {number} id - Comment ID
 * @returns {Promise} - API response
 */
export function deleteComment(id) {
  return request({
    url: `/comments/${id}`,
    method: 'delete'
  })
}
