import{r as d,c as E,p as Y,w as Z,o as c,b as p,d as a,q as z,i as B,m as o,t as T,e,f as t,g as v,s as h,h as V,F as A,v as ll,k as el,l as r,K as tl,j as L,L as ol,M as sl}from"./index-BC3U7MsG.js";import{g as al}from"./resource-CWJ1Qb9i.js";import{R as nl}from"./ResourceCard-Cwp_Kmif.js";import{_ as ul}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FavoriteButton-fz6enDeT.js";const dl={class:"search-results"},rl={class:"search-info"},il={key:0},vl={key:1},_l={class:"advanced-filters"},fl={class:"filter-header"},pl={class:"basic-filters"},ml={class:"filter-row"},gl={class:"filter-item"},bl={class:"filter-row"},yl={class:"filter-item"},wl={class:"advanced-filters-content"},Cl={class:"filter-row"},kl={class:"filter-item"},Tl={class:"filter-row"},Vl={class:"filter-item"},xl={class:"filter-row"},Rl={class:"filter-item"},Fl={class:"filter-actions"},Pl={class:"resources-list"},Dl={class:"pagination"},Sl={__name:"SearchResults",setup(ql){const x=Y(),M=el(),R=d([]),F=d(!1),P=d(0),_=d(1),N=d(10),i=d(null),k=d("createTime"),m=d(null),g=d(null),b=d(null),y=d(!1),D=E(()=>x.query.keyword||""),S=E(()=>{const n=x.query.examType;return n!==void 0?n==="null"?null:Number(n):null}),I=E(()=>{const n=["考研","考公","法考","教资","其他"];return i.value!==null?n[i.value]:"全部"}),f=async()=>{F.value=!0;try{const n={page:_.value,size:N.value,sortType:k.value,keyword:D.value};i.value!==null&&(n.examType=i.value),m.value&&(n.fileType=m.value),g.value&&(n.timeRange=g.value),b.value&&(n.downloadRange=b.value);const l=await al(n);R.value=l.data.records,P.value=l.data.total}catch(n){console.error("Failed to fetch resources:",n)}finally{F.value=!1}},w=()=>{_.value=1,f()},O=()=>{y.value=!y.value},j=()=>{i.value=null,k.value="createTime",m.value=null,g.value=null,b.value=null,_.value=1,f()},G=()=>{_.value=1,f()},H=n=>{_.value=n,f()},K=n=>{M.push({name:"ResourceDetail",params:{id:n}})},U=()=>{S.value!==null&&(i.value=S.value)};return Z(()=>x.query,()=>{U(),f()}),c(()=>{U(),f()}),(n,l)=>{const Q=v("el-icon"),q=v("el-button"),s=v("el-radio-button"),C=v("el-radio-group"),W=v("el-card"),X=v("el-pagination"),$=v("el-empty"),J=h("loading");return r(),p("div",dl,[l[45]||(l[45]=a("h2",{class:"page-title"},"搜索结果",-1)),a("div",rl,[D.value?(r(),p("span",il,[l[5]||(l[5]=o("关键词: ")),a("strong",null,T(D.value),1)])):B("",!0),S.value!==null?(r(),p("span",vl,[l[6]||(l[6]=o("考试类型: ")),a("strong",null,T(I.value),1)])):B("",!0),a("span",null,[l[7]||(l[7]=o("共找到 ")),a("strong",null,T(P.value),1),l[8]||(l[8]=o(" 条结果"))])]),a("div",_l,[e(W,null,{header:t(()=>[a("div",fl,[l[9]||(l[9]=a("span",null,"筛选条件",-1)),e(q,{type:"text",onClick:O,class:"toggle-btn"},{default:t(()=>[o(T(y.value?"收起":"展开")+" ",1),e(Q,null,{default:t(()=>[y.value?(r(),V(L(sl),{key:1})):(r(),V(L(ol),{key:0}))]),_:1})]),_:1})])]),default:t(()=>[a("div",pl,[a("div",ml,[a("div",gl,[l[16]||(l[16]=a("label",null,"考试类型：",-1)),e(C,{modelValue:i.value,"onUpdate:modelValue":l[0]||(l[0]=u=>i.value=u),onChange:w},{default:t(()=>[e(s,{label:null},{default:t(()=>l[10]||(l[10]=[o("全部")])),_:1,__:[10]}),e(s,{label:0},{default:t(()=>l[11]||(l[11]=[o("考研")])),_:1,__:[11]}),e(s,{label:1},{default:t(()=>l[12]||(l[12]=[o("考公")])),_:1,__:[12]}),e(s,{label:2},{default:t(()=>l[13]||(l[13]=[o("法考")])),_:1,__:[13]}),e(s,{label:3},{default:t(()=>l[14]||(l[14]=[o("教资")])),_:1,__:[14]}),e(s,{label:4},{default:t(()=>l[15]||(l[15]=[o("其他")])),_:1,__:[15]})]),_:1},8,["modelValue"])])]),a("div",bl,[a("div",yl,[l[22]||(l[22]=a("label",null,"排序方式：",-1)),e(C,{modelValue:k.value,"onUpdate:modelValue":l[1]||(l[1]=u=>k.value=u),onChange:w},{default:t(()=>[e(s,{label:"createTime"},{default:t(()=>l[17]||(l[17]=[o("最新上传")])),_:1,__:[17]}),e(s,{label:"downloadCount"},{default:t(()=>l[18]||(l[18]=[o("下载最多")])),_:1,__:[18]}),e(s,{label:"favoriteCount"},{default:t(()=>l[19]||(l[19]=[o("收藏最多")])),_:1,__:[19]}),e(s,{label:"commentCount"},{default:t(()=>l[20]||(l[20]=[o("评论最多")])),_:1,__:[20]}),e(s,{label:"relevance"},{default:t(()=>l[21]||(l[21]=[o("综合热度")])),_:1,__:[21]})]),_:1},8,["modelValue"])])])]),z(a("div",wl,[a("div",Cl,[a("div",kl,[l[30]||(l[30]=a("label",null,"文件类型：",-1)),e(C,{modelValue:m.value,"onUpdate:modelValue":l[2]||(l[2]=u=>m.value=u),onChange:w},{default:t(()=>[e(s,{label:null},{default:t(()=>l[23]||(l[23]=[o("全部")])),_:1,__:[23]}),e(s,{label:"PDF"},{default:t(()=>l[24]||(l[24]=[o("PDF")])),_:1,__:[24]}),e(s,{label:"DOC"},{default:t(()=>l[25]||(l[25]=[o("Word")])),_:1,__:[25]}),e(s,{label:"PPT"},{default:t(()=>l[26]||(l[26]=[o("PPT")])),_:1,__:[26]}),e(s,{label:"XLS"},{default:t(()=>l[27]||(l[27]=[o("Excel")])),_:1,__:[27]}),e(s,{label:"IMAGE"},{default:t(()=>l[28]||(l[28]=[o("图片")])),_:1,__:[28]}),e(s,{label:"OTHER"},{default:t(()=>l[29]||(l[29]=[o("其他")])),_:1,__:[29]})]),_:1},8,["modelValue"])])]),a("div",Tl,[a("div",Vl,[l[36]||(l[36]=a("label",null,"上传时间：",-1)),e(C,{modelValue:g.value,"onUpdate:modelValue":l[3]||(l[3]=u=>g.value=u),onChange:w},{default:t(()=>[e(s,{label:null},{default:t(()=>l[31]||(l[31]=[o("全部")])),_:1,__:[31]}),e(s,{label:"today"},{default:t(()=>l[32]||(l[32]=[o("今天")])),_:1,__:[32]}),e(s,{label:"week"},{default:t(()=>l[33]||(l[33]=[o("本周")])),_:1,__:[33]}),e(s,{label:"month"},{default:t(()=>l[34]||(l[34]=[o("本月")])),_:1,__:[34]}),e(s,{label:"quarter"},{default:t(()=>l[35]||(l[35]=[o("三个月内")])),_:1,__:[35]})]),_:1},8,["modelValue"])])]),a("div",xl,[a("div",Rl,[l[42]||(l[42]=a("label",null,"下载量：",-1)),e(C,{modelValue:b.value,"onUpdate:modelValue":l[4]||(l[4]=u=>b.value=u),onChange:w},{default:t(()=>[e(s,{label:null},{default:t(()=>l[37]||(l[37]=[o("全部")])),_:1,__:[37]}),e(s,{label:"0-10"},{default:t(()=>l[38]||(l[38]=[o("10次以下")])),_:1,__:[38]}),e(s,{label:"10-50"},{default:t(()=>l[39]||(l[39]=[o("10-50次")])),_:1,__:[39]}),e(s,{label:"50-100"},{default:t(()=>l[40]||(l[40]=[o("50-100次")])),_:1,__:[40]}),e(s,{label:"100+"},{default:t(()=>l[41]||(l[41]=[o("100次以上")])),_:1,__:[41]})]),_:1},8,["modelValue"])])]),a("div",Fl,[e(q,{onClick:j},{default:t(()=>l[43]||(l[43]=[o("重置筛选")])),_:1,__:[43]}),e(q,{type:"primary",onClick:G},{default:t(()=>l[44]||(l[44]=[o("应用筛选")])),_:1,__:[44]})])],512),[[tl,y.value]])]),_:1})]),z((r(),p("div",Pl,[R.value.length>0?(r(),p(A,{key:0},[(r(!0),p(A,null,ll(R.value,u=>(r(),V(nl,{key:u.id,resource:u,onClick:El=>K(u.id)},null,8,["resource","onClick"]))),128)),a("div",Dl,[e(X,{background:"",layout:"prev, pager, next",total:P.value,"page-size":N.value,"current-page":_.value,onCurrentChange:H},null,8,["total","page-size","current-page"])])],64)):(r(),V($,{key:1,description:"未找到相关资料"}))])),[[J,F.value]])])}}},Ll=ul(Sl,[["__scopeId","data-v-d2d82827"]]);export{Ll as default};
