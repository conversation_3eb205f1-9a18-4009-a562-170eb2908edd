import{X as t}from"./index-BC3U7MsG.js";function o(e){return t({url:"/comments",method:"post",data:e})}function s(e,m=1,r=10){return t({url:`/comments/resource/${e}`,method:"get",params:{page:m,size:r}})}function u(e){return t({url:`/comments/replies/${e}`,method:"get"})}function a(e=1,m=10){return t({url:"/comments/my",method:"get",params:{page:e,size:m}})}function c(e){return t({url:`/comments/${e}`,method:"delete"})}export{s as a,o as b,a as c,c as d,u as g};
