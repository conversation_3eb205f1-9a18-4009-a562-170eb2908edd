package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.Message;
import com.gongxingxue.mapper.MessageMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * Message service
 */
@Service
@RequiredArgsConstructor
public class MessageService extends ServiceImpl<MessageMapper, Message> {

    /**
     * 发送审核通知
     */
    public void sendAuditNotification(Long userId, String resourceName, Integer auditStatus, String rejectReason) {
        String title;
        String content;
        
        if (auditStatus == 1) {
            title = "资料审核通过";
            content = String.format("您上传的资料《%s》已通过审核，现在可以被其他用户查看和下载了。", resourceName);
        } else {
            title = "资料审核未通过";
            content = String.format("您上传的资料《%s》审核未通过。", resourceName);
            if (rejectReason != null && !rejectReason.trim().isEmpty()) {
                content += "驳回原因：" + rejectReason;
            }
        }
        
        Message message = new Message()
                .setUserId(userId)
                .setType(1)
                .setTitle(title)
                .setContent(content)
                .setIsRead(0)
                .setCreateTime(LocalDateTime.now());
        
        save(message);
    }

    /**
     * 发送评论回复通知
     */
    public void sendCommentReplyNotification(Long userId, String commenterName, String resourceName, Long resourceId) {
        // 不给自己发通知
        if (userId == null) {
            return;
        }
        
        Message message = new Message()
                .setUserId(userId)
                .setType(2)
                .setTitle("收到新回复")
                .setContent(String.format("%s 回复了您在《%s》中的评论", commenterName, resourceName))
                .setRelatedId(resourceId)
                .setIsRead(0)
                .setCreateTime(LocalDateTime.now());
        
        save(message);
    }

    /**
     * 获取用户消息列表
     */
    public Page<Message> getUserMessages(Long userId, Integer page, Integer size) {
        Page<Message> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(Message::getUserId, userId)
                   .orderByDesc(Message::getCreateTime);
        
        return page(pageParam, queryWrapper);
    }

    /**
     * 获取用户未读消息列表
     */
    public Page<Message> getUserUnreadMessages(Long userId, Integer page, Integer size) {
        Page<Message> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(Message::getUserId, userId)
                   .eq(Message::getIsRead, 0)
                   .orderByDesc(Message::getCreateTime);
        
        return page(pageParam, queryWrapper);
    }

    /**
     * 标记消息为已读
     */
    public boolean markAsRead(Long messageId, Long userId) {
        Message message = getById(messageId);
        if (message == null || !message.getUserId().equals(userId)) {
            return false;
        }
        
        message.setIsRead(1);
        return updateById(message);
    }

    /**
     * 标记所有消息为已读
     */
    public boolean markAllAsRead(Long userId) {
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getUserId, userId)
                   .eq(Message::getIsRead, 0);
        
        Message updateMessage = new Message().setIsRead(1);
        return update(updateMessage, queryWrapper);
    }

    /**
     * 获取未读消息数量
     */
    public Integer getUnreadCount(Long userId) {
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getUserId, userId)
                   .eq(Message::getIsRead, 0);
        
        return Math.toIntExact(count(queryWrapper));
    }
}
