package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Study task entity
 */
@Data
@Accessors(chain = true)
@TableName("study_task")
public class StudyTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Study plan ID
     */
    private Long planId;

    /**
     * Task name
     */
    private String name;

    /**
     * Task description
     */
    private String description;

    /**
     * Related resource ID
     */
    private Long resourceId;

    /**
     * Scheduled date
     */
    private LocalDateTime scheduledDate;

    /**
     * Completion status: 0=not completed, 1=completed
     */
    private Integer status;

    /**
     * Completion time
     */
    private LocalDateTime completionTime;

    /**
     * Creation time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * Logical delete flag: 0=not deleted, 1=deleted
     */
    @TableLogic
    private Integer deleted;
}
