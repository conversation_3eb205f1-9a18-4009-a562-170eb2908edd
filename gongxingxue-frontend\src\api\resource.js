import request from '../utils/request'

/**
 * Get public resources
 * @param {Object} params - Query parameters
 * @returns {Promise} - API response
 */
export function getPublicResources(params) {
  return request({
    url: '/resources/public',
    method: 'get',
    params
  })
}

/**
 * Get resource by ID (unified API - supports both authenticated and anonymous access)
 * @param {number} id - Resource ID
 * @returns {Promise} - API response
 */
export function getResourceById(id) {
  return request({
    url: `/resources/${id}`,
    method: 'get'
  })
}

/**
 * Get my resources
 * @returns {Promise} - API response
 */
export function getMyResources() {
  return request({
    url: '/resources/my',
    method: 'get'
  })
}

/**
 * Upload resource
 * @param {FormData} formData - Form data with file and resource info
 * @returns {Promise} - API response
 */
export function uploadResource(formData) {
  return request({
    url: '/resources/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * Delete resource
 * @param {number} id - Resource ID
 * @returns {Promise} - API response
 */
export function deleteResource(id) {
  return request({
    url: `/resources/${id}`,
    method: 'delete'
  })
}

/**
 * Download resource file
 * @param {number} id - Resource ID
 * @returns {Promise} - API response with blob
 */
export function downloadResource(id) {
  return request({
    url: `/resources/download/${id}`,
    method: 'get',
    responseType: 'blob'  // 重要：指定响应类型为blob
  })
}

/**
 * Get download URL for resource (for direct access)
 * @param {number} id - Resource ID
 * @returns {string} - Download URL
 */
export function getDownloadUrl(id) {
  return `/api/resources/download/${id}`
}

/**
 * Get view URL for resource
 * @param {number} id - Resource ID
 * @returns {string} - View URL
 */
export function getViewUrl(id) {
  return `/api/resources/view/${id}`
}
