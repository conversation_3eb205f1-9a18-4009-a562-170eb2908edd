<template>
  <div class="my-comments">
    <h2 class="page-title">我的评论</h2>

    <div class="comments-container" v-loading="loading">
      <template v-if="comments.length > 0">
        <div class="comment-item" v-for="comment in comments" :key="comment.id">
          <div class="comment-main">
            <div class="comment-content">
              <div class="comment-text">{{ comment.content }}</div>
              <div class="comment-time">{{ formatDate(comment.createTime) }}</div>
            </div>

            <div class="comment-resource">
              <div class="resource-info">
                <span>评论于资料：</span>
                <router-link
                  :to="{
                    name: 'ResourceDetail',
                    params: { id: comment.resourceId },
                    query: { commentId: comment.id }
                  }"
                  class="resource-link"
                >
                  {{ comment.resourceName || `资料 #${comment.resourceId}` }}
                </router-link>
              </div>
            </div>
          </div>

          <div class="comment-actions">
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(comment)"
            >
              删除
            </el-button>
          </div>
        </div>

        <div class="pagination">
          <el-pagination
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </template>

      <el-empty v-else description="暂无评论记录" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMyComments, deleteComment } from '../api/comment'
import { formatDate } from '../utils/auth'

// Data
const comments = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// Fetch my comments
const fetchMyComments = async () => {
  loading.value = true
  try {
    const response = await getMyComments(currentPage.value, pageSize.value)
    comments.value = response.data.records
    total.value = response.data.total
  } catch (error) {
    console.error('Failed to fetch my comments:', error)
    ElMessage.error('获取评论列表失败')
  } finally {
    loading.value = false
  }
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchMyComments()
}

// Handle delete
const handleDelete = async (comment) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该评论吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteComment(comment.id)
    ElMessage.success('评论已删除')

    // Refresh list
    fetchMyComments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete comment:', error)
      ElMessage.error('删除失败')
    }
  }
}

// Fetch data on component mount
onMounted(() => {
  fetchMyComments()
})
</script>

<style lang="scss" scoped>
.my-comments {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .comments-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    min-height: 300px;
  }

  .comment-item {
    display: flex; // 改为flex布局
    align-items: center; // 垂直居中对齐
    padding: 15px;
    border-bottom: 1px solid #ebeef5;

    &:last-child {
      border-bottom: none;
    }

    .comment-main {
      flex: 1; // 占据剩余空间
      min-width: 0; // 允许内容截断

      .comment-content {
        margin-bottom: 8px;

        .comment-text {
          font-size: 14px;
          line-height: 1.6;
          color: #303133;
          margin-bottom: 4px;
        }

        .comment-time {
          font-size: 12px;
          color: #909399;
          line-height: 1.2;
        }
      }

      .comment-resource {
        .resource-info {
          font-size: 13px;
          color: #606266;
          line-height: 1.4;

          .resource-link {
            color: #409eff;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }
    }

    .comment-actions {
      flex-shrink: 0; // 防止按钮被压缩
      margin-left: 15px; // 给按钮一些左边距
    }
  }

  .pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .my-comments {
    .comment-item {
      .comment-resource {
        flex-direction: column;
        align-items: flex-start;

        .resource-info {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
