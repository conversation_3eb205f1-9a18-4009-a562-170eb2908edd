<template>
  <div class="study-plan">
    <h2 class="page-title">学习规划</h2>

    <div class="plan-container">
      <div class="plan-sidebar">
        <div class="sidebar-header">
          <h3>我的计划</h3>
          <el-button type="primary" size="small" @click="showCreatePlanDialog">
            <el-icon><el-icon-plus /></el-icon>
            新建计划
          </el-button>
        </div>

        <div class="plan-list" v-loading="loadingPlans">
          <template v-if="plans.length > 0">
            <div
              v-for="plan in plans"
              :key="plan.id"
              class="plan-item"
              :class="{ active: currentPlan && currentPlan.id === plan.id }"
              @click="selectPlan(plan)"
            >
              <div class="plan-name">{{ plan.name }}</div>
              <div class="plan-type">{{ getPlanTypeText(plan.planType) }}</div>
            </div>
          </template>

          <el-empty v-else description="暂无学习计划" />
        </div>
      </div>

      <div class="plan-content">
        <template v-if="currentPlan">
          <div class="plan-header">
            <div class="plan-info">
              <h3>{{ currentPlan.name }}</h3>
              <div class="plan-meta">
                <span>{{ getPlanTypeText(currentPlan.planType) }}</span>
                <span>{{ formatDate(currentPlan.startDate, 'YYYY-MM-DD') }} 至 {{ formatDate(currentPlan.endDate, 'YYYY-MM-DD') }}</span>
              </div>
              <div class="plan-description" v-if="currentPlan.description">
                {{ currentPlan.description }}
              </div>
            </div>

            <div class="plan-actions">
              <el-button type="primary" size="small" @click="showAddTaskDialog">
                <el-icon><el-icon-plus /></el-icon>
                添加任务
              </el-button>
              <el-button type="danger" size="small" @click="handleDeletePlan">
                <el-icon><el-icon-delete /></el-icon>
                删除计划
              </el-button>
            </div>
          </div>

          <div class="task-list" v-loading="loadingTasks">
            <template v-if="tasks.length > 0">
              <el-table :data="tasks" style="width: 100%">
                <el-table-column prop="name" label="任务名称" min-width="200" />

                <el-table-column prop="scheduledDate" label="计划日期" width="180">
                  <template #default="{ row }">
                    {{ formatDate(row.scheduledDate, 'YYYY-MM-DD') }}
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 1 ? 'success' : 'info'">
                      {{ row.status === 1 ? '已完成' : '未完成' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="280" fixed="right">
                  <template #default="{ row }">
                    <div class="task-actions">
                      <div class="action-slot complete-slot">
                        <el-button
                          v-if="row.status === 0"
                          type="success"
                          size="small"
                          @click="handleCompleteTask(row)"
                        >
                          完成
                        </el-button>
                      </div>
                      <div class="action-slot resource-slot">
                        <el-button
                          v-if="row.resourceId"
                          type="primary"
                          size="small"
                          @click="viewResource(row.resourceId)"
                        >
                          查看资料
                        </el-button>
                      </div>
                      <div class="action-slot delete-slot">
                        <el-button
                          type="danger"
                          size="small"
                          @click="handleDeleteTask(row)"
                        >
                          删除
                        </el-button>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </template>

            <el-empty v-else description="暂无任务，点击'添加任务'开始规划" />
          </div>
        </template>

        <el-empty v-else description="请选择或创建一个学习计划" />
      </div>
    </div>

    <!-- Create Plan Dialog -->
    <el-dialog
      v-model="createPlanDialogVisible"
      title="创建学习计划"
      width="500px"
    >
      <el-form
        ref="planFormRef"
        :model="planForm"
        :rules="planRules"
        label-width="100px"
      >
        <el-form-item label="计划名称" prop="name">
          <el-input v-model="planForm.name" placeholder="请输入计划名称" />
        </el-form-item>

        <el-form-item label="计划类型" prop="planType">
          <el-select v-model="planForm.planType" placeholder="请选择计划类型">
            <el-option label="日计划" :value="0" />
            <el-option label="周计划" :value="1" />
            <el-option label="月计划" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item label="起止时间" prop="dateRange">
          <el-date-picker
            v-model="planForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="计划描述" prop="description">
          <el-input
            v-model="planForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入计划描述（选填）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="createPlanDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submittingPlan" @click="handleCreatePlan">
          创建
        </el-button>
      </template>
    </el-dialog>

    <!-- Add Task Dialog -->
    <el-dialog
      v-model="addTaskDialogVisible"
      title="添加任务"
      width="500px"
    >
      <el-form
        ref="taskFormRef"
        :model="taskForm"
        :rules="taskRules"
        label-width="100px"
      >
        <el-form-item label="任务名称" prop="name">
          <el-input v-model="taskForm.name" placeholder="请输入任务名称" />
        </el-form-item>

        <el-form-item label="计划日期" prop="scheduledDate">
          <el-date-picker
            v-model="taskForm.scheduledDate"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ss"
          />
        </el-form-item>

        <el-form-item label="关联资料" prop="resourceId">
          <el-select
            v-model="taskForm.resourceId"
            placeholder="请选择关联资料（选填）"
            filterable
            clearable
          >
            <el-option
              v-for="resource in resources"
              :key="resource.id"
              :label="resource.name"
              :value="resource.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述（选填）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="addTaskDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submittingTask" @click="handleAddTask">
          添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getMyStudyPlans,
  createStudyPlan,
  deleteStudyPlan,
  getTasksByPlanId,
  addTask,
  completeTask,
  deleteTask
} from '../api/studyPlan'
import { getMyResources } from '../api/resource'
import { formatDate } from '../utils/auth'

const router = useRouter()

// Data
const plans = ref([])
const currentPlan = ref(null)
const tasks = ref([])
const resources = ref([])
const loadingPlans = ref(false)
const loadingTasks = ref(false)

// Dialogs
const createPlanDialogVisible = ref(false)
const addTaskDialogVisible = ref(false)
const submittingPlan = ref(false)
const submittingTask = ref(false)

// Forms
const planFormRef = ref(null)
const taskFormRef = ref(null)

const planForm = reactive({
  name: '',
  planType: null,
  dateRange: [],
  description: ''
})

const taskForm = reactive({
  name: '',
  scheduledDate: '',
  resourceId: null,
  description: ''
})

// Form rules
const planRules = {
  name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' }
  ],
  planType: [
    { required: true, message: '请选择计划类型', trigger: 'change' }
  ],
  dateRange: [
    { required: true, message: '请选择起止时间', trigger: 'change' }
  ]
}

const taskRules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' }
  ],
  scheduledDate: [
    { required: true, message: '请选择计划日期', trigger: 'change' }
  ]
}

// Get plan type text
const getPlanTypeText = (planType) => {
  const planTypes = ['日计划', '周计划', '月计划']
  return planTypes[planType] || '未知'
}

// Fetch study plans
const fetchStudyPlans = async () => {
  loadingPlans.value = true
  try {
    const response = await getMyStudyPlans()
    plans.value = response.data

    // Select first plan if available
    if (plans.value.length > 0 && !currentPlan.value) {
      selectPlan(plans.value[0])
    }
  } catch (error) {
    console.error('Failed to fetch study plans:', error)
    ElMessage.error('获取学习计划失败')
  } finally {
    loadingPlans.value = false
  }
}

// Fetch tasks for current plan
const fetchTasks = async () => {
  if (!currentPlan.value) return

  loadingTasks.value = true
  try {
    const response = await getTasksByPlanId(currentPlan.value.id)
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loadingTasks.value = false
  }
}

// Fetch resources for task selection
const fetchResources = async () => {
  try {
    const response = await getMyResources()
    // Filter only approved resources
    resources.value = response.data.filter(r => r.auditStatus === 1)
  } catch (error) {
    console.error('Failed to fetch resources:', error)
  }
}

// Select plan
const selectPlan = (plan) => {
  currentPlan.value = plan
  fetchTasks()
}

// Show create plan dialog
const showCreatePlanDialog = () => {
  // Reset form
  if (planFormRef.value) {
    planFormRef.value.resetFields()
  }

  planForm.name = ''
  planForm.planType = null
  planForm.dateRange = []
  planForm.description = ''

  createPlanDialogVisible.value = true
}

// Show add task dialog
const showAddTaskDialog = () => {
  // Reset form
  if (taskFormRef.value) {
    taskFormRef.value.resetFields()
  }

  taskForm.name = ''
  taskForm.scheduledDate = ''
  taskForm.resourceId = null
  taskForm.description = ''

  // Fetch resources if needed
  if (resources.value.length === 0) {
    fetchResources()
  }

  addTaskDialogVisible.value = true
}

// Handle create plan
const handleCreatePlan = async () => {
  if (!planFormRef.value) return

  await planFormRef.value.validate(async (valid) => {
    if (!valid) return

    submittingPlan.value = true

    try {
      const [startDate, endDate] = planForm.dateRange

      await createStudyPlan({
        name: planForm.name,
        planType: planForm.planType,
        startDate,
        endDate,
        description: planForm.description
      })

      ElMessage.success('学习计划创建成功')
      createPlanDialogVisible.value = false

      // Refresh plans
      await fetchStudyPlans()
    } catch (error) {
      console.error('Failed to create study plan:', error)
      ElMessage.error('创建学习计划失败')
    } finally {
      submittingPlan.value = false
    }
  })
}

// Handle add task
const handleAddTask = async () => {
  if (!taskFormRef.value || !currentPlan.value) return

  await taskFormRef.value.validate(async (valid) => {
    if (!valid) return

    submittingTask.value = true

    try {
      await addTask(currentPlan.value.id, {
        name: taskForm.name,
        scheduledDate: taskForm.scheduledDate,
        resourceId: taskForm.resourceId || undefined,
        description: taskForm.description
      })

      ElMessage.success('任务添加成功')
      addTaskDialogVisible.value = false

      // Refresh tasks
      await fetchTasks()
    } catch (error) {
      console.error('Failed to add task:', error)
      ElMessage.error('添加任务失败')
    } finally {
      submittingTask.value = false
    }
  })
}

// Handle complete task
const handleCompleteTask = async (task) => {
  try {
    await completeTask(task.id)
    ElMessage.success('任务已完成')

    // Refresh tasks
    await fetchTasks()
  } catch (error) {
    console.error('Failed to complete task:', error)
    ElMessage.error('操作失败')
  }
}

// Handle delete task
const handleDeleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该任务吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteTask(task.id)
    ElMessage.success('任务已删除')

    // Refresh tasks
    await fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete task:', error)
      ElMessage.error('删除失败')
    }
  }
}

// Handle delete plan
const handleDeletePlan = async () => {
  if (!currentPlan.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除该学习计划吗？计划中的所有任务也将被删除。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteStudyPlan(currentPlan.value.id)
    ElMessage.success('学习计划已删除')

    // Reset current plan
    currentPlan.value = null
    tasks.value = []

    // Refresh plans
    await fetchStudyPlans()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete study plan:', error)
      ElMessage.error('删除失败')
    }
  }
}

// View resource
const viewResource = (resourceId) => {
  router.push({ name: 'ResourceDetail', params: { id: resourceId } })
}

// Fetch data on component mount
onMounted(() => {
  fetchStudyPlans()
})
</script>

<style lang="scss" scoped>
.study-plan {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .plan-container {
    display: flex;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    min-height: 500px;

    .plan-sidebar {
      width: 250px;
      border-right: 1px solid #ebeef5;

      .sidebar-header {
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .plan-list {
        padding: 10px 0;
        min-height: 200px;

        .plan-item {
          padding: 12px 15px;
          cursor: pointer;
          transition: background-color 0.3s;

          &:hover {
            background-color: #f5f7fa;
          }

          &.active {
            background-color: #ecf5ff;
            color: #409eff;
          }

          .plan-name {
            font-size: 14px;
            margin-bottom: 5px;
            font-weight: 500;
          }

          .plan-type {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }

    .plan-content {
      flex: 1;
      padding: 20px;

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 20px;

        .plan-info {
          h3 {
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: 500;
          }

          .plan-meta {
            color: #606266;
            font-size: 14px;
            margin-bottom: 10px;

            span {
              margin-right: 15px;
            }
          }

          .plan-description {
            color: #909399;
            font-size: 14px;
          }
        }
      }

      .task-list {
        min-height: 200px;

        .task-actions {
          display: flex;
          align-items: center;
          gap: 8px;

          .action-slot {
            display: flex;
            justify-content: center;
            align-items: center;

            .el-button {
              margin: 0;
              white-space: nowrap;
            }
          }

          .complete-slot {
            width: 60px;
          }

          .resource-slot {
            width: 80px;
          }

          .delete-slot {
            width: 60px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .study-plan {
    .plan-container {
      flex-direction: column;

      .plan-sidebar {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }
}
</style>
