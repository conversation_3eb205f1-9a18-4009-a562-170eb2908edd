import request from '../utils/request'

/**
 * Get pending resources
 * @param {Object} params - Query parameters
 * @returns {Promise} - API response
 */
export function getPendingResources(params) {
  return request({
    url: '/admin/resources/pending',
    method: 'get',
    params
  })
}

/**
 * Get approved resources
 * @param {Object} params - Query parameters
 * @returns {Promise} - API response
 */
export function getApprovedResources(params) {
  return request({
    url: '/admin/resources/approved',
    method: 'get',
    params
  })
}

/**
 * Get rejected resources
 * @param {Object} params - Query parameters
 * @returns {Promise} - API response
 */
export function getRejectedResources(params) {
  return request({
    url: '/admin/resources/rejected',
    method: 'get',
    params
  })
}

/**
 * Approve resource
 * @param {number} id - Resource ID
 * @returns {Promise} - API response
 */
export function approveResource(id) {
  return request({
    url: `/admin/resources/${id}/approve`,
    method: 'put'
  })
}

/**
 * Reject resource
 * @param {number} id - Resource ID
 * @param {string} rejectReason - Rejection reason
 * @returns {Promise} - API response
 */
export function rejectResource(id, rejectReason) {
  return request({
    url: `/admin/resources/${id}/reject`,
    method: 'put',
    params: {
      rejectReason
    }
  })
}

/**
 * Batch approve resources
 * @param {Array} resourceIds - Resource IDs
 * @returns {Promise} - API response
 */
export function batchApproveResources(resourceIds) {
  return request({
    url: '/admin/resources/batch-approve',
    method: 'put',
    data: resourceIds
  })
}

/**
 * Batch reject resources
 * @param {Array} resourceIds - Resource IDs
 * @param {string} rejectReason - Rejection reason
 * @returns {Promise} - API response
 */
export function batchRejectResources(resourceIds, rejectReason) {
  return request({
    url: '/admin/resources/batch-reject',
    method: 'put',
    data: resourceIds,
    params: {
      rejectReason
    }
  })
}

/**
 * Get user list
 * @param {Object} params - Query parameters
 * @returns {Promise} - API response
 */
export function getUserList(params) {
  return request({
    url: '/admin/users',
    method: 'get',
    params
  })
}

/**
 * Update user status
 * @param {number} id - User ID
 * @param {number} status - User status (0=normal, 1=disabled)
 * @returns {Promise} - API response
 */
export function updateUserStatus(id, status) {
  return request({
    url: `/admin/users/${id}/status`,
    method: 'put',
    params: {
      status
    }
  })
}
