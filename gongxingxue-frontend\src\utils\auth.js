const TOKEN_KEY = 'gongxingxue_token'

/**
 * Get token from localStorage
 * @returns {string|null} - JWT token
 */
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

/**
 * Set token in localStorage
 * @param {string} token - JWT token
 */
export function setToken(token) {
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * Remove token from localStorage
 */
export function removeToken() {
  localStorage.removeItem(TOKEN_KEY)
}

/**
 * Check if user is logged in
 * @returns {boolean} - True if user is logged in
 */
export function isLoggedIn() {
  const token = getToken()
  if (!token) return false

  try {
    // Simple check if token exists and is not expired
    // You can add more sophisticated JWT validation here if needed
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp > currentTime
  } catch (error) {
    // If token is malformed, consider user not logged in
    return false
  }
}

/**
 * Format date
 * @param {string|Date} date - Date to format
 * @param {string} format - Format string
 * @returns {string} - Formatted date string
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''

  const d = new Date(date)

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hours = String(d.getHours()).padStart(2, '0')
  const minutes = String(d.getMinutes()).padStart(2, '0')
  const seconds = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}
