import{b as f,d as s,e as t,f as e,g as d,k as p,l as m,j as r,P as g,m as i,Q as v,R as k,S as b}from"./index-BC3U7MsG.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const C={class:"not-found"},N={class:"not-found-container"},w={class:"error-illustration"},z={class:"error-icon"},B={class:"error-content"},F={class:"action-buttons"},y={__name:"NotFound",setup(V){const l=p(),u=()=>{l.push("/")},_=()=>{window.history.length>1?l.go(-1):l.push("/")},c=()=>{l.push("/search")};return(h,o)=>{const n=d("el-icon"),a=d("el-button");return m(),f("div",C,[s("div",N,[s("div",w,[o[0]||(o[0]=s("div",{class:"error-code"},"404",-1)),s("div",z,[t(n,{size:120,color:"#409EFF"},{default:e(()=>[t(r(g))]),_:1})])]),s("div",B,[o[4]||(o[4]=s("h1",{class:"error-title"},"页面未找到",-1)),o[5]||(o[5]=s("p",{class:"error-description"}," 抱歉，您访问的页面不存在或已被移除。 ",-1)),o[6]||(o[6]=s("p",{class:"error-suggestion"}," 您可以： ",-1)),o[7]||(o[7]=s("ul",{class:"suggestion-list"},[s("li",null,"检查网址是否正确"),s("li",null,"返回首页重新开始"),s("li",null,"使用搜索功能查找资料")],-1)),s("div",F,[t(a,{type:"primary",size:"large",onClick:u},{default:e(()=>[t(n,null,{default:e(()=>[t(r(v))]),_:1}),o[1]||(o[1]=i(" 返回首页 "))]),_:1,__:[1]}),t(a,{size:"large",onClick:_},{default:e(()=>[t(n,null,{default:e(()=>[t(r(k))]),_:1}),o[2]||(o[2]=i(" 返回上页 "))]),_:1,__:[2]}),t(a,{size:"large",onClick:c},{default:e(()=>[t(n,null,{default:e(()=>[t(r(b))]),_:1}),o[3]||(o[3]=i(" 搜索资料 "))]),_:1,__:[3]})])])])])}}},S=x(y,[["__scopeId","data-v-4bc36d65"]]);export{S as default};
