import{r as k,o as S,a0 as H,b as r,d as e,q as I,s as P,j as p,h as $,F,v as j,e as n,g as u,E as q,k as A,l as i,f as l,i as G,t as a,m as v,G as L}from"./index-BC3U7MsG.js";import{u as J,F as K}from"./FavoriteButton-fz6enDeT.js";import{_ as O}from"./_plugin-vue_export-helper-DlAUqK2U.js";const Q={class:"my-favorites"},W={class:"favorites-content"},X={class:"favorites-grid"},Y=["onClick"],Z={class:"resource-header"},tt={class:"resource-title"},et={class:"resource-tags"},st={key:0,class:"resource-description"},ot={class:"resource-meta"},at={class:"uploader"},nt={class:"download-count"},it={class:"comment-count"},rt={class:"favorite-count"},lt={class:"upload-time"},ct={class:"card-actions"},dt={class:"pagination-wrapper"},pt={__name:"MyFavorites",setup(ut){const g=A(),{favorites:_,loading:x,total:w,fetchFavorites:T}=J(),c=k(1),y=k(12),m=async(o=!1)=>{try{(o||_.length===0)&&await T(c.value,y.value)}catch(t){console.error("Fetch favorites error:",t),q.error("获取收藏列表失败")}},z=o=>{c.value=o,m(!0)},N=(o,t)=>{t||setTimeout(()=>{_.length===0&&c.value>1&&(c.value--,m())},100)},h=o=>{g.push({name:"ResourceDetail",params:{id:o}})},U=()=>{g.push({name:"Home"})},b=o=>({0:"考研",1:"考公",2:"法考",3:"教资",4:"其他"})[o]||"其他",B=o=>({0:"danger",1:"warning",2:"success",3:"primary",4:""})[o]||"",D=o=>{var d;return o&&((d=o.split(".").pop())==null?void 0:d.toUpperCase())||"Unknown"};return S(()=>{m(!0)}),H(()=>{m(!0)}),(o,t)=>{const d=u("el-tag"),C=u("el-button"),E=u("el-card"),M=u("el-pagination"),V=u("el-empty"),R=P("loading");return i(),r("div",Q,[t[6]||(t[6]=e("div",{class:"page-header"},[e("h2",null,"我的收藏"),e("p",{class:"subtitle"},"管理您收藏的学习资料")],-1)),I((i(),r("div",W,[p(_).length>0?(i(),r(F,{key:0},[e("div",X,[(i(!0),r(F,null,j(p(_),s=>(i(),r("div",{key:s.id,class:"favorite-card"},[n(E,{shadow:"hover",class:"resource-card"},{default:l(()=>[e("div",{class:"resource-info",onClick:f=>h(s.id)},[e("div",Z,[e("h3",tt,a(s.name),1),e("div",et,[n(d,{type:B(s.examType),size:"small"},{default:l(()=>[v(a(b(s.examType)),1)]),_:2},1032,["type"]),n(d,{type:"info",size:"small",class:"file-type-tag"},{default:l(()=>[v(a(D(s.originalFilename)),1)]),_:2},1024)])]),s.description?(i(),r("p",st,a(s.description),1)):G("",!0),e("div",ot,[e("span",at,a(s.username||"匿名用户"),1),t[0]||(t[0]=e("span",{class:"separator"},"•",-1)),e("span",nt,a(s.downloadCount||0)+"下载",1),t[1]||(t[1]=e("span",{class:"separator"},"•",-1)),e("span",it,a(s.commentCount||0)+"评论",1),t[2]||(t[2]=e("span",{class:"separator"},"•",-1)),e("span",rt,a(s.favoriteCount||0)+"收藏",1),t[3]||(t[3]=e("span",{class:"separator"},"•",-1)),e("span",lt,a(p(L)(s.createTime)),1)])],8,Y),e("div",ct,[n(C,{type:"primary",size:"small",onClick:f=>h(s.id)},{default:l(()=>t[4]||(t[4]=[v(" 查看详情 ")])),_:2,__:[4]},1032,["onClick"]),n(K,{"resource-id":s.id,"initial-favorited":!0,"favorite-count":s.favoriteCount||0,size:"small","onUpdate:favorited":f=>N(s.id,f)},null,8,["resource-id","favorite-count","onUpdate:favorited"])])]),_:2},1024)]))),128))]),e("div",dt,[n(M,{background:"",layout:"prev, pager, next, total",total:p(w),"page-size":y.value,"current-page":c.value,onCurrentChange:z},null,8,["total","page-size","current-page"])])],64)):(i(),$(V,{key:1,description:"暂无收藏的资料"},{default:l(()=>[n(C,{type:"primary",onClick:U},{default:l(()=>t[5]||(t[5]=[v(" 去首页看看 ")])),_:1,__:[5]})]),_:1}))])),[[R,p(x)]])])}}},ft=O(pt,[["__scopeId","data-v-8f9ae529"]]);export{ft as default};
