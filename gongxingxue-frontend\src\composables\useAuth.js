import { computed } from 'vue'
import { useUserStore } from '../store/user'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * 🎯 统一的权限管理组合函数
 *
 * 这个组合函数提供了：
 * 1. 统一的登录状态检查
 * 2. 友好的登录提示
 * 3. 权限控制的工具函数
 * 4. 简化的权限判断逻辑
 */
export function useAuth() {
  const userStore = useUserStore()
  const router = useRouter()

  // 计算属性：是否已登录
  const isLoggedIn = computed(() => userStore.isLoggedIn)

  // 计算属性：当前用户信息
  const currentUser = computed(() => userStore.user)

  // 计算属性：是否为管理员
  const isAdmin = computed(() => userStore.user?.role === 1)

  /**
   * 显示友好的登录提示
   * @param {string} action - 需要登录的操作名称
   * @param {boolean} autoRedirect - 是否自动跳转到登录页
   */
  const showLoginPrompt = async (action = '此操作', autoRedirect = true) => {
    try {
      await ElMessageBox.confirm(
        `${action}需要登录，是否前往登录页面？`,
        '需要登录',
        {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'info',
          center: true
        }
      )

      // 用户确认后跳转到登录页
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      })
    } catch {
      // 用户取消，不做任何操作
      if (!autoRedirect) {
        ElMessage.info('操作已取消')
      }
    }
  }

  /**
   * 检查权限并执行操作
   * @param {Function} action - 要执行的操作
   * @param {Object} options - 配置选项
   * @param {boolean} options.requireAuth - 是否需要登录（默认true）
   * @param {boolean} options.requireAdmin - 是否需要管理员权限（默认false）
   * @param {string} options.actionName - 操作名称，用于提示（默认'此操作'）
   * @param {boolean} options.showPrompt - 是否显示登录提示（默认true）
   */
  const checkAuthAndExecute = async (action, options = {}) => {
    const {
      requireAuth = true,
      requireAdmin = false,
      actionName = '此操作',
      showPrompt = true
    } = options

    // 如果不需要认证，直接执行
    if (!requireAuth) {
      return await action()
    }

    // 检查是否已登录
    if (!isLoggedIn.value) {
      if (showPrompt) {
        await showLoginPrompt(actionName)
      } else {
        ElMessage.warning(`${actionName}需要登录`)
      }
      return false
    }

    // 检查管理员权限
    if (requireAdmin && !isAdmin.value) {
      ElMessage.error('权限不足，需要管理员权限')
      return false
    }

    // 执行操作
    try {
      return await action()
    } catch (error) {
      console.error('操作执行失败:', error)
      ElMessage.error('操作失败，请稍后重试')
      return false
    }
  }

  /**
   * 简化的权限检查函数
   * @param {string} permission - 权限类型 ('login', 'admin')
   * @returns {boolean} - 是否有权限
   */
  const hasPermission = (permission) => {
    switch (permission) {
      case 'login':
        return isLoggedIn.value
      case 'admin':
        return isAdmin.value
      default:
        return true
    }
  }

  /**
   * 获取操作按钮的配置
   * @param {string} action - 操作类型
   * @returns {Object} - 按钮配置
   */
  const getActionConfig = (action) => {
    const configs = {
      download: {
        requireAuth: true,
        actionName: '下载资料',
        icon: 'Download',
        text: isLoggedIn.value ? '下载' : '登录后下载'
      },
      favorite: {
        requireAuth: true,
        actionName: '收藏资料',
        icon: 'Star',
        text: isLoggedIn.value ? '收藏' : '登录后收藏'
      },
      comment: {
        requireAuth: true,
        actionName: '发表评论',
        icon: 'ChatDotRound',
        text: isLoggedIn.value ? '发表评论' : '登录后评论'
      },
      upload: {
        requireAuth: true,
        actionName: '上传资料',
        icon: 'Upload',
        text: isLoggedIn.value ? '上传资料' : '登录后上传'
      },
      admin: {
        requireAuth: true,
        requireAdmin: true,
        actionName: '管理操作',
        icon: 'Setting',
        text: '管理'
      }
    }

    return configs[action] || {
      requireAuth: false,
      actionName: '操作',
      text: '操作'
    }
  }

  return {
    // 状态
    isLoggedIn,
    currentUser,
    isAdmin,

    // 方法
    showLoginPrompt,
    checkAuthAndExecute,
    hasPermission,
    getActionConfig
  }
}
