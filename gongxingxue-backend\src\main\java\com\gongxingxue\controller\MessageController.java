package com.gongxingxue.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gongxingxue.common.Result;
import com.gongxingxue.entity.Message;
import com.gongxingxue.service.MessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Message controller
 */
@RestController
@RequestMapping("/messages")
@RequiredArgsConstructor
@Api(tags = "消息管理")
public class MessageController {

    private final MessageService messageService;

    /**
     * 获取用户消息列表
     */
    @GetMapping
    @ApiOperation("获取用户消息列表")
    public Result<Page<Message>> getMessages(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String type,
            HttpServletRequest request) {
        
        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        Page<Message> messages;
        if ("unread".equals(type)) {
            messages = messageService.getUserUnreadMessages(userId, page, size);
        } else {
            messages = messageService.getUserMessages(userId, page, size);
        }
        
        return Result.success(messages);
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    @ApiOperation("获取未读消息数量")
    public Result<Integer> getUnreadCount(HttpServletRequest request) {
        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        Integer count = messageService.getUnreadCount(userId);
        return Result.success(count);
    }

    /**
     * 标记消息为已读
     */
    @PostMapping("/{id}/read")
    @ApiOperation("标记消息为已读")
    public Result<Void> markAsRead(@PathVariable Long id, HttpServletRequest request) {
        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = messageService.markAsRead(id, userId);
        if (success) {
            return Result.success("标记成功", null);
        } else {
            return Result.error("标记失败");
        }
    }

    /**
     * 标记所有消息为已读
     */
    @PostMapping("/read-all")
    @ApiOperation("标记所有消息为已读")
    public Result<Void> markAllAsRead(HttpServletRequest request) {
        // 获取当前用户ID
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }
        
        boolean success = messageService.markAllAsRead(userId);
        if (success) {
            return Result.success("全部标记成功", null);
        } else {
            return Result.error("标记失败");
        }
    }
}
