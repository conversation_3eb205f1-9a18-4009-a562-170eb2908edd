import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken } from './auth'
import { appConfig } from './config'
import router from '../router'

// Create axios instance
const service = axios.create({
  baseURL: appConfig.api.baseURL,
  timeout: appConfig.api.timeout
})

// Request interceptor
service.interceptors.request.use(
  config => {
    // Add token to request headers
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }



    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// Response interceptor
service.interceptors.response.use(
  response => {
    const res = response.data

    // 🎯 特殊处理：如果是blob响应（文件下载），直接返回
    if (response.data instanceof Blob) {
      return response
    }

    // Check if API returned success status
    if (res.success) {
      return res
    }

    // Handle error response
    ElMessage.error(res.message || '请求失败')
    return Promise.reject(new Error(res.message || '请求失败'))
  },
  error => {

    // Handle different HTTP error statuses
    const { response } = error

    if (response) {
      // Handle 401 Unauthorized
      if (response.status === 401) {
        // Check if this is a public API that shouldn't require authentication
        const isPublicAPI = response.config.url.includes('/resources/public') ||
                           response.config.url.includes('/comments/resource') ||
                           response.config.url.includes('/auth/') ||
                           response.config.url.match(/\/resources\/\d+$/) ||  // 资料详情页
                           response.config.url.includes('/resources/view')

        // Check if this is a background API that shouldn't show error messages
        const isBackgroundAPI = response.config.url.includes('/messages/unread-count') ||
                               response.config.url.includes('/favorites/check')

        if (!isPublicAPI && !isBackgroundAPI) {
          ElMessage.error('登录已过期，请重新登录')
          router.push('/login')
        } else if (isPublicAPI) {
          // For public APIs, just show the error without redirecting
          ElMessage.error('访问失败，请稍后重试')
        }
        // For background APIs, silently fail without showing error messages
      }
      // Handle 403 Forbidden
      else if (response.status === 403) {
        ElMessage.error('没有权限访问该资源')
        router.push('/')
      }
      // Handle other errors
      else {
        const errorMessage = response.data?.message || '请求失败'
        ElMessage.error(errorMessage)
      }
    } else {
      ElMessage.error('网络错误，请检查您的网络连接')
    }

    return Promise.reject(error)
  }
)

export default service
