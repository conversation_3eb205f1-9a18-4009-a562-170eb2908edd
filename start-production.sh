#!/bin/bash

# 生产环境启动脚本
echo "🚀 启动生产环境应用..."

# 设置工作目录
cd /www/wwwroot/gongxingxue/backend

# 加载环境变量
if [ -f "production.env" ]; then
    echo "📝 加载生产环境配置..."
    export $(cat production.env | grep -v '^#' | xargs)
    echo "✅ 环境变量加载完成"
else
    echo "❌ 错误：找不到 production.env 文件"
    exit 1
fi

# 检查必要的环境变量
echo "🔍 检查环境变量..."
if [ -z "$SPRING_PROFILES_ACTIVE" ]; then
    echo "❌ 错误：SPRING_PROFILES_ACTIVE 未设置"
    exit 1
fi

if [ -z "$DB_PASSWORD" ]; then
    echo "❌ 错误：DB_PASSWORD 未设置"
    exit 1
fi

echo "✅ 环境变量检查通过"
echo "   - 环境: $SPRING_PROFILES_ACTIVE"
echo "   - 端口: $SERVER_PORT"
echo "   - 数据库: $DB_NAME"

# 创建必要目录
mkdir -p /www/wwwroot/gongxingxue/uploads
mkdir -p /www/wwwroot/gongxingxue/logs

# 设置目录权限
chmod 755 /www/wwwroot/gongxingxue/uploads
chmod 755 /www/wwwroot/gongxingxue/logs

# 启动应用
echo "🎯 启动Spring Boot应用..."
nohup java -jar \
    -Xms512m \
    -Xmx1024m \
    -XX:+UseG1GC \
    -Dspring.profiles.active=prod \
    gongxingxue-backend-0.0.1-SNAPSHOT.jar \
    > /www/wwwroot/gongxingxue/logs/startup.log 2>&1 &

# 获取进程ID
APP_PID=$!
echo "✅ 应用已启动，PID: $APP_PID"

# 等待应用启动
echo "⏳ 等待应用启动..."
sleep 10

# 检查应用是否启动成功
if ps -p $APP_PID > /dev/null 2>&1; then
    echo "✅ 应用启动成功!"
    
    # 测试API
    echo "🧪 测试API连接..."
    sleep 5
    API_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8081/api/auth/captcha" || echo "000")
    
    if [ "$API_RESPONSE" = "200" ]; then
        echo "✅ API测试成功!"
        echo "🌐 前端访问地址: http://************"
        echo "🔗 后端API地址: http://************:8081/api"
    else
        echo "⚠️ API测试失败 (HTTP $API_RESPONSE)"
        echo "📋 请检查日志: tail -f /www/wwwroot/gongxingxue/logs/application.log"
    fi
else
    echo "❌ 应用启动失败"
    echo "📋 启动日志:"
    tail -20 /www/wwwroot/gongxingxue/logs/startup.log
    exit 1
fi

echo "🎉 部署完成!"
