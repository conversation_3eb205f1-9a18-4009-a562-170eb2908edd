import{r as C,p as w,b as d,d as s,e as o,f as t,g as l,i as y,k as b,l as c,j as n,T as x,m as u,U as z,Q as B,R as N,t as V}from"./index-BC3U7MsG.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const R={class:"server-error"},S={class:"error-container"},h={class:"error-illustration"},T={class:"error-icon"},j={class:"error-content"},q={class:"action-buttons"},D={key:0,class:"error-details"},F={class:"error-message"},H={__name:"ServerError",setup(I){const a=b(),p=w(),_=C(p.query.error||""),f=()=>{window.location.reload()},m=()=>{a.push("/")},g=()=>{window.history.length>1?a.go(-1):a.push("/")};return(M,e)=>{const r=l("el-icon"),i=l("el-button"),v=l("el-collapse-item"),k=l("el-collapse");return c(),d("div",R,[s("div",S,[s("div",h,[e[0]||(e[0]=s("div",{class:"error-code"},"500",-1)),s("div",T,[o(r,{size:120,color:"#F56C6C"},{default:t(()=>[o(n(x))]),_:1})])]),s("div",j,[e[4]||(e[4]=s("h1",{class:"error-title"},"服务器错误",-1)),e[5]||(e[5]=s("p",{class:"error-description"}," 抱歉，服务器遇到了一些问题，我们正在努力修复中。 ",-1)),e[6]||(e[6]=s("p",{class:"error-suggestion"}," 您可以： ",-1)),e[7]||(e[7]=s("ul",{class:"suggestion-list"},[s("li",null,"稍后再试"),s("li",null,"刷新页面"),s("li",null,"返回首页"),s("li",null,"如果问题持续存在，请联系管理员")],-1)),s("div",q,[o(i,{type:"primary",size:"large",onClick:f},{default:t(()=>[o(r,null,{default:t(()=>[o(n(z))]),_:1}),e[1]||(e[1]=u(" 刷新页面 "))]),_:1,__:[1]}),o(i,{size:"large",onClick:m},{default:t(()=>[o(r,null,{default:t(()=>[o(n(B))]),_:1}),e[2]||(e[2]=u(" 返回首页 "))]),_:1,__:[2]}),o(i,{size:"large",onClick:g},{default:t(()=>[o(r,null,{default:t(()=>[o(n(N))]),_:1}),e[3]||(e[3]=u(" 返回上页 "))]),_:1,__:[3]})]),_.value?(c(),d("div",D,[o(k,null,{default:t(()=>[o(v,{title:"错误详情",name:"1"},{default:t(()=>[s("pre",F,V(_.value),1)]),_:1})]),_:1})])):y("",!0)])])])}}},A=E(H,[["__scopeId","data-v-97acb645"]]);export{A as default};
