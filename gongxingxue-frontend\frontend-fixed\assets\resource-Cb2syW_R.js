import{X as r}from"./index-C6vAxNDy.js";function u(e){return r({url:"/resources/public",method:"get",params:e})}function s(e){return r({url:`/resources/${e}`,method:"get"})}function t(){return r({url:"/resources/my",method:"get"})}function c(e){return r({url:`/resources/${e}`,method:"delete"})}function n(e){return r({url:`/resources/download/${e}`,method:"get",responseType:"blob"})}export{s as a,t as b,c,n as d,u as g};
