import{r as k,o as G,b as y,d as u,q as N,s as $,h as p,f as a,E as g,g as d,k as V,l,e as o,m as r,t as c,H as j,j as L,G as O,i as A,D as v}from"./index-C6vAxNDy.js";import{b as I,c as J}from"./resource-Cb2syW_R.js";import{_ as q}from"./_plugin-vue_export-helper-DlAUqK2U.js";const H={class:"my-resources"},K={class:"resources-container"},U={key:1},Q={class:"file-info"},W={class:"file-type"},Y={class:"file-size"},Z={class:"download-count"},ee={class:"action-buttons"},te={__name:"MyResources",setup(se){const b=V(),m=k([]),f=k(!1),T=async()=>{f.value=!0;try{const e=await I();m.value=e.data}catch(e){console.error("Failed to fetch my resources:",e),g.error("获取资料列表失败")}finally{f.value=!1}},C=e=>["考研","考公","法考","教资","其他"][e]||"未知",D=e=>["primary","success","warning","danger",""][e]||"",w=e=>["待审核","已通过","已驳回"][e]||"未知",B=e=>["info","success","danger"][e]||"info",S=e=>{var i;if(!e)return"未知";switch((i=e.split(".").pop())==null?void 0:i.toUpperCase()){case"PDF":return"PDF";case"DOC":case"DOCX":return"DOC";case"PPT":case"PPTX":return"PPT";case"XLS":case"XLSX":return"XLS";case"TXT":return"TXT";case"JPG":case"JPEG":case"PNG":case"GIF":case"BMP":return"图片";default:return"其他"}},M=e=>{if(!e||e===0)return"0 B";const t=1024,i=["B","KB","MB","GB"],n=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,n)).toFixed(1))+" "+i[n]},P=e=>{const t=b.resolve({name:"ResourceDetail",params:{id:e.id}});window.open(t.href,"_blank")},R=e=>{v.alert(e.rejectReason||"管理员未提供驳回原因","驳回原因",{confirmButtonText:"确定"})},F=async e=>{try{await v.confirm("确定要删除该资料吗？删除后无法恢复。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await J(e.id),g.success("资料已删除"),T()}catch(t){t!=="cancel"&&(console.error("Failed to delete resource:",t),g.error("删除失败"))}};return G(()=>{T()}),(e,t)=>{const i=d("router-link"),n=d("el-table-column"),h=d("el-tag"),_=d("el-button"),z=d("el-table"),X=d("el-empty"),E=$("loading");return l(),y("div",H,[t[5]||(t[5]=u("h2",{class:"page-title"},"我的资料",-1)),N((l(),y("div",K,[m.value.length>0?(l(),p(z,{key:0,data:m.value,style:{width:"100%"}},{default:a(()=>[o(n,{prop:"name",label:"资料名称","min-width":"200"},{default:a(({row:s})=>[s.auditStatus===1?(l(),p(i,{key:0,to:{name:"ResourceDetail",params:{id:s.id}},class:"resource-link"},{default:a(()=>[r(c(s.name),1)]),_:2},1032,["to"])):(l(),y("span",U,c(s.name),1))]),_:1}),o(n,{prop:"examType",label:"考试类型",width:"100"},{default:a(({row:s})=>[o(h,{type:D(s.examType),class:j({"custom-purple":s.examType===4})},{default:a(()=>[r(c(C(s.examType)),1)]),_:2},1032,["type","class"])]),_:1}),o(n,{label:"文件信息",width:"120"},{default:a(({row:s})=>[u("div",Q,[u("span",W,c(S(s.originalFilename)),1),u("span",Y,c(M(s.fileSize)),1)])]),_:1}),o(n,{prop:"downloadCount",label:"下载次数",width:"100",align:"center"},{default:a(({row:s})=>[u("span",Z,c(s.downloadCount||0),1)]),_:1}),o(n,{prop:"createTime",label:"上传时间",width:"180"},{default:a(({row:s})=>[r(c(L(O)(s.createTime)),1)]),_:1}),o(n,{prop:"auditStatus",label:"审核状态",width:"120"},{default:a(({row:s})=>[o(h,{type:B(s.auditStatus),effect:"dark"},{default:a(()=>[r(c(w(s.auditStatus)),1)]),_:2},1032,["type"])]),_:1}),o(n,{label:"操作",width:"180",fixed:"right"},{default:a(({row:s})=>[u("div",ee,[s.auditStatus===1?(l(),p(_,{key:0,type:"primary",size:"small",onClick:x=>P(s),class:"action-btn"},{default:a(()=>t[1]||(t[1]=[r(" 查看原页 ")])),_:2,__:[1]},1032,["onClick"])):s.auditStatus===2?(l(),p(_,{key:1,type:"warning",size:"small",onClick:x=>R(s),class:"action-btn"},{default:a(()=>t[2]||(t[2]=[r(" 查看原因 ")])),_:2,__:[2]},1032,["onClick"])):A("",!0),o(_,{type:"danger",size:"small",onClick:x=>F(s),class:"action-btn"},{default:a(()=>t[3]||(t[3]=[r(" 删除 ")])),_:2,__:[3]},1032,["onClick"])])]),_:1})]),_:1},8,["data"])):(l(),p(X,{key:1,description:"暂无上传的资料"},{default:a(()=>[o(_,{type:"primary",onClick:t[0]||(t[0]=s=>e.$router.push("/upload"))},{default:a(()=>t[4]||(t[4]=[r("上传资料")])),_:1,__:[4]})]),_:1}))])),[[E,f.value]])])}}},le=q(te,[["__scopeId","data-v-726c61bc"]]);export{le as default};
