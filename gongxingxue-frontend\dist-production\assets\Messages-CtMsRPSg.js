import{a as F,m as I,b as P}from"./message-HPAOXBeD.js";import{r as l,c as S,G as q,o as G,b as r,d as t,q as H,i as f,e as u,f as h,g as c,h as C,s as L,F as U,v as $,E as _,l as n,m as w,t as y,H as j}from"./index-BC3U7MsG.js";import{_ as J}from"./_plugin-vue_export-helper-DlAUqK2U.js";const K={class:"messages"},O={class:"messages-container"},Q={class:"messages-header"},W={class:"header-actions"},X={class:"messages-list"},Y={class:"message-header"},Z={class:"message-title"},ee={class:"title-text"},ae={class:"message-time"},se={class:"message-content"},te={key:0,class:"message-actions"},ne={key:0,class:"pagination"},le={__name:"Messages",setup(oe){const o=l("all"),k=l(!1),v=l(!1),d=l([]),m=l(1),b=l(10),p=l(0),x=S(()=>e=>q(e)),g=async()=>{k.value=!0;try{const e=o.value==="unread"?"unread":null,a=await F(m.value,b.value,e);a.success&&(d.value=a.data.records.map(i=>({...i,marking:!1})),p.value=a.data.total)}catch{_.error("获取消息失败")}finally{k.value=!1}},M=e=>{o.value=e,m.value=1,g()},z=e=>{m.value=e,g()},E=async e=>{e.marking=!0;try{if(await I(e.id),e.isRead=1,_.success("已标记为已读"),window.dispatchEvent(new CustomEvent("messageRead")),o.value==="unread"){const a=d.value.findIndex(i=>i.id===e.id);a>-1&&(d.value.splice(a,1),p.value-=1)}}catch{_.error("标记失败")}finally{e.marking=!1}},V=async()=>{v.value=!0;try{await P(),_.success("全部消息已标记为已读"),window.dispatchEvent(new CustomEvent("messageRead")),g()}catch{_.error("操作失败")}finally{v.value=!1}};return G(()=>{g()}),(e,a)=>{const i=c("el-tab-pane"),T=c("el-tabs"),R=c("el-button"),A=c("el-tag"),B=c("el-empty"),D=c("el-pagination"),N=L("loading");return n(),r("div",K,[a[3]||(a[3]=t("h2",{class:"page-title"},"消息中心",-1)),t("div",O,[t("div",Q,[u(T,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=s=>o.value=s),onTabChange:M},{default:h(()=>[u(i,{label:"全部消息",name:"all"}),u(i,{label:"未读消息",name:"unread"})]),_:1},8,["modelValue"]),t("div",W,[o.value==="all"?(n(),C(R,{key:0,type:"primary",size:"small",onClick:V,disabled:v.value},{default:h(()=>[w(y(v.value?"标记中...":"全部已读"),1)]),_:1},8,["disabled"])):f("",!0)])]),H((n(),r("div",X,[d.value.length>0?(n(!0),r(U,{key:0},$(d.value,s=>(n(),r("div",{key:s.id,class:j(["message-item",{unread:!s.isRead}])},[t("div",Y,[t("div",Z,[t("span",ee,y(s.title),1),s.isRead?f("",!0):(n(),C(A,{key:0,type:"danger",size:"small"},{default:h(()=>a[1]||(a[1]=[w("未读")])),_:1,__:[1]}))]),t("span",ae,y(x.value(s.createTime)),1)]),t("div",se,y(s.content),1),s.isRead?f("",!0):(n(),r("div",te,[u(R,{size:"small",type:"primary",onClick:ie=>E(s),loading:s.marking},{default:h(()=>a[2]||(a[2]=[w(" 标记已读 ")])),_:2,__:[2]},1032,["onClick","loading"])]))],2))),128)):(n(),C(B,{key:1,description:"暂无消息"}))])),[[N,k.value]]),p.value>b.value?(n(),r("div",ne,[u(D,{background:"",layout:"prev, pager, next",total:p.value,"page-size":b.value,"current-page":m.value,onCurrentChange:z},null,8,["total","page-size","current-page"])])):f("",!0)])])}}},ue=J(le,[["__scopeId","data-v-30448c82"]]);export{ue as default};
