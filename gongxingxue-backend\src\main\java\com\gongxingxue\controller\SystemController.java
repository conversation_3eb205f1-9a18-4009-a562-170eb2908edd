package com.gongxingxue.controller;

import com.gongxingxue.common.Result;
import com.gongxingxue.service.FileStorageService;
import com.gongxingxue.util.FileUtil;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统监控控制器
 *
 * 提供系统状态监控功能，包括数据库连接池状态等
 */
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
@Api(tags = "系统监控API")
public class SystemController {

    private final DataSource dataSource;
    private final FileStorageService fileStorageService;
    private final FileUtil fileUtil;

    /**
     * 获取数据库连接池状态
     *
     * 这个接口可以帮助监控数据库连接池的使用情况：
     * - 当前活跃连接数
     * - 空闲连接数
     * - 等待连接的线程数
     * - 连接池配置信息
     */
    @GetMapping("/db-pool-status")
    @ApiOperation("获取数据库连接池状态")
    public Result<Map<String, Object>> getDbPoolStatus() {
        Map<String, Object> status = new HashMap<>();

        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                HikariPoolMXBean poolBean = hikariDataSource.getHikariPoolMXBean();

                // 连接池使用情况
                status.put("activeConnections", poolBean.getActiveConnections());
                status.put("idleConnections", poolBean.getIdleConnections());
                status.put("totalConnections", poolBean.getTotalConnections());
                status.put("threadsAwaitingConnection", poolBean.getThreadsAwaitingConnection());

                // 连接池配置
                status.put("maximumPoolSize", hikariDataSource.getMaximumPoolSize());
                status.put("minimumIdle", hikariDataSource.getMinimumIdle());
                status.put("connectionTimeout", hikariDataSource.getConnectionTimeout());
                status.put("idleTimeout", hikariDataSource.getIdleTimeout());
                status.put("maxLifetime", hikariDataSource.getMaxLifetime());
                status.put("poolName", hikariDataSource.getPoolName());

                // 健康状态评估
                int activeConnections = poolBean.getActiveConnections();
                int maximumPoolSize = hikariDataSource.getMaximumPoolSize();
                double usageRate = (double) activeConnections / maximumPoolSize;

                String healthStatus;
                if (usageRate < 0.7) {
                    healthStatus = "健康";
                } else if (usageRate < 0.9) {
                    healthStatus = "警告";
                } else {
                    healthStatus = "危险";
                }

                status.put("healthStatus", healthStatus);
                status.put("usageRate", String.format("%.2f%%", usageRate * 100));

            } else {
                status.put("error", "不支持的数据源类型");
            }

            return Result.success(status);

        } catch (Exception e) {
            status.put("error", "获取连接池状态失败：" + e.getMessage());
            return Result.error("获取连接池状态失败");
        }
    }

    /**
     * 获取系统基本信息
     */
    @GetMapping("/info")
    @ApiOperation("获取系统基本信息")
    public Result<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();

        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        info.put("javaVersion", System.getProperty("java.version"));
        info.put("totalMemory", runtime.totalMemory() / 1024 / 1024 + " MB");
        info.put("freeMemory", runtime.freeMemory() / 1024 / 1024 + " MB");
        info.put("maxMemory", runtime.maxMemory() / 1024 / 1024 + " MB");
        info.put("availableProcessors", runtime.availableProcessors());

        // 系统信息
        info.put("osName", System.getProperty("os.name"));
        info.put("osVersion", System.getProperty("os.version"));
        info.put("osArch", System.getProperty("os.arch"));

        return Result.success(info);
    }

    /**
     * 获取存储统计信息
     */
    @GetMapping("/storage-stats")
    @ApiOperation("获取存储统计信息")
    public Result<String> getStorageStats() {
        try {
            String stats = fileStorageService.getStorageStats();
            return Result.success(stats);
        } catch (Exception e) {
            return Result.error("获取存储统计失败：" + e.getMessage());
        }
    }

    /**
     * 查找UUID文件信息
     */
    @GetMapping("/file-info/{uuid}")
    @ApiOperation("根据UUID查找文件信息")
    public Result<String> getFileInfoByUUID(@PathVariable String uuid) {
        try {
            if (!FileUtil.isValidUUID(uuid)) {
                return Result.error("无效的UUID格式");
            }

            String filename = fileUtil.findFileByUUID(uuid);
            if (filename == null) {
                return Result.error("未找到UUID对应的文件: " + uuid);
            }

            String fileInfo = fileUtil.getFileInfo(filename);
            return Result.success(fileInfo);
        } catch (Exception e) {
            return Result.error("查找文件信息失败：" + e.getMessage());
        }
    }

    /**
     * 列出所有UUID文件
     */
    @GetMapping("/uuid-files")
    @ApiOperation("列出所有UUID文件")
    public Result<List<String>> listUUIDFiles() {
        try {
            List<String> files = fileUtil.listAllUUIDFiles();
            return Result.success(files);
        } catch (Exception e) {
            return Result.error("列出文件失败：" + e.getMessage());
        }
    }

    /**
     * 验证UUID格式
     */
    @GetMapping("/validate-uuid/{uuid}")
    @ApiOperation("验证UUID格式是否正确")
    public Result<Map<String, Object>> validateUUID(@PathVariable String uuid) {
        Map<String, Object> result = new HashMap<>();
        result.put("uuid", uuid);
        result.put("isValid", FileUtil.isValidUUID(uuid));

        if (FileUtil.isValidUUID(uuid)) {
            result.put("message", "UUID格式正确");
            // 检查文件是否存在
            String filename = fileUtil.findFileByUUID(uuid);
            result.put("fileExists", filename != null);
            if (filename != null) {
                result.put("filename", filename);
            }
        } else {
            result.put("message", "UUID格式错误");
        }

        return Result.success(result);
    }
}
