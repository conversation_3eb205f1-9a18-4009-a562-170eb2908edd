import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../store/user'

// 使用动态导入来避免组件加载问题
const MainLayout = () => import('../layouts/MainLayout.vue')
const AdminLayout = () => import('../layouts/AdminLayout.vue')

// Public pages
const Home = () => import('../views/Home.vue')
const Login = () => import('../views/Login.vue')
const Register = () => import('../views/Register.vue')
const ForgotPassword = () => import('../views/ForgotPassword.vue')
const ResetPassword = () => import('../views/ResetPassword.vue')
const ResourceDetail = () => import('../views/ResourceDetail.vue')
const SearchResults = () => import('../views/SearchResults.vue')

// Error pages
const NotFound = () => import('../views/NotFound.vue')
const ServerError = () => import('../views/ServerError.vue')
const NetworkError = () => import('../views/NetworkError.vue')

// Protected pages
const UploadResource = () => import('../views/UploadResource.vue')
const MyResources = () => import('../views/MyResources.vue')
const MyComments = () => import('../views/MyComments.vue')
const Messages = () => import('../views/Messages.vue')
const StudyPlan = () => import('../views/StudyPlan.vue')
const Profile = () => import('../views/Profile.vue')

// Admin pages
const AdminDashboard = () => import('../views/admin/Dashboard.vue')
const AdminResources = () => import('../views/admin/Resources.vue')
const AdminUsers = () => import('../views/admin/Users.vue')

const routes = [
  {
    path: '/',
    component: MainLayout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home,
        meta: { title: '首页' }
      },
      {
        path: 'login',
        name: 'Login',
        component: Login,
        meta: { title: '登录' }
      },
      {
        path: 'register',
        name: 'Register',
        component: Register,
        meta: { title: '注册' }
      },
      {
        path: 'forgot-password',
        name: 'ForgotPassword',
        component: ForgotPassword,
        meta: { title: '找回密码' }
      },
      {
        path: 'reset-password',
        name: 'ResetPassword',
        component: ResetPassword,
        meta: { title: '重置密码' }
      },
      {
        path: 'resources/:id',
        name: 'ResourceDetail',
        component: ResourceDetail,
        meta: { title: '资料详情' }
      },
      {
        path: 'search',
        name: 'SearchResults',
        component: SearchResults,
        meta: { title: '搜索结果' }
      },
      {
        path: 'upload',
        name: 'UploadResource',
        component: UploadResource,
        meta: { title: '上传资料', requiresAuth: true }
      },
      {
        path: 'my-resources',
        name: 'MyResources',
        component: MyResources,
        meta: { title: '我的资料', requiresAuth: true }
      },
      {
        path: 'my-comments',
        name: 'MyComments',
        component: MyComments,
        meta: { title: '我的评论', requiresAuth: true }
      },
      {
        path: 'my-favorites',
        name: 'MyFavorites',
        component: () => import('../views/MyFavorites.vue'),
        meta: { title: '我的收藏', requiresAuth: true }
      },
      {
        path: 'messages',
        name: 'Messages',
        component: Messages,
        meta: { title: '消息中心', requiresAuth: true }
      },
      {
        path: 'study-plan',
        name: 'StudyPlan',
        component: StudyPlan,
        meta: { title: '学习规划', requiresAuth: true }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile,
        meta: { title: '个人中心', requiresAuth: true }
      }
    ]
  },
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: '',
        name: 'AdminDashboard',
        component: AdminDashboard,
        meta: { title: '管理后台' }
      },
      {
        path: 'resources',
        name: 'AdminResources',
        component: AdminResources,
        meta: { title: '资料管理' }
      },
      {
        path: 'users',
        name: 'AdminUsers',
        component: AdminUsers,
        meta: { title: '用户管理' }
      }
    ]
  },
  // Error pages (outside of layouts)
  {
    path: '/404',
    name: 'NotFound',
    component: NotFound,
    meta: { title: '页面未找到' }
  },
  {
    path: '/500',
    name: 'ServerError',
    component: ServerError,
    meta: { title: '服务器错误' }
  },
  {
    path: '/network-error',
    name: 'NetworkError',
    component: NetworkError,
    meta: { title: '网络错误' }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFoundCatch',
    component: NotFound,
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard
router.beforeEach((to, from, next) => {
  // Update document title
  document.title = to.meta.title ? `${to.meta.title} - 考研/考公资料共享平台` : '考研/考公资料共享平台'

  // Check if the route requires authentication
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const userStore = useUserStore()

    // If not logged in, redirect to login page
    if (!userStore.isLoggedIn) {
      next({ name: 'Login', query: { redirect: to.fullPath } })
      return
    }

    // Check if the route requires admin role
    if (to.matched.some(record => record.meta.requiresAdmin) && userStore.user.role !== 1) {
      next({ name: 'Home' })
      return
    }
  }

  next()
})

export default router
