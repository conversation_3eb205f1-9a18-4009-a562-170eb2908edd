package com.gongxingxue.config;

import com.gongxingxue.interceptor.AdminInterceptor;
import com.gongxingxue.interceptor.AuthInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web configuration
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final AuthInterceptor authInterceptor;
    private final AdminInterceptor adminInterceptor;

    /**
     * Configure CORS
     * 注意：CORS配置已移至CorsConfig.java中统一管理，这里不再重复配置
     * 避免配置冲突，确保生产环境安全
     */
    // @Override
    // public void addCorsMappings(CorsRegistry registry) {
    //     // CORS配置已在CorsConfig.java中处理
    // }

    /**
     * Configure interceptors
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 🎯 简化的认证拦截器配置
        // 注意：具体的路径匹配逻辑在AuthInterceptor中处理，这里只做基础配置
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns(
                    // 认证相关
                    "/api/auth/**",
                    // 浏览功能
                    "/api/resources/public/**",
                    "/api/resources/view/**",
                    "/api/comments/resource/**",
                    // 系统功能
                    "/api/admin/resources/preview/**",
                    "/api/admin/resources/download/**",  // 管理员下载
                    "/swagger-ui/**", "/swagger-resources/**", "/v2/api-docs/**", "/v3/api-docs/**"
                );

        // Admin interceptor - 修正路径匹配
        registry.addInterceptor(adminInterceptor)
                .addPathPatterns("/api/admin/**")
                .excludePathPatterns(
                    "/api/admin/resources/preview/**",
                    "/api/admin/resources/download/**"  // 排除下载接口，无需管理员权限验证
                );
    }

    /**
     * Configure resource handlers
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // Swagger UI resources
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/");
        registry.addResourceHandler("/swagger-resources/**")
                .addResourceLocations("classpath:/META-INF/resources/");
    }
}
