#!/bin/bash

# 部署前安全检查脚本
# 用于检查部署前的安全配置是否正确

echo "🔍 开始部署前安全检查..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
PASS_COUNT=0
WARN_COUNT=0
FAIL_COUNT=0

# 检查函数
check_pass() {
    echo -e "${GREEN}✅ PASS${NC}: $1"
    ((PASS_COUNT++))
}

check_warn() {
    echo -e "${YELLOW}⚠️  WARN${NC}: $1"
    ((WARN_COUNT++))
}

check_fail() {
    echo -e "${RED}❌ FAIL${NC}: $1"
    ((FAIL_COUNT++))
}

echo ""
echo "=== 环境变量检查 ==="

# 检查必需的环境变量
required_vars=("SPRING_PROFILES_ACTIVE" "DB_PASSWORD" "MAIL_PASSWORD" "JWT_SECRET")

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        check_fail "缺少必需的环境变量: $var"
    else
        if [ "$var" = "JWT_SECRET" ]; then
            if [ ${#!var} -lt 32 ]; then
                check_fail "JWT_SECRET长度不足32字符，当前长度: ${#!var}"
            else
                check_pass "JWT_SECRET长度符合要求: ${#!var}字符"
            fi
        else
            check_pass "环境变量 $var 已设置"
        fi
    fi
done

# 检查生产环境配置
if [ "$SPRING_PROFILES_ACTIVE" = "prod" ]; then
    check_pass "使用生产环境配置"
    
    # 检查CORS配置
    if [ -z "$CORS_ALLOWED_ORIGINS" ]; then
        check_warn "未设置CORS_ALLOWED_ORIGINS，将使用默认值"
    else
        if [[ "$CORS_ALLOWED_ORIGINS" == *"*"* ]]; then
            check_fail "CORS配置包含通配符，存在安全风险"
        else
            check_pass "CORS配置安全: $CORS_ALLOWED_ORIGINS"
        fi
    fi
else
    check_warn "当前不是生产环境: $SPRING_PROFILES_ACTIVE"
fi

echo ""
echo "=== 文件权限检查 ==="

# 检查上传目录
UPLOAD_DIR=${FILE_UPLOAD_DIR:-"/app/uploads"}
if [ -d "$UPLOAD_DIR" ]; then
    check_pass "上传目录存在: $UPLOAD_DIR"
    
    # 检查目录权限
    if [ -w "$UPLOAD_DIR" ]; then
        check_pass "上传目录可写"
    else
        check_fail "上传目录不可写: $UPLOAD_DIR"
    fi
else
    check_warn "上传目录不存在，将自动创建: $UPLOAD_DIR"
fi

# 检查日志目录
LOG_DIR=${LOG_FILE:-"/app/logs/application.log"}
LOG_DIR=$(dirname "$LOG_DIR")
if [ -d "$LOG_DIR" ]; then
    check_pass "日志目录存在: $LOG_DIR"
else
    check_warn "日志目录不存在，将自动创建: $LOG_DIR"
fi

echo ""
echo "=== 网络和端口检查 ==="

# 检查端口占用
SERVER_PORT=${SERVER_PORT:-8081}
if netstat -tlnp 2>/dev/null | grep -q ":$SERVER_PORT "; then
    check_warn "端口 $SERVER_PORT 已被占用"
else
    check_pass "端口 $SERVER_PORT 可用"
fi

# 检查数据库连接
echo ""
echo "=== 数据库连接检查 ==="

DB_HOST=$(echo "$DB_URL" | sed -n 's/.*\/\/\([^:]*\):.*/\1/p')
DB_PORT=$(echo "$DB_URL" | sed -n 's/.*:\([0-9]*\)\/.*/\1/p')
DB_NAME=$(echo "$DB_URL" | sed -n 's/.*\/\([^?]*\).*/\1/p')

if [ -n "$DB_HOST" ] && [ -n "$DB_PORT" ]; then
    if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
        check_pass "数据库服务器可达: $DB_HOST:$DB_PORT"
    else
        check_fail "无法连接到数据库服务器: $DB_HOST:$DB_PORT"
    fi
else
    check_warn "无法解析数据库连接信息"
fi

echo ""
echo "=== 安全配置检查 ==="

# 检查Swagger配置
if [ "$SPRING_PROFILES_ACTIVE" = "prod" ]; then
    if [ "$SWAGGER_ENABLED" = "true" ]; then
        check_fail "生产环境不应启用Swagger"
    else
        check_pass "生产环境Swagger已禁用"
    fi
fi

# 检查默认密码
if [ "$DB_PASSWORD" = "123456" ] || [ "$DB_PASSWORD" = "password" ] || [ "$DB_PASSWORD" = "root" ]; then
    check_fail "数据库使用弱密码，存在安全风险"
else
    check_pass "数据库密码强度符合要求"
fi

echo ""
echo "=== 应用健康检查 ==="

# 检查Java版本
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    check_pass "Java版本: $JAVA_VERSION"
else
    check_fail "未找到Java运行环境"
fi

# 检查Maven
if command -v mvn &> /dev/null; then
    MVN_VERSION=$(mvn -version 2>&1 | head -n 1 | cut -d' ' -f3)
    check_pass "Maven版本: $MVN_VERSION"
else
    check_warn "未找到Maven（如果使用jar包部署可忽略）"
fi

echo ""
echo "=== 检查结果汇总 ==="
echo -e "${GREEN}通过: $PASS_COUNT${NC}"
echo -e "${YELLOW}警告: $WARN_COUNT${NC}"
echo -e "${RED}失败: $FAIL_COUNT${NC}"

echo ""
if [ $FAIL_COUNT -eq 0 ]; then
    if [ $WARN_COUNT -eq 0 ]; then
        echo -e "${GREEN}🎉 所有检查通过，可以安全部署！${NC}"
        exit 0
    else
        echo -e "${YELLOW}⚠️  存在警告项，建议修复后部署${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 存在严重问题，必须修复后才能部署！${NC}"
    echo ""
    echo "修复建议："
    echo "1. 设置所有必需的环境变量"
    echo "2. 确保JWT_SECRET长度至少32字符"
    echo "3. 配置安全的CORS策略"
    echo "4. 使用强密码"
    echo "5. 生产环境禁用Swagger"
    exit 2
fi
