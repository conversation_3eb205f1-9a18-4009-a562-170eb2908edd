package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Resource entity for study materials
 */
@Data
@Accessors(chain = true)
@TableName("resource")
public class Resource implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Resource name
     */
    private String name;

    /**
     * Resource description
     */
    private String description;

    /**
     * Exam type: 0=考研, 1=考公, 2=法考, 3=教资, 4=其他
     */
    private Integer examType;

    /**
     * File path
     */
    private String filePath;

    /**
     * Original filename
     */
    private String originalFilename;

    /**
     * File size in bytes
     */
    private Long fileSize;

    /**
     * File type
     */
    private String fileType;

    /**
     * User ID of uploader
     */
    private Long userId;

    /**
     * Username of uploader (not stored in database, for display only)
     */
    @TableField(exist = false)
    private String username;

    /**
     * Download count
     */
    private Integer downloadCount;

    /**
     * Comment count
     */
    private Integer commentCount;

    /**
     * Favorite count
     */
    private Integer favoriteCount;

    /**
     * Audit status: 0=pending, 1=approved, 2=rejected
     */
    private Integer auditStatus;

    /**
     * Audit time
     */
    private LocalDateTime auditTime;

    /**
     * Audit user ID
     */
    private Long auditUserId;

    /**
     * Rejection reason
     */
    private String rejectReason;

    /**
     * Creation time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * Logical delete flag: 0=not deleted, 1=deleted
     */
    @TableLogic
    private Integer deleted;
}
