package com.gongxingxue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gongxingxue.entity.UserFavorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import java.time.LocalDateTime;

/**
 * User favorite mapper
 */
@Mapper
public interface UserFavoriteMapper extends BaseMapper<UserFavorite> {

    /**
     * Check if user has favorited the resource
     */
    @Select("SELECT COUNT(*) > 0 FROM user_favorite WHERE user_id = #{userId} AND resource_id = #{resourceId} AND deleted = 0")
    boolean isFavorited(@Param("userId") Long userId, @Param("resourceId") Long resourceId);

    /**
     * Get favorite count for a resource
     */
    @Select("SELECT COUNT(*) FROM user_favorite WHERE resource_id = #{resourceId} AND deleted = 0")
    int getFavoriteCount(@Param("resourceId") Long resourceId);

    @Select("SELECT * FROM user_favorite WHERE user_id = #{userId} AND resource_id = #{resourceId} LIMIT 1")
    UserFavorite findByUserIdAndResourceId(@Param("userId") Long userId, @Param("resourceId") Long resourceId);

    @Update("UPDATE user_favorite SET deleted = 0, create_time = #{createTime} WHERE id = #{id}")
    int restoreFavorite(@Param("id") Long id, @Param("createTime") LocalDateTime createTime);
}
