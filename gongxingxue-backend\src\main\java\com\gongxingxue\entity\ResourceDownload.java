package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Resource download record entity
 */
@Data
@Accessors(chain = true)
@TableName("resource_download")
public class ResourceDownload implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Resource ID
     */
    private Long resourceId;

    /**
     * User ID
     */
    private Long userId;

    /**
     * IP address
     */
    private String ip;

    /**
     * User agent
     */
    private String userAgent;

    /**
     * Download time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
