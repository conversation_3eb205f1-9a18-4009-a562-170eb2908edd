<template>
  <div class="admin-layout">
    <el-container>
      <el-aside width="200px">
        <div class="logo">
          <h2>管理后台</h2>
        </div>
        <el-menu
          :default-active="activeMenu"
          class="el-menu-vertical"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <el-menu-item index="/admin">
            <el-icon><el-icon-menu /></el-icon>
            <span>控制台</span>
          </el-menu-item>
          <el-menu-item index="/admin/resources">
            <el-icon><el-icon-document /></el-icon>
            <span>资料管理</span>
          </el-menu-item>
          <el-menu-item index="/admin/users">
            <el-icon><el-icon-user /></el-icon>
            <span>用户管理</span>
          </el-menu-item>
          <el-menu-item index="/">
            <el-icon><el-icon-back /></el-icon>
            <span>返回前台</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <el-container>
        <el-header>
          <div class="header-content">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/admin' }">管理后台</el-breadcrumb-item>
                <el-breadcrumb-item v-if="currentRoute.meta.title">{{ currentRoute.meta.title }}</el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            
            <div class="user-info">
              <el-dropdown trigger="click" @command="handleCommand">
                <div class="avatar-container">
                  <el-avatar :size="32" :src="userAvatar">{{ userInitial }}</el-avatar>
                  <span class="username">{{ userName }}</span>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <el-main>
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '../store/user'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// Current route
const currentRoute = computed(() => route)
const activeMenu = computed(() => route.path)

// User info
const userAvatar = computed(() => userStore.user?.avatar || '')
const userName = computed(() => userStore.user?.nickname || userStore.user?.username || '')
const userInitial = computed(() => {
  const name = userName.value
  return name ? name.charAt(0).toUpperCase() : 'A'
})

// Handle dropdown menu commands
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push({ name: 'Profile' })
      break
    case 'logout':
      userStore.logout()
      ElMessage.success('已成功退出登录')
      router.push({ name: 'Home' })
      break
  }
}
</script>

<style lang="scss" scoped>
.admin-layout {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
  
  .el-aside {
    background-color: #304156;
    color: #bfcbd9;
    
    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      
      h2 {
        margin: 0;
        font-size: 1.2rem;
      }
    }
    
    .el-menu {
      border-right: none;
    }
  }
  
  .el-header {
    background-color: #fff;
    color: #333;
    line-height: 60px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 100%;
      
      .breadcrumb {
        margin-left: 10px;
      }
      
      .user-info {
        .avatar-container {
          display: flex;
          align-items: center;
          cursor: pointer;
          
          .username {
            margin-left: 8px;
            font-size: 14px;
          }
        }
      }
    }
  }
  
  .el-main {
    background-color: #f0f2f5;
    padding: 20px;
  }
}
</style>
