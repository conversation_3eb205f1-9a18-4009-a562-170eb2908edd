package com.gongxingxue.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.util.Random;

/**
 * Email service
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Value("${app.frontend.url:http://localhost:3000}")
    private String frontendUrl;

    @Value("${spring.mail.username}")
    private String fromEmail;

    /**
     * Generate random verification code
     */
    public String generateVerificationCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }

    /**
     * Send verification code email
     */
    public void sendVerificationCode(String toEmail, String code) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail); // 使用配置的发送邮箱
            helper.setTo(toEmail);
            helper.setSubject("【考研/考公资料共享平台】邮箱验证码");//【考研/考公资料共享平台】邮箱验证码

            String content = buildVerificationEmailContent(code);
            helper.setText(content, true);

            mailSender.send(message);
            log.info("Verification code email sent to: {}", toEmail);
        } catch (MessagingException e) {
            log.error("Failed to send verification code email to: {}", toEmail, e);
            throw new RuntimeException("邮件发送失败，请稍后重试");
        }
    }

    /**
     * Send password reset email
     */
    public void sendPasswordResetEmail(String toEmail, String resetToken) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail); // 使用配置的发送邮箱
            helper.setTo(toEmail);
            helper.setSubject("【考研/考公资料共享平台】密码重置");

            String content = buildPasswordResetEmailContent(resetToken);
            helper.setText(content, true);

            mailSender.send(message);
            log.info("Password reset email sent to: {}", toEmail);
        } catch (MessagingException e) {
            log.error("Failed to send password reset email to: {}", toEmail, e);
            throw new RuntimeException("邮件发送失败，请稍后重试");
        }
    }

    /**
     * Build verification email content
     */
    private String buildVerificationEmailContent(String code) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>邮箱验证</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<h2 style='color: #409eff; text-align: center;'>邮箱验证码</h2>" +
                "<div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;'>" +
                "<p>您好！</p>" +
                "<p>您正在进行邮箱验证，验证码为：</p>" +
                "<div style='text-align: center; margin: 20px 0;'>" +
                "<span style='font-size: 24px; font-weight: bold; color: #409eff; background-color: #f0f9ff; padding: 10px 20px; border-radius: 5px; letter-spacing: 2px;'>" +
                code +
                "</span>" +
                "</div>" +
                "<p style='color: #666;'>验证码有效期为5分钟，请及时使用。</p>" +
                "<p style='color: #666;'>如果这不是您的操作，请忽略此邮件。</p>" +
                "</div>" +
                "<hr style='border: none; border-top: 1px solid #eee; margin: 20px 0;'>" +
                "<p style='text-align: center; color: #999; font-size: 12px;'>此邮件由系统自动发送，请勿回复</p>" +
                "<p style='text-align: center; color: #999; font-size: 12px;'>考研/考公资料共享平台</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }

    /**
     * Build password reset email content
     */
    private String buildPasswordResetEmailContent(String resetToken) {
        String resetUrl = frontendUrl + "/reset-password?token=" + resetToken;

        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta charset='UTF-8'>" +
                "<title>密码重置</title>" +
                "</head>" +
                "<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>" +
                "<div style='max-width: 600px; margin: 0 auto; padding: 20px;'>" +
                "<h2 style='color: #409eff; text-align: center;'>密码重置</h2>" +
                "<div style='background-color: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0;'>" +
                "<p>您好！</p>" +
                "<p>您申请了密码重置，请点击下面的链接重置您的密码：</p>" +
                "<div style='text-align: center; margin: 20px 0;'>" +
                "<a href='" + resetUrl + "' style='display: inline-block; background-color: #409eff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;'>重置密码</a>" +
                "</div>" +
                "<p style='color: #666;'>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>" +
                "<p style='word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px;'>" + resetUrl + "</p>" +
                "<p style='color: #666;'>此链接有效期为30分钟，请及时使用。</p>" +
                "<p style='color: #666;'>如果这不是您的操作，请忽略此邮件。</p>" +
                "</div>" +
                "<hr style='border: none; border-top: 1px solid #eee; margin: 20px 0;'>" +
                "<p style='text-align: center; color: #999; font-size: 12px;'>此邮件由系统自动发送，请勿回复</p>" +
                "<p style='text-align: center; color: #999; font-size: 12px;'>考研/考公资料共享平台</p>" +
                "</div>" +
                "</body>" +
                "</html>";
    }
}
