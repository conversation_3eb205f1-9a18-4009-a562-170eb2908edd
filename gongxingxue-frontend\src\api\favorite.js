import request from '../utils/request'

/**
 * 添加收藏
 */
export function addFavorite(resourceId) {
  return request({
    url: `/favorites/${resourceId}`,
    method: 'post'
  })
}

/**
 * 取消收藏
 */
export function removeFavorite(resourceId) {
  return request({
    url: `/favorites/${resourceId}`,
    method: 'delete'
  })
}

/**
 * 切换收藏状态
 */
export function toggleFavorite(resourceId) {
  return request({
    url: `/favorites/${resourceId}/toggle`,
    method: 'put'
  })
}

/**
 * 检查是否已收藏
 */
export function checkFavorited(resourceId) {
  return request({
    url: `/favorites/check/${resourceId}`,
    method: 'get'
  })
}

/**
 * 获取我的收藏列表
 */
export function getMyFavorites(params = {}) {
  return request({
    url: '/favorites/my',
    method: 'get',
    params
  })
}
