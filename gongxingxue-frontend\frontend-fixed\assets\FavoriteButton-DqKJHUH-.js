import{X as F,a0 as _,x as S,u as b,r as p,w as y,o as x,a as B,g as E,h as M,l as C,f as L,m as N,b as T,i as z,t as I,j as k,a1 as U,a2 as V,E as v}from"./index-C6vAxNDy.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";function j(r){return F({url:`/favorites/${r}/toggle`,method:"put"})}function q(r){return F({url:`/favorites/check/${r}`,method:"get"})}function D(r={}){return F({url:"/favorites/my",method:"get",params:r})}const a=S({favorites:[],favoriteIds:new Set,loading:!1,total:0}),m=new Set;function R(){const r=async(e=1,i=12)=>{a.loading=!0;try{console.log("Fetching favorites from server...",{page:e,size:i});const o=await D({page:e,size:i});if(o.success)return a.favorites=o.data.records,a.total=o.data.total,a.favoriteIds.clear(),o.data.records.forEach(d=>{a.favoriteIds.add(d.id)}),o.data;console.error("Fetch favorites failed:",o)}catch(o){console.error("Fetch favorites error:",o)}finally{a.loading=!1}},g=e=>a.favoriteIds.has(e),n=e=>{a.favoriteIds.has(e.id)||(a.favorites.unshift(e),a.favoriteIds.add(e.id),a.total++,f(e.id,!0))},l=e=>{a.favoriteIds.has(e)&&(a.favorites=a.favorites.filter(i=>i.id!==e),a.favoriteIds.delete(e),a.total=Math.max(0,a.total-1),f(e,!1))},c=(e,i,o=null)=>{i&&o?n(o):l(e)},f=(e,i)=>{m.forEach(o=>{try{o(e,i)}catch(d){console.error("Favorite change callback error:",d)}})},h=e=>(m.add(e),()=>{m.delete(e)}),u=()=>r();return{..._(a),fetchFavorites:r,isFavorited:g,addToFavorites:n,removeFromFavorites:l,toggleFavoriteState:c,onFavoriteChange:h,refreshFavorites:u}}const X={key:0,class:"count"},A={__name:"FavoriteButton",props:{resourceId:{type:Number,required:!0},initialFavorited:{type:Boolean,default:!1},favoriteCount:{type:Number,default:0},showCount:{type:Boolean,default:!0},size:{type:String,default:"default"}},emits:["update:favorited","update:count"],setup(r,{emit:g}){const n=r,l=g,c=b(),{toggleFavoriteState:f,onFavoriteChange:h}=R(),u=p(!1),e=p(n.initialFavorited);y(()=>n.initialFavorited,t=>{e.value=t});const i=async()=>{if(!c.isLoggedIn){v.warning("请先登录后再收藏");return}u.value=!0;try{const t=await j(n.resourceId);if(t.success){e.value=t.data,v.success(t.message),f(n.resourceId,t.data,{id:n.resourceId,favoriteCount:n.favoriteCount}),l("update:favorited",t.data);const s=t.data?n.favoriteCount+1:Math.max(0,n.favoriteCount-1);l("update:count",s)}else v.error(t.message||"操作失败")}catch(t){console.error("Toggle favorite error:",t),v.error("操作失败，请稍后重试")}finally{u.value=!1}},o=async()=>{var t;if(!c.isLoggedIn||!c.token){e.value=!1;return}try{const s=await q(n.resourceId);s.success&&(e.value=s.data,l("update:favorited",s.data))}catch(s){console.error("Check favorite status error:",s),((t=s.response)==null?void 0:t.status)===401&&(e.value=!1)}},d=h((t,s)=>{t===n.resourceId&&(e.value=s,l("update:favorited",s))});return y(()=>c.isLoggedIn,(t,s)=>{t&&c.token?s!==void 0&&o():e.value=!1},{immediate:!0}),x(()=>{c.isLoggedIn&&c.token&&o()}),B(()=>{d()}),(t,s)=>{const w=E("el-button");return C(),M(w,{type:e.value?"warning":"default",icon:e.value?k(U):k(V),loading:u.value,size:r.size,onClick:i,class:"favorite-button"},{default:L(()=>[N(I(e.value?"已收藏":"收藏")+" ",1),r.showCount&&r.favoriteCount>0?(C(),T("span",X," ("+I(r.favoriteCount)+") ",1)):z("",!0)]),_:1},8,["type","icon","loading","size"])}}},J=$(A,[["__scopeId","data-v-75484330"]]);export{J as F,R as u};
