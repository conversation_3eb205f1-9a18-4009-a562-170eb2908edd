import{k as x,c as m,g as d,b as F,l as k,d as s,H as z,t as a,j as B,G as _,e as u,N as R,y as p,f,m as L,O as M,E as w}from"./index-BC3U7MsG.js";import{d as S}from"./resource-CWJ1Qb9i.js";import{F as X}from"./FavoriteButton-fz6enDeT.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const O={class:"resource-info"},G={class:"resource-header"},U={class:"resource-title"},I={class:"resource-description"},N={class:"resource-meta"},$={class:"file-info"},j={class:"uploader"},V={class:"download-count"},J={class:"comment-count"},q={class:"favorite-count"},A={class:"upload-time"},H={class:"resource-actions"},K={__name:"ResourceCard",props:{resource:{type:Object,required:!0}},emits:["click","update:favoriteCount"],setup(r,{emit:g}){const v=x(),n=r,C=g,y=m(()=>["考研","考公","法考","教资","其他"][n.resource.examType]||"未知"),h=m(()=>{const o=n.resource.description||"";return o.length>100?o.substring(0,100)+"...":o}),P=o=>{var t;if(!o)return"未知";switch((t=o.split(".").pop())==null?void 0:t.toUpperCase()){case"PDF":return"PDF";case"DOC":case"DOCX":return"DOC";case"PPT":case"PPTX":return"PPT";case"XLS":case"XLSX":return"XLS";case"TXT":return"TXT";case"JPG":case"JPEG":case"PNG":case"GIF":case"BMP":return"图片";default:return"其他"}},T=o=>{if(!o||o===0)return"0 B";const e=1024,t=["B","KB","MB","GB"],c=Math.floor(Math.log(o)/Math.log(e));return parseFloat((o/Math.pow(e,c)).toFixed(1))+" "+t[c]},b=async o=>{if(o.stopPropagation(),!M()){w.warning("请先登录后再下载资料"),v.push("/login");return}try{console.log("开始下载资料，ID:",n.resource.id),console.log("资料信息:",{id:n.resource.id,name:n.resource.name,originalFilename:n.resource.originalFilename,filePath:n.resource.filePath});const e=await S(n.resource.id);if(console.log("下载API响应:",e),!e||!e.data)throw new Error("下载响应为空");const t=e.data;if(!(t instanceof Blob)||t.size===0)throw new Error("下载的文件为空或格式错误");console.log("Downloaded blob size:",t.size,"bytes"),console.log("Downloaded blob type:",t.type);let c=n.resource.originalFilename||n.resource.name||`resource_${n.resource.id}`;const l=window.URL.createObjectURL(t),i=document.createElement("a");i.href=l,i.download=c,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(l),console.log("Download initiated for:",c,"Size:",t.size,"bytes")}catch(e){console.error("Download failed:",e),w.error("下载失败："+(e.message||"请稍后重试"))}},D=o=>{C("update:favoriteCount",o)};return(o,e)=>{const t=d("el-icon-download"),c=d("el-icon"),l=d("el-button");return k(),F("div",{class:"resource-card",onClick:e[1]||(e[1]=i=>o.$emit("click"))},[s("div",O,[s("div",G,[s("span",{class:z(["exam-type-tag",`exam-type-${r.resource.examType}`])},a(y.value),3),s("h3",U,a(r.resource.name),1)]),s("div",I,a(h.value),1),s("div",N,[s("span",$,a(P(r.resource.originalFilename))+" "+a(T(r.resource.fileSize)),1),e[2]||(e[2]=s("span",{class:"separator"},"•",-1)),s("span",j,a(r.resource.username||"匿名用户"),1),e[3]||(e[3]=s("span",{class:"separator"},"•",-1)),s("span",V,a(r.resource.downloadCount||0)+"下载",1),e[4]||(e[4]=s("span",{class:"separator"},"•",-1)),s("span",J,a(r.resource.commentCount||0)+"评论",1),e[5]||(e[5]=s("span",{class:"separator"},"•",-1)),s("span",q,a(r.resource.favoriteCount||0)+"收藏",1),e[6]||(e[6]=s("span",{class:"separator"},"•",-1)),s("span",A,a(B(_)(r.resource.createTime)),1)])]),s("div",H,[u(X,{"resource-id":r.resource.id,"favorite-count":r.resource.favoriteCount||0,size:"small","show-count":!1,"onUpdate:count":D,onClick:e[0]||(e[0]=p(()=>{},["stop"]))},null,8,["resource-id","favorite-count"]),u(l,{type:"primary",size:"small",onClick:p(b,["stop"])},{default:f(()=>[u(c,null,{default:f(()=>[u(t)]),_:1}),e[7]||(e[7]=L(" 下载 "))]),_:1,__:[7]}),R(o.$slots,"actions",{},void 0,!0)])])}}},ee=E(K,[["__scopeId","data-v-cf8fcc2c"]]);export{ee as R};
