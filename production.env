# 生产环境变量配置文件
# 根据已知信息创建的完整配置

# ================================
# 基础环境配置
# ================================
SPRING_PROFILES_ACTIVE=prod

# ================================
# 数据库配置
# ================================
DB_URL=************************************************************************************************************
DB_USERNAME=gongxingxue
DB_PASSWORD=gxxdb123456

# 数据库连接池配置
DB_MIN_IDLE=20
DB_MAX_POOL_SIZE=50

# ================================
# 服务器配置 - 生产环境
# ================================
SERVER_PORT=8081
SERVER_IP=************
# 生产环境服务器配置
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

# ================================
# 邮件配置
# ================================
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=kwrhhpdvjbvldgch

# ================================
# JWT配置
# ================================
JWT_SECRET=gongxingxue_prod_secret_key_2024_baota_panel_deployment
JWT_EXPIRATION=86400000

# ================================
# CORS和前端配置 - 生产环境
# ================================
# 移除localhost，只允许生产环境域名
CORS_ALLOWED_ORIGINS=http://************,https://************
FRONTEND_URL=http://************

# ================================
# 文件存储配置
# ================================
FILE_UPLOAD_DIR=/www/wwwroot/gongxingxue/uploads

# ================================
# 日志配置
# ================================
LOG_FILE=/www/wwwroot/gongxingxue/logs/application.log

# ================================
# 联系信息配置
# ================================
CONTACT_EMAIL=<EMAIL>

# ================================
# 安全配置 - 生产环境
# ================================
# 文件上传限制
FILE_MAX_SIZE=31457280
ALLOWED_FILE_TYPES=pdf,doc,docx,ppt,pptx,xls,xlsx,txt,jpg,jpeg,png,gif,mp4,mp3,avi,mov,wmv,flv,zip,rar,7z

# 验证码配置
CAPTCHA_EXPIRE_TIME=300

# 密码安全配置
PASSWORD_SALT=gongxingxue_password_salt_2025

# ================================
# 性能配置 - 生产环境
# ================================
# 数据库连接池优化
DB_POOL_INITIAL_SIZE=10
DB_POOL_MAX_ACTIVE=30
DB_POOL_MAX_IDLE=15
DB_POOL_MIN_IDLE=10

# 应用性能配置
APP_NAME=gongxingxue
APP_VERSION=1.0.0
NODE_ENV=production

# ================================
# 监控配置 - 生产环境
# ================================
HEALTH_CHECK_ENABLED=true
MANAGEMENT_ENDPOINTS_ENABLED=true

# 日志级别配置
LOG_LEVEL_ROOT=WARN
LOG_LEVEL_APP=INFO

# ================================
# 部署使用说明
# ================================
# 1. 服务器部署步骤：
#    - 将此文件上传到服务器 /www/wwwroot/gongxingxue/ 目录
#    - 重命名为 .env 或保持 production.env
#    - 设置文件权限：chmod 600 production.env
#
# 2. 目录权限设置：
#    - mkdir -p /www/wwwroot/gongxingxue/uploads
#    - mkdir -p /www/wwwroot/gongxingxue/logs
#    - chmod 755 /www/wwwroot/gongxingxue/uploads
#    - chmod 755 /www/wwwroot/gongxingxue/logs
#
# 3. 启动应用：
#    - cd /www/wwwroot/gongxingxue
#    - 加载环境变量后启动Spring Boot应用
#
# 4. 验证部署：
#    - 前端访问: http://************
#    - 后端API: http://************:8081/api
#    - 健康检查: http://************:8081/actuator/health

# ================================
# 生产环境部署信息
# ================================
# 服务器IP: ************
# 数据库: gongxingxue (用户: gongxingxue)
# 文件存储: /www/wwwroot/gongxingxue/uploads
# 日志文件: /www/wwwroot/gongxingxue/logs/application.log
# 应用端口: 8081
# 前端端口: 80 (通过nginx或直接访问)

# ================================
# 安全提醒
# ================================
# 1. 定期备份数据库和上传文件
# 2. 监控磁盘空间使用情况
# 3. 定期检查应用日志
# 4. 保护此配置文件的安全性
# 5. 考虑配置HTTPS证书提升安全性

