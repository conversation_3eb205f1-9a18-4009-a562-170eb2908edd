package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.Comment;
import com.gongxingxue.entity.Resource;
import com.gongxingxue.entity.User;
import com.gongxingxue.mapper.CommentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Comment service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommentService extends ServiceImpl<CommentMapper, Comment> {

    private final ResourceService resourceService;
    private final UserService userService;
    private final MessageService messageService;

    /**
     * 添加评论
     *
     * 评论系统采用两级结构设计：
     * 1. 顶级评论：直接对资料的评论，parentId为null
     * 2. 回复评论：对顶级评论的回复，parentId指向顶级评论
     *
     * 注意：不支持多级嵌套回复，所有回复都会归属到顶级评论下
     * 这样设计的好处是：
     * - 简化数据结构，避免无限嵌套
     * - 提高查询性能
     * - 改善用户体验，避免回复链过长
     *
     * @param resourceId 资料ID
     * @param userId 评论用户ID
     * @param content 评论内容
     * @param parentId 父评论ID，如果是顶级评论则为null
     * @return 创建的评论对象
     */
    @Transactional
    public Comment addComment(Long resourceId, Long userId, String content, Long parentId) {
        // 处理父评论逻辑：确保所有回复都指向顶级评论
        Long rootParentId = parentId;
        Comment parentComment = null;

        if (parentId != null) {
            // 查找父评论
            parentComment = getById(parentId);
            if (parentComment != null && parentComment.getParentId() != null) {
                // 如果父评论本身也是回复（有父评论），则将当前评论指向顶级评论
                // 这样确保了评论结构始终保持两级：顶级评论 -> 回复评论
                rootParentId = parentComment.getParentId();
                log.debug("检测到多级回复，将评论归属到顶级评论ID：{}", rootParentId);
            }
        }

        Comment comment = new Comment()
                .setResourceId(resourceId)
                .setUserId(userId)
                .setContent(content)
                .setParentId(rootParentId)  // Always point to the root comment
                .setCreateTime(LocalDateTime.now());

        // Save comment
        save(comment);

        // Update resource comment count
        resourceService.updateCommentCount(resourceId, 1);

        // Send reply notification if this is a reply
        if (parentComment != null && !parentComment.getUserId().equals(userId)) {
            // Get current user info
            User currentUser = userService.getUserById(userId);
            // Get resource info
            Resource resource = resourceService.getById(resourceId);

            if (currentUser != null && resource != null) {
                String commenterName = currentUser.getNickname() != null ? currentUser.getNickname() : currentUser.getUsername();
                messageService.sendCommentReplyNotification(
                    parentComment.getUserId(),
                    commenterName,
                    resource.getName(),
                    resourceId
                );
            }
        }

        return comment;
    }

    /**
     * Get comments by resource ID
     */
    public Page<Comment> getCommentsByResourceId(Long resourceId, Integer page, Integer size) {
        Page<Comment> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();

        // Filter by resource ID
        queryWrapper.eq(Comment::getResourceId, resourceId);

        // Only get top-level comments
        queryWrapper.isNull(Comment::getParentId);

        // Sort by creation time
        queryWrapper.orderByDesc(Comment::getCreateTime);

        Page<Comment> commentPage = page(pageParam, queryWrapper);

        // Load user information and reply count for each comment
        for (Comment comment : commentPage.getRecords()) {
            // Load user information
            User user = userService.getUserById(comment.getUserId());
            if (user != null) {
                // Create a simple user object to avoid exposing sensitive information
                User safeUser = new User()
                    .setId(user.getId())
                    .setUsername(user.getUsername())
                    .setNickname(user.getNickname())
                    .setAvatar(user.getAvatar());
                comment.setUser(safeUser);
            }

            // Calculate reply count
            LambdaQueryWrapper<Comment> replyWrapper = new LambdaQueryWrapper<>();
            replyWrapper.eq(Comment::getParentId, comment.getId());
            int replyCount = Math.toIntExact(count(replyWrapper));
            comment.setReplyCount(replyCount);
        }

        return commentPage;
    }

    /**
     * Get replies by parent comment ID
     */
    public List<Comment> getRepliesByParentId(Long parentId) {
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();

        // Filter by parent ID
        queryWrapper.eq(Comment::getParentId, parentId);

        // Sort by creation time
        queryWrapper.orderByAsc(Comment::getCreateTime);

        List<Comment> replies = list(queryWrapper);

        // Load user information for each reply
        for (Comment reply : replies) {
            User user = userService.getUserById(reply.getUserId());
            if (user != null) {
                // Create a simple user object to avoid exposing sensitive information
                User safeUser = new User()
                    .setId(user.getId())
                    .setUsername(user.getUsername())
                    .setNickname(user.getNickname())
                    .setAvatar(user.getAvatar());
                reply.setUser(safeUser);
            }
        }

        return replies;
    }

    /**
     * Get comments by user ID
     */
    public Page<Comment> getCommentsByUserId(Long userId, Integer page, Integer size) {
        Page<Comment> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();

        // Filter by user ID
        queryWrapper.eq(Comment::getUserId, userId);

        // Explicitly filter out deleted comments to ensure they don't appear in user's comment list
        queryWrapper.eq(Comment::getDeleted, 0);

        // Sort by creation time
        queryWrapper.orderByDesc(Comment::getCreateTime);

        Page<Comment> commentPage = page(pageParam, queryWrapper);

        // Load user information, resource information and reply count for each comment
        for (Comment comment : commentPage.getRecords()) {
            // Load user information
            User user = userService.getUserById(comment.getUserId());
            if (user != null) {
                // Create a simple user object to avoid exposing sensitive information
                User safeUser = new User()
                    .setId(user.getId())
                    .setUsername(user.getUsername())
                    .setNickname(user.getNickname())
                    .setAvatar(user.getAvatar());
                comment.setUser(safeUser);
            }

            // Load resource information
            Resource resource = resourceService.getById(comment.getResourceId());
            if (resource != null) {
                comment.setResourceName(resource.getName());
            }

            // Calculate reply count
            LambdaQueryWrapper<Comment> replyWrapper = new LambdaQueryWrapper<>();
            replyWrapper.eq(Comment::getParentId, comment.getId());
            int replyCount = Math.toIntExact(count(replyWrapper));
            comment.setReplyCount(replyCount);
        }

        return commentPage;
    }

    /**
     * Delete comment
     */
    @Transactional
    public boolean deleteComment(Long commentId, Long userId) {
        Comment comment = getById(commentId);
        if (comment == null || !comment.getUserId().equals(userId)) {
            return false;
        }

        // Delete comment
        boolean result = removeById(commentId);

        // Update resource comment count
        if (result) {
            resourceService.updateCommentCount(comment.getResourceId(), -1);

            // Delete replies
            LambdaQueryWrapper<Comment> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Comment::getParentId, commentId);
            List<Comment> replies = list(queryWrapper);

            for (Comment reply : replies) {
                removeById(reply.getId());
                resourceService.updateCommentCount(reply.getResourceId(), -1);
            }
        }

        return result;
    }
}
