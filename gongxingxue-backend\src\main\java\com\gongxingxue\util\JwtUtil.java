package com.gongxingxue.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT utility class for token generation and validation
 */
@Component
public class JwtUtil {

    @Value("${jwt.secret}")
    private String secret;

    @Value("${jwt.expiration}")
    private Long expiration;

    private SecretKey key;

    @PostConstruct
    public void init() {
        this.key = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 为用户生成JWT token
     *
     * JWT token包含以下信息：
     * - subject: 用户名（作为主要标识）
     * - userId: 用户ID（用于快速查询）
     * - role: 用户角色（0=普通用户，1=管理员）
     * - iat: 签发时间
     * - exp: 过期时间
     *
     * 安全考虑：
     * - 使用HMAC SHA256算法签名，确保token不被篡改
     * - 设置合理的过期时间，平衡安全性和用户体验
     * - 不在token中存储敏感信息（如密码）
     *
     * @param username 用户名
     * @param userId 用户ID
     * @param role 用户角色（0=普通用户，1=管理员）
     * @return 生成的JWT token字符串
     */
    public String generateToken(String username, Long userId, Integer role) {
        // 创建自定义声明（claims），存储用户的额外信息
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);    // 用户ID，便于后续快速查询用户信息
        claims.put("role", role);        // 用户角色，用于权限控制

        // 调用内部方法创建token，username作为subject（主题）
        return createToken(claims, username);
    }

    /**
     * Create token
     */
    private String createToken(Map<String, Object> claims, String subject) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiration))
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * Validate token
     */
    public Boolean validateToken(String token) {
        try {
            return !isTokenExpired(token);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Check if token is expired
     */
    private Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    /**
     * Get expiration date from token
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * Get username from token
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    /**
     * Get user ID from token
     */
    public Long getUserIdFromToken(String token) {
        return ((Number) getAllClaimsFromToken(token).get("userId")).longValue();
    }

    /**
     * Get user role from token
     */
    public Integer getRoleFromToken(String token) {
        return ((Number) getAllClaimsFromToken(token).get("role")).intValue();
    }

    /**
     * Get claim from token
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    /**
     * Get all claims from token
     */
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
}
