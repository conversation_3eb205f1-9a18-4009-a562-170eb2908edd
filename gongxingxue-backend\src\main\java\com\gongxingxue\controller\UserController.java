package com.gongxingxue.controller;

import com.gongxingxue.common.Result;
import com.gongxingxue.entity.User;
import com.gongxingxue.service.EmailService;
import com.gongxingxue.service.UserService;
import com.gongxingxue.service.VerificationCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * User controller
 */
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Api(tags = "User API")
@Slf4j
public class UserController {

    private final UserService userService;
    private final EmailService emailService;
    private final VerificationCodeService verificationCodeService;

    /**
     * Get current user info
     */
    @GetMapping("/current")
    @ApiOperation("Get current user info")
    public Result<User> getCurrentUser(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        User user = userService.getUserById(userId);

        // Remove sensitive information
        user.setPassword(null);

        return Result.success(user);
    }

    /**
     * Update profile
     */
    @PutMapping("/profile")
    @ApiOperation("Update user profile")
    public Result<Void> updateProfile(
            @RequestParam(required = false) String nickname,
            @RequestParam(required = false) String avatar,
            @RequestParam(required = false) String email,
            @RequestParam(required = false) String phone,
            HttpServletRequest request) {

        Long userId = (Long) request.getAttribute("userId");
        boolean success = userService.updateProfile(userId, nickname, avatar, email, phone);

        if (success) {
            return Result.success("Profile updated successfully", null);
        } else {
            return Result.error("Failed to update profile");
        }
    }

    /**
     * Change password
     */
    @PutMapping("/password")
    @ApiOperation("Change password")
    public Result<Void> changePassword(
            @RequestParam @NotBlank String oldPassword,
            @RequestParam @NotBlank String newPassword,
            HttpServletRequest request) {

        try {
            Long userId = (Long) request.getAttribute("userId");
            boolean success = userService.changePassword(userId, oldPassword, newPassword);

            if (success) {
                return Result.success("Password changed successfully", null);
            } else {
                return Result.error("Failed to change password");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Send email verification code
     */
    @PostMapping("/email/send-code")
    @ApiOperation("Send email verification code")
    public Result<Void> sendEmailVerificationCode(
            @RequestParam @NotBlank @Email String email,
            HttpServletRequest request) {

        try {
            Long userId = (Long) request.getAttribute("userId");

            // Check if email is already bound to another user
            if (userService.isEmailBound(email, userId)) {
                return Result.error("该邮箱已被其他用户绑定");
            }

            // Check if code was sent recently (prevent spam)
            if (verificationCodeService.hasValidCode(email)) {
                return Result.error("验证码已发送，请稍后再试");
            }

            // Generate and send verification code
            String code = emailService.generateVerificationCode();
            emailService.sendVerificationCode(email, code);

            // Store code with 5 minutes expiration
            verificationCodeService.storeCode(email, code, 5);

            return Result.success("验证码已发送到您的邮箱", null);
        } catch (Exception e) {
            log.error("邮件发送失败: {}", e.getMessage(), e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * Bind email
     */
    @PostMapping("/email/bind")
    @ApiOperation("Bind email to user account")
    public Result<Void> bindEmail(
            @RequestParam @NotBlank @Email String email,
            @RequestParam @NotBlank String code,
            HttpServletRequest request) {

        try {
            Long userId = (Long) request.getAttribute("userId");

            // Verify code
            if (!verificationCodeService.verifyCode(email, code)) {
                return Result.error("验证码错误或已过期");
            }

            // Check if email is already bound to another user
            if (userService.isEmailBound(email, userId)) {
                return Result.error("该邮箱已被其他用户绑定");
            }

            // Bind email
            boolean success = userService.bindEmail(userId, email);

            if (success) {
                return Result.success("邮箱绑定成功", null);
            } else {
                return Result.error("邮箱绑定失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Unbind email
     */
    @DeleteMapping("/email/unbind")
    @ApiOperation("Unbind email from user account")
    public Result<Void> unbindEmail(HttpServletRequest request) {
        try {
            Long userId = (Long) request.getAttribute("userId");
            boolean success = userService.unbindEmail(userId);

            if (success) {
                return Result.success("邮箱解绑成功", null);
            } else {
                return Result.error("邮箱解绑失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
