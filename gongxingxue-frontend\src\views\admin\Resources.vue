<template>
  <div class="admin-resources">
    <h2 class="page-title">资料管理</h2>

    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="待审核资料" name="pending">
        <div class="resources-table" v-loading="loading">
          <div class="table-filters">
            <el-form :inline="true" :model="pendingFilters">
              <el-form-item label="考试类型">
                <el-select v-model="pendingFilters.examType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="考研" :value="0" />
                  <el-option label="考公" :value="1" />
                  <el-option label="法考" :value="2" />
                  <el-option label="教资" :value="3" />
                  <el-option label="其他" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="文件类型">
                <el-select v-model="pendingFilters.fileType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="PDF" value="PDF" />
                  <el-option label="DOC" value="DOC" />
                  <el-option label="DOCX" value="DOCX" />
                  <el-option label="PPT" value="PPT" />
                  <el-option label="PPTX" value="PPTX" />
                  <el-option label="XLS" value="XLS" />
                  <el-option label="XLSX" value="XLSX" />
                  <el-option label="TXT" value="TXT" />
                  <el-option label="图片" value="IMAGE" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
              </el-form-item>

              <el-form-item label="上传用户">
                <el-input v-model="pendingFilters.username" placeholder="用户名" style="width: 150px;" />
              </el-form-item>

              <el-form-item label="关键词">
                <el-input v-model="pendingFilters.keyword" placeholder="资料名称" style="width: 200px;" />
              </el-form-item>

              <el-form-item label="上传时间">
                <el-select v-model="pendingFilters.timeRange" placeholder="选择时间范围" clearable style="width: 150px;" @change="handlePendingTimeRangeChange">
                  <el-option label="全部" :value="null" />
                  <el-option label="今天" value="today" />
                  <el-option label="昨天" value="yesterday" />
                  <el-option label="最近7天" value="week" />
                  <el-option label="最近30天" value="month" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>

              <el-form-item v-if="pendingFilters.timeRange === 'custom'" label="自定义日期">
                <el-date-picker
                  v-model="pendingFilters.customTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 300px;"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="handlePendingSearch">搜索</el-button>
                <el-button @click="resetPendingFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="table-actions">
            <div class="selection-actions" v-if="selectedResources.length > 0">
              <el-button type="success" @click="handleBatchApprove">
                批量通过 ({{ selectedResources.length }})
              </el-button>
              <el-button type="danger" @click="handleBatchReject">
                批量驳回 ({{ selectedResources.length }})
              </el-button>
            </div>

            <div class="refresh-action">
              <el-button type="primary" plain @click="handleRefresh">
                <el-icon><el-icon-refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </div>

          <el-table
            :data="resources"
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column prop="name" label="资料名称" min-width="200">
              <template #default="{ row }">
                <el-button type="text" @click="handlePreview(row)">
                  {{ row.name }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column prop="examType" label="考试类型" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getExamTypeTag(row.examType)"
                  :class="{ 'custom-purple': row.examType === 4 }"
                >
                  {{ getExamTypeText(row.examType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileType" label="文件类型" width="100">
              <template #default="{ row }">
                <el-tag type="info" size="small">
                  {{ getFileExtension(row.originalFilename) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileSize" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>

            <el-table-column prop="userId" label="上传用户" width="120">
              <template #default="{ row }">
                {{ row.username || `用户 #${row.userId}` }}
              </template>
            </el-table-column>

            <el-table-column prop="createTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="220" fixed="right">
              <template #default="{ row }">
                <el-button type="success" size="small" @click="handleApprove(row)">
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="handleReject(row)">
                  驳回
                </el-button>
                <el-button type="primary" size="small" @click="handlePreview(row)">
                  预览
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="total > 0"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
            class="pagination"
          />

          <el-empty v-if="resources.length === 0" description="暂无待审核资料" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="已通过资料" name="approved">
        <div class="resources-table" v-loading="loading">
          <div class="table-filters">
            <el-form :inline="true" :model="filters">
              <el-form-item label="考试类型">
                <el-select v-model="filters.examType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="考研" :value="0" />
                  <el-option label="考公" :value="1" />
                  <el-option label="法考" :value="2" />
                  <el-option label="教资" :value="3" />
                  <el-option label="其他" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="文件类型">
                <el-select v-model="filters.fileType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="PDF" value="PDF" />
                  <el-option label="DOC" value="DOC" />
                  <el-option label="DOCX" value="DOCX" />
                  <el-option label="PPT" value="PPT" />
                  <el-option label="PPTX" value="PPTX" />
                  <el-option label="XLS" value="XLS" />
                  <el-option label="XLSX" value="XLSX" />
                  <el-option label="TXT" value="TXT" />
                  <el-option label="图片" value="IMAGE" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
              </el-form-item>

              <el-form-item label="上传用户">
                <el-input v-model="filters.username" placeholder="用户名" style="width: 150px;" />
              </el-form-item>

              <el-form-item label="关键词">
                <el-input v-model="filters.keyword" placeholder="资料名称" style="width: 200px;" />
              </el-form-item>

              <el-form-item label="时间筛选">
                <el-select v-model="filters.timeType" placeholder="时间类型" style="width: 120px;">
                  <el-option label="上传时间" value="upload" />
                  <el-option label="审核时间" value="audit" />
                </el-select>
              </el-form-item>

              <el-form-item label="时间范围">
                <el-select v-model="filters.timeRange" placeholder="选择时间范围" clearable style="width: 150px;" @change="handleTimeRangeChange">
                  <el-option label="全部" :value="null" />
                  <el-option label="今天" value="today" />
                  <el-option label="昨天" value="yesterday" />
                  <el-option label="最近7天" value="week" />
                  <el-option label="最近30天" value="month" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>

              <el-form-item v-if="filters.timeRange === 'custom'" label="自定义日期">
                <el-date-picker
                  v-model="filters.customTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 300px;"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="resources" style="width: 100%">
            <el-table-column prop="name" label="资料名称" min-width="200">
              <template #default="{ row }">
                <router-link
                  :to="{ name: 'ResourceDetail', params: { id: row.id } }"
                  class="resource-link"
                >
                  {{ row.name }}
                </router-link>
              </template>
            </el-table-column>

            <el-table-column prop="examType" label="考试类型" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getExamTypeTag(row.examType)"
                  :class="{ 'custom-purple': row.examType === 4 }"
                >
                  {{ getExamTypeText(row.examType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileType" label="文件类型" width="100">
              <template #default="{ row }">
                <el-tag type="info" size="small">
                  {{ getFileExtension(row.originalFilename) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileSize" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>

            <el-table-column prop="userId" label="上传用户" width="120">
              <template #default="{ row }">
                {{ row.username || `用户 #${row.userId}` }}
              </template>
            </el-table-column>

            <el-table-column prop="createTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="downloadCount" label="下载量" width="100" />

            <el-table-column prop="commentCount" label="评论数" width="100" />

            <el-table-column prop="auditTime" label="审核时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.auditTime) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="120" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handlePreview(row)">
                  预览
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="total > 0"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
            class="pagination"
          />

          <el-empty v-if="resources.length === 0" description="暂无已通过资料" />
        </div>
      </el-tab-pane>

      <el-tab-pane label="已驳回资料" name="rejected">
        <div class="resources-table" v-loading="loading">
          <div class="table-filters">
            <el-form :inline="true" :model="rejectedFilters">
              <el-form-item label="考试类型">
                <el-select v-model="rejectedFilters.examType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="考研" :value="0" />
                  <el-option label="考公" :value="1" />
                  <el-option label="法考" :value="2" />
                  <el-option label="教资" :value="3" />
                  <el-option label="其他" :value="4" />
                </el-select>
              </el-form-item>

              <el-form-item label="文件类型">
                <el-select v-model="rejectedFilters.fileType" placeholder="全部" clearable style="width: 120px;">
                  <el-option label="全部" :value="null" />
                  <el-option label="PDF" value="PDF" />
                  <el-option label="DOC" value="DOC" />
                  <el-option label="DOCX" value="DOCX" />
                  <el-option label="PPT" value="PPT" />
                  <el-option label="PPTX" value="PPTX" />
                  <el-option label="XLS" value="XLS" />
                  <el-option label="XLSX" value="XLSX" />
                  <el-option label="TXT" value="TXT" />
                  <el-option label="图片" value="IMAGE" />
                  <el-option label="其他" value="OTHER" />
                </el-select>
              </el-form-item>

              <el-form-item label="上传用户">
                <el-input v-model="rejectedFilters.username" placeholder="用户名" style="width: 150px;" />
              </el-form-item>

              <el-form-item label="关键词">
                <el-input v-model="rejectedFilters.keyword" placeholder="资料名称" style="width: 200px;" />
              </el-form-item>

              <el-form-item label="时间筛选">
                <el-select v-model="rejectedFilters.timeType" placeholder="时间类型" style="width: 120px;">
                  <el-option label="上传时间" value="upload" />
                  <el-option label="审核时间" value="audit" />
                </el-select>
              </el-form-item>

              <el-form-item label="时间范围">
                <el-select v-model="rejectedFilters.timeRange" placeholder="选择时间范围" clearable style="width: 150px;" @change="handleRejectedTimeRangeChange">
                  <el-option label="全部" :value="null" />
                  <el-option label="今天" value="today" />
                  <el-option label="昨天" value="yesterday" />
                  <el-option label="最近7天" value="week" />
                  <el-option label="最近30天" value="month" />
                  <el-option label="自定义" value="custom" />
                </el-select>
              </el-form-item>

              <el-form-item v-if="rejectedFilters.timeRange === 'custom'" label="自定义日期">
                <el-date-picker
                  v-model="rejectedFilters.customTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 300px;"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="handleRejectedSearch">搜索</el-button>
                <el-button @click="resetRejectedFilters">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="resources" style="width: 100%">
            <el-table-column prop="name" label="资料名称" min-width="200">
              <template #default="{ row }">
                <el-button type="text" @click="handlePreview(row)">
                  {{ row.name }}
                </el-button>
              </template>
            </el-table-column>

            <el-table-column prop="examType" label="考试类型" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getExamTypeTag(row.examType)"
                  :class="{ 'custom-purple': row.examType === 4 }"
                >
                  {{ getExamTypeText(row.examType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileType" label="文件类型" width="100">
              <template #default="{ row }">
                <el-tag type="info" size="small">
                  {{ getFileExtension(row.originalFilename) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="fileSize" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>

            <el-table-column prop="userId" label="上传用户" width="120">
              <template #default="{ row }">
                {{ row.username || `用户 #${row.userId}` }}
              </template>
            </el-table-column>

            <el-table-column prop="createTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="auditTime" label="审核时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.auditTime) }}
              </template>
            </el-table-column>

            <el-table-column prop="rejectReason" label="驳回原因" min-width="200">
              <template #default="{ row }">
                <el-tooltip
                  v-if="row.rejectReason"
                  :content="row.rejectReason"
                  placement="top"
                  effect="dark"
                >
                  <span class="reject-reason">
                    {{ row.rejectReason.length > 20 ? row.rejectReason.substring(0, 20) + '...' : row.rejectReason }}
                  </span>
                </el-tooltip>
                <span v-else class="no-reason">未提供原因</span>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handlePreview(row)">
                  预览
                </el-button>
                <el-button type="success" size="small" @click="handleReApprove(row)">
                  重新通过
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="total > 0"
            background
            layout="prev, pager, next"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
            class="pagination"
          />

          <el-empty v-if="resources.length === 0" description="暂无已驳回资料" />
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- Reject Dialog -->
    <el-dialog v-model="rejectDialogVisible" title="驳回资料" width="500px">
      <el-form>
        <el-form-item label="驳回原因" required>
          <el-input
            v-model="rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入驳回原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="confirmReject">
          确认驳回
        </el-button>
      </template>
    </el-dialog>

    <!-- Batch Reject Dialog -->
    <el-dialog v-model="batchRejectDialogVisible" title="批量驳回资料" width="500px">
      <el-form>
        <el-form-item label="驳回原因" required>
          <el-input
            v-model="batchRejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入驳回原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchRejectDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="confirmBatchReject">
          确认驳回
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getPendingResources,
  getApprovedResources,
  getRejectedResources,
  approveResource,
  rejectResource,
  batchApproveResources,
  batchRejectResources
} from '../../api/admin'
import { getViewUrl } from '../../api/resource'
import { formatDate } from '../../utils/auth'
import { useUserStore } from '../../store/user'

// Data
const activeTab = ref('pending')
const resources = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const selectedResources = ref([])

// User store
const userStore = useUserStore()

// Filters for approved resources
const filters = reactive({
  examType: null,
  fileType: null,
  username: '',
  keyword: '',
  timeType: 'upload', // 'upload' or 'audit'
  timeRange: null,
  customTimeRange: null
})

// Filters for pending resources
const pendingFilters = reactive({
  examType: null,
  fileType: null,
  username: '',
  keyword: '',
  timeRange: null,
  customTimeRange: null
})

// Filters for rejected resources
const rejectedFilters = reactive({
  examType: null,
  fileType: null,
  username: '',
  keyword: '',
  timeType: 'upload', // 'upload' or 'audit'
  timeRange: null,
  customTimeRange: null
})

// Reject dialog
const rejectDialogVisible = ref(false)
const rejectReason = ref('')
const currentResource = ref(null)

// Batch reject dialog
const batchRejectDialogVisible = ref(false)
const batchRejectReason = ref('')

// Submitting state
const submitting = ref(false)

// Fetch resources
const fetchResources = async () => {
  loading.value = true

  try {
    let response

    if (activeTab.value === 'pending') {
      // 获取待审核资料
      const timeParams = getTimeRangeParams(pendingFilters.timeRange, pendingFilters.customTimeRange, 'upload')
      const params = {
        page: currentPage.value,
        size: pageSize.value,
        examType: pendingFilters.examType,
        fileType: pendingFilters.fileType,
        username: pendingFilters.username,
        keyword: pendingFilters.keyword,
        ...timeParams
      }

      console.log('Pending resources params:', params)
      response = await getPendingResources(params)
    } else if (activeTab.value === 'approved') {
      // 获取已通过资料
      const timeParams = getTimeRangeParams(filters.timeRange, filters.customTimeRange, filters.timeType)
      const params = {
        page: currentPage.value,
        size: pageSize.value,
        examType: filters.examType,
        fileType: filters.fileType,
        username: filters.username,
        keyword: filters.keyword,
        ...timeParams
      }

      console.log('Approved resources params:', params)
      response = await getApprovedResources(params)
    } else if (activeTab.value === 'rejected') {
      // 获取已驳回资料
      const timeParams = getTimeRangeParams(rejectedFilters.timeRange, rejectedFilters.customTimeRange, rejectedFilters.timeType)
      const params = {
        page: currentPage.value,
        size: pageSize.value,
        examType: rejectedFilters.examType,
        fileType: rejectedFilters.fileType,
        username: rejectedFilters.username,
        keyword: rejectedFilters.keyword,
        ...timeParams
      }

      console.log('Rejected resources params:', params)
      response = await getRejectedResources(params)
    }

    resources.value = response.data.records || []
    total.value = response.data.total || 0
  } catch (error) {
    console.error('Failed to fetch resources:', error)
    ElMessage.error('获取资料列表失败')
    // 确保出错时清空数据
    resources.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// Handle tab click
const handleTabClick = (tab) => {
  // Manually update activeTab since v-model might not work properly
  activeTab.value = tab.props.name

  currentPage.value = 1
  selectedResources.value = []

  // 立即清空数据，避免显示上一个标签页的内容
  resources.value = []
  total.value = 0

  fetchResources()
}

// Handle refresh
const handleRefresh = () => {
  selectedResources.value = []

  // 立即清空数据，避免显示旧数据
  resources.value = []
  total.value = 0

  fetchResources()
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchResources()
}

// Handle selection change
const handleSelectionChange = (selection) => {
  selectedResources.value = selection
}

// Handle search
const handleSearch = () => {
  currentPage.value = 1
  fetchResources()
}

// Reset filters
const resetFilters = () => {
  filters.examType = null
  filters.fileType = null
  filters.username = ''
  filters.keyword = ''
  filters.timeType = 'upload'
  filters.timeRange = null
  filters.customTimeRange = null
  currentPage.value = 1
  fetchResources()
}

// Handle pending search
const handlePendingSearch = () => {
  currentPage.value = 1
  fetchResources()
}

// Reset pending filters
const resetPendingFilters = () => {
  pendingFilters.examType = null
  pendingFilters.fileType = null
  pendingFilters.username = ''
  pendingFilters.keyword = ''
  pendingFilters.timeRange = null
  pendingFilters.customTimeRange = null
  currentPage.value = 1
  fetchResources()
}

// Handle rejected search
const handleRejectedSearch = () => {
  currentPage.value = 1
  fetchResources()
}

// Reset rejected filters
const resetRejectedFilters = () => {
  rejectedFilters.examType = null
  rejectedFilters.fileType = null
  rejectedFilters.username = ''
  rejectedFilters.keyword = ''
  rejectedFilters.timeType = 'upload'
  rejectedFilters.timeRange = null
  rejectedFilters.customTimeRange = null
  currentPage.value = 1
  fetchResources()
}

// Get time range parameters
const getTimeRangeParams = (timeRange, customTimeRange, timeType) => {
  if (!timeRange) return {}

  const now = new Date()
  let startTime, endTime

  switch (timeRange) {
    case 'today':
      // 今天 00:00:00 到 23:59:59
      startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0)
      endTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999)
      break
    case 'yesterday':
      // 昨天 00:00:00 到 23:59:59
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
      startTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0)
      endTime = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59, 999)
      break
    case 'week':
      // 最近7天
      startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'month':
      // 最近30天
      startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      endTime = now
      break
    case 'custom':
      if (customTimeRange && Array.isArray(customTimeRange) && customTimeRange.length === 2) {
        // 将日期扩展为全天范围
        startTime = new Date(customTimeRange[0] + ' 00:00:00')
        endTime = new Date(customTimeRange[1] + ' 23:59:59')
        console.log('Custom date range:', customTimeRange, 'Expanded to:', { startTime, endTime })
      } else {
        console.log('Invalid custom date range:', customTimeRange)
        return {}
      }
      break
    default:
      return {}
  }

  if (!startTime || !endTime) return {}

  // 使用本地时间格式，避免时区问题
  const formatDateTime = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  const params = {}
  if (timeType === 'upload') {
    params.startTime = formatDateTime(startTime)
    params.endTime = formatDateTime(endTime)
  } else if (timeType === 'audit') {
    params.auditStartTime = formatDateTime(startTime)
    params.auditEndTime = formatDateTime(endTime)
  }

  // 添加调试信息
  console.log('Time range params:', {
    timeRange,
    timeType,
    startTime: formatDateTime(startTime),
    endTime: formatDateTime(endTime),
    params
  })

  return params
}

// Handle time range change for approved resources
const handleTimeRangeChange = (value) => {
  if (value !== 'custom') {
    filters.customTimeRange = null
  }
}

// Handle time range change for pending resources
const handlePendingTimeRangeChange = (value) => {
  console.log('Pending time range changed to:', value)
  if (value !== 'custom') {
    pendingFilters.customTimeRange = null
    console.log('Cleared custom date range')
  }
}

// Handle time range change for rejected resources
const handleRejectedTimeRangeChange = (value) => {
  console.log('Rejected time range changed to:', value)
  if (value !== 'custom') {
    rejectedFilters.customTimeRange = null
    console.log('Cleared rejected custom date range')
  }
}

// Watch for custom date range changes
watch(() => pendingFilters.customTimeRange, (newVal) => {
  console.log('Pending custom date range changed:', newVal)
})

watch(() => filters.customTimeRange, (newVal) => {
  console.log('Approved custom date range changed:', newVal)
})

watch(() => rejectedFilters.customTimeRange, (newVal) => {
  console.log('Rejected custom date range changed:', newVal)
})

// Get exam type text
const getExamTypeText = (examType) => {
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return examTypes[examType] || '未知'
}

// Get exam type tag type
const getExamTypeTag = (examType) => {
  const tagTypes = ['primary', 'success', 'warning', 'danger', '']
  return tagTypes[examType] || ''
}

// Get file extension from filename
const getFileExtension = (filename) => {
  if (!filename) return '未知'
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1) return '无扩展名'
  return filename.substring(lastDotIndex + 1).toUpperCase()
}

// Format file size to human readable format
const formatFileSize = (fileSize) => {
  if (!fileSize || fileSize === 0) return '未知'

  const units = ['B', 'KB', 'MB', 'GB']
  let size = fileSize
  let unitIndex = 0

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`
}

// Handle approve
const handleApprove = async (resource) => {
  try {
    await ElMessageBox.confirm(
      `确定要通过资料 '${resource.name}' 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await approveResource(resource.id)
    ElMessage.success('资料已通过审核')

    // Refresh resources
    fetchResources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to approve resource:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Handle reject
const handleReject = (resource) => {
  currentResource.value = resource
  rejectReason.value = ''
  rejectDialogVisible.value = true
}

// Confirm reject
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  submitting.value = true
  try {
    await rejectResource(currentResource.value.id, rejectReason.value)
    ElMessage.success('资料已驳回')
    rejectDialogVisible.value = false

    // Refresh resources
    fetchResources()
  } catch (error) {
    console.error('Failed to reject resource:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// Handle batch approve
const handleBatchApprove = async () => {
  if (selectedResources.value.length === 0) {
    ElMessage.warning('请选择要批量通过的资料')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量通过选中的 ${selectedResources.value.length} 个资料吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const resourceIds = selectedResources.value.map(item => item.id)
    await batchApproveResources(resourceIds)
    ElMessage.success('批量通过操作成功')

    // Refresh resources
    fetchResources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch approve resources:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Handle batch reject
const handleBatchReject = () => {
  if (selectedResources.value.length === 0) {
    ElMessage.warning('请选择要批量驳回的资料')
    return
  }

  batchRejectReason.value = ''
  batchRejectDialogVisible.value = true
}

// Confirm batch reject
const confirmBatchReject = async () => {
  if (!batchRejectReason.value.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  submitting.value = true
  try {
    const resourceIds = selectedResources.value.map(item => item.id)
    await batchRejectResources(resourceIds, batchRejectReason.value)
    ElMessage.success('批量驳回操作成功')
    batchRejectDialogVisible.value = false

    // Refresh resources
    fetchResources()
  } catch (error) {
    console.error('Failed to batch reject resources:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// Handle re-approve (for rejected resources)
const handleReApprove = async (resource) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新通过资料 '${resource.name}' 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await approveResource(resource.id)
    ElMessage.success('资料已重新通过审核')

    // Refresh resources
    fetchResources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to re-approve resource:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Handle preview
const handlePreview = (resource) => {
  // Use admin preview URL (no authentication required)
  const adminPreviewUrl = `/api/admin/resources/preview/${resource.id}`

  // Open in new window
  window.open(adminPreviewUrl, '_blank')
}

// Fetch data on component mount
onMounted(() => {
  fetchResources()
})
</script>

<style lang="scss" scoped>
.admin-resources {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .resources-table {
    margin-top: 20px;

    .table-actions {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }

    .table-filters {
      margin-bottom: 15px;
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .resource-link {
      color: #409eff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  // 自定义紫色标签样式 - 用于"其他"类型，模仿Element Plus的设计
  :deep(.el-tag) {
    &.custom-purple {
      background-color: #f4f0ff;
      border: 1px solid #d4c5f9;
      color: #7c3aed;

      // 保持与Element Plus标签一致的样式
      border-radius: 4px;
      font-size: 12px;
      line-height: 1;
      white-space: nowrap;

      // 悬停效果
      &:hover {
        background-color: #ede9fe;
        border-color: #c4b5fd;
      }
    }
  }

  // 驳回原因样式
  .reject-reason {
    color: #e74c3c;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  .no-reason {
    color: #999;
    font-style: italic;
  }
}
</style>
