# 生产环境配置
spring:
  # 数据库配置 - 生产环境
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${DB_URL:*****************************************************************************************************************}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    hikari:
      # 生产环境连接池配置（较大）
      minimum-idle: ${DB_MIN_IDLE:20}
      maximum-pool-size: ${DB_MAX_POOL_SIZE:50}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
      validation-timeout: 5000
      leak-detection-threshold: 60000
      pool-name: GongxingxueHikariPool-Prod

  # 邮件配置 - 生产环境
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true

# 文件存储配置 - 生产环境
file:
  upload-dir: ${FILE_UPLOAD_DIR:/app/uploads}

# JWT配置 - 生产环境
jwt:
  secret: ${JWT_SECRET}  # 必须通过环境变量提供
  expiration: ${JWT_EXPIRATION:86400000}

# Swagger配置 - 生产环境强制禁用
swagger:
  enabled: false
  title: Gongxingxue API - 生产环境
  description: API Documentation for Exam Preparation Resource Sharing Platform (Production)
  version: 1.0.0
  contact:
    name: Gongxingxue Team
    email: ${CONTACT_EMAIL:<EMAIL>}

# 日志配置 - 生产环境
logging:
  level:
    com.gongxingxue: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:/app/logs/application.log}
    max-size: 100MB
    max-history: 30

# 服务器配置 - 生产环境
server:
  port: ${SERVER_PORT:8081}
  servlet:
    context-path: /api
  # 生产环境安全配置
  error:
    include-message: never
    include-binding-errors: never
    include-stacktrace: never
    include-exception: false

# CORS配置 - 生产环境
cors:
  allowed:
    origins: ${CORS_ALLOWED_ORIGINS:http://************,https://************}

# 应用配置 - 生产环境
app:
  frontend:
    url: ${FRONTEND_URL:http://************}
