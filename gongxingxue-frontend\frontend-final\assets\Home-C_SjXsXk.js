import{r as s,w as F,o as P,b as _,d as r,q as H,e as a,f as t,g as u,s as S,h as T,F as k,v as U,k as I,l as i,m as d}from"./index-BCDD51NH.js";import{g as L}from"./resource-BWs-v3qB.js";import{R as M}from"./ResourceCard-CaduVvWq.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./FavoriteButton-BoyGPNBM.js";const j={class:"home-page"},q={class:"filter-section"},A={class:"exam-types"},G={class:"sort-section"},J={class:"resources-list"},K={class:"pagination"},O={__name:"Home",setup(Q){const y=I(),g=s([]),f=s(!1),C=s(0),c=s(1),x=s(10),p=s(null),b=s("createTime"),m=async()=>{f.value=!0;try{const l={page:c.value,size:x.value,sortType:b.value};p.value!==null&&(l.examType=p.value);const e=await L(l);g.value=e.data.records,C.value=e.data.total}catch(l){console.error("Failed to fetch resources:",l)}finally{f.value=!1}},V=()=>{c.value=1,m()},h=()=>{c.value=1,m()},w=l=>{c.value=l,m()},R=l=>{y.push({name:"ResourceDetail",params:{id:l}})};return F(()=>y.currentRoute.value.query,l=>{l.examType!==void 0&&(p.value=l.examType!==null?Number(l.examType):null),m()},{immediate:!0}),P(()=>{m()}),(l,e)=>{const n=u("el-radio-button"),z=u("el-radio-group"),v=u("el-option"),B=u("el-select"),N=u("el-pagination"),D=u("el-empty"),E=S("loading");return i(),_("div",j,[r("div",q,[r("div",A,[e[8]||(e[8]=r("span",{class:"filter-label"},"考试类型：",-1)),a(z,{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=o=>p.value=o),onChange:V},{default:t(()=>[a(n,{label:null},{default:t(()=>e[2]||(e[2]=[d("全部")])),_:1,__:[2]}),a(n,{label:0},{default:t(()=>e[3]||(e[3]=[d("考研")])),_:1,__:[3]}),a(n,{label:1},{default:t(()=>e[4]||(e[4]=[d("考公")])),_:1,__:[4]}),a(n,{label:2},{default:t(()=>e[5]||(e[5]=[d("法考")])),_:1,__:[5]}),a(n,{label:3},{default:t(()=>e[6]||(e[6]=[d("教资")])),_:1,__:[6]}),a(n,{label:4},{default:t(()=>e[7]||(e[7]=[d("其他")])),_:1,__:[7]})]),_:1},8,["modelValue"])]),r("div",G,[e[9]||(e[9]=r("span",{class:"filter-label"},"排序：",-1)),a(B,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=o=>b.value=o),onChange:h,placeholder:"选择排序方式",style:{width:"140px"}},{default:t(()=>[a(v,{label:"最新上传",value:"createTime"}),a(v,{label:"下载最多",value:"downloadCount"}),a(v,{label:"收藏最多",value:"favoriteCount"}),a(v,{label:"评论最多",value:"commentCount"}),a(v,{label:"综合热度",value:"relevance"})]),_:1},8,["modelValue"])])]),H((i(),_("div",J,[g.value.length>0?(i(),_(k,{key:0},[(i(!0),_(k,null,U(g.value,o=>(i(),T(M,{key:o.id,resource:o,onClick:W=>R(o.id)},null,8,["resource","onClick"]))),128)),r("div",K,[a(N,{background:"",layout:"prev, pager, next",total:C.value,"page-size":x.value,"current-page":c.value,onCurrentChange:w},null,8,["total","page-size","current-page"])])],64)):(i(),T(D,{key:1,description:"暂无资料"}))])),[[E,f.value]])])}}},le=$(O,[["__scopeId","data-v-5899ba6c"]]);export{le as default};
