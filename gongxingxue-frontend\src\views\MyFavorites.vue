<template>
  <div class="my-favorites">
    <div class="page-header">
      <h2>我的收藏</h2>
      <p class="subtitle">管理您收藏的学习资料</p>
    </div>

    <div class="favorites-content" v-loading="loading">
      <template v-if="favorites.length > 0">
        <div class="favorites-grid">
          <div
            v-for="resource in favorites"
            :key="resource.id"
            class="favorite-card"
          >
            <el-card shadow="hover" class="resource-card">
              <!-- 资料信息 -->
              <div class="resource-info" @click="viewResource(resource.id)">
                <div class="resource-header">
                  <h3 class="resource-title">{{ resource.name }}</h3>
                  <div class="resource-tags">
                    <el-tag
                      :type="getExamTypeColor(resource.examType)"
                      size="small"
                    >
                      {{ getExamTypeName(resource.examType) }}
                    </el-tag>
                    <el-tag
                      type="info"
                      size="small"
                      class="file-type-tag"
                    >
                      {{ getFileTypeFromName(resource.originalFilename) }}
                    </el-tag>
                  </div>
                </div>

                <p class="resource-description" v-if="resource.description">
                  {{ resource.description }}
                </p>

                <div class="resource-meta">
                  <span class="uploader">{{ resource.username || '匿名用户' }}</span>
                  <span class="separator">•</span>
                  <span class="download-count">{{ resource.downloadCount || 0 }}下载</span>
                  <span class="separator">•</span>
                  <span class="comment-count">{{ resource.commentCount || 0 }}评论</span>
                  <span class="separator">•</span>
                  <span class="favorite-count">{{ resource.favoriteCount || 0 }}收藏</span>
                  <span class="separator">•</span>
                  <span class="upload-time">{{ formatDate(resource.createTime) }}</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  @click="viewResource(resource.id)"
                >
                  查看详情
                </el-button>
                <favorite-button
                  :resource-id="resource.id"
                  :initial-favorited="true"
                  :favorite-count="resource.favoriteCount || 0"
                  size="small"
                  @update:favorited="handleFavoriteUpdate(resource.id, $event)"
                />
              </div>
            </el-card>
          </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-wrapper">
          <el-pagination
            background
            layout="prev, pager, next, total"
            :total="total"
            :page-size="pageSize"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
      </template>

      <template v-else>
        <el-empty description="暂无收藏的资料">
          <el-button type="primary" @click="goToHome">
            去首页看看
          </el-button>
        </el-empty>
      </template>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import FavoriteButton from '../components/FavoriteButton.vue'
import { ElMessage } from 'element-plus'
import { formatDate } from '../utils/auth'
import { useFavorites } from '../composables/useFavorites'

const router = useRouter()

// Use global favorites state
const {
  favorites,
  loading,
  total,
  fetchFavorites: fetchFavoritesFromStore,
  onFavoriteChange
} = useFavorites()

// Local state
const currentPage = ref(1)
const pageSize = ref(12)

// Fetch favorites
const fetchFavorites = async (forceRefresh = false) => {
  try {
    // 如果是强制刷新或者收藏列表为空，则重新获取数据
    if (forceRefresh || favorites.length === 0) {
      await fetchFavoritesFromStore(currentPage.value, pageSize.value)
    }
  } catch (error) {
    console.error('Fetch favorites error:', error)
    ElMessage.error('获取收藏列表失败')
  }
}

// Handle page change
const handlePageChange = (page) => {
  currentPage.value = page
  fetchFavorites(true) // 强制刷新
}

// Handle favorite update (remove from list if unfavorited)
const handleFavoriteUpdate = (resourceId, isFavorited) => {
  if (!isFavorited) {
    // The global state will handle the removal automatically
    // Just check if we need to go to previous page
    setTimeout(() => {
      if (favorites.length === 0 && currentPage.value > 1) {
        currentPage.value--
        fetchFavorites()
      }
    }, 100) // Small delay to let the global state update
  }
}

// View resource details
const viewResource = (id) => {
  router.push({ name: 'ResourceDetail', params: { id } })
}

// Go to home
const goToHome = () => {
  router.push({ name: 'Home' })
}

// Utility functions
const getExamTypeName = (type) => {
  const types = {
    0: '考研',
    1: '考公',
    2: '法考',
    3: '教资',
    4: '其他'
  }
  return types[type] || '其他'
}

const getExamTypeColor = (type) => {
  const colors = {
    0: 'danger',    // 考研 - 红色
    1: 'warning',   // 考公 - 橙色
    2: 'success',   // 法考 - 绿色
    3: 'primary',   // 教资 - 蓝色
    4: ''           // 其他 - 默认色
  }
  return colors[type] || ''
}

const getFileTypeFromName = (filename) => {
  if (!filename) return 'Unknown'
  const ext = filename.split('.').pop()?.toUpperCase()
  return ext || 'Unknown'
}

// Lifecycle
onMounted(() => {
  fetchFavorites(true) // 强制刷新
})

// 页面激活时重新获取数据（处理页面刷新的情况）
onActivated(() => {
  fetchFavorites(true) // 强制刷新
})
</script>

<style lang="scss" scoped>
.my-favorites {
  .page-header {
    margin-bottom: 30px;
    text-align: center;

    h2 {
      font-size: 28px;
      color: #303133;
      margin-bottom: 10px;
    }

    .subtitle {
      color: #606266;
      font-size: 16px;
      margin: 0;
    }
  }

  .favorites-content {
    min-height: 400px;

    .favorites-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      margin-bottom: 30px;

      .favorite-card {
        .resource-card {
          height: 100%;
          display: flex;
          flex-direction: column;

          .resource-info {
            flex: 1;
            cursor: pointer;

            &:hover {
              .resource-title {
                color: #409eff;
              }
            }

            .resource-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 10px;

              .resource-title {
                font-size: 16px;
                font-weight: 600;
                color: #303133;
                margin: 0;
                flex: 1;
                margin-right: 10px;
                line-height: 1.4;
                transition: color 0.2s ease;
              }

              .resource-tags {
                display: flex;
                gap: 5px;
                flex-shrink: 0;

                .file-type-tag {
                  margin-left: 5px;
                }
              }
            }

            .resource-description {
              color: #606266;
              font-size: 14px;
              margin: 10px 0;
              line-height: 1.5;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            .resource-meta {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
              color: #909399;
              margin-top: 15px;

              .separator {
                color: #dcdfe6;
              }

              .uploader {
                color: #909399;
              }

              .download-count,
              .comment-count,
              .favorite-count {
                color: #909399;
              }

              .upload-time {
                color: #c0c4cc;
              }
            }
          }

          .card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #f0f0f0;
          }
        }
      }
    }

    .pagination-wrapper {
      display: flex;
      justify-content: center;
      margin-top: 30px;
    }
  }
}

@media (max-width: 768px) {
  .my-favorites {
    .favorites-grid {
      grid-template-columns: 1fr;
      gap: 15px;

      .card-actions {
        flex-direction: column;
        gap: 8px;

        .el-button {
          width: 100%;
        }
      }
    }
  }
}
</style>
