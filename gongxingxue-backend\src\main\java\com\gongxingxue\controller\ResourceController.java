package com.gongxingxue.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gongxingxue.common.Result;
import com.gongxingxue.service.FileStorageService;
import com.gongxingxue.service.ResourceService;
import com.gongxingxue.util.JwtUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * Resource controller
 */
@Slf4j
@RestController
@RequestMapping("/resources")
@RequiredArgsConstructor
@Api(tags = "Resource API")
public class ResourceController {

    private final ResourceService resourceService;
    private final FileStorageService fileStorageService;
    private final JwtUtil jwtUtil;

    /**
     * 上传学习资料
     */
    @PostMapping("/upload")
    @ApiOperation(value = "上传学习资料", notes = "上传学习资料文件，支持PDF、Word、Excel、PPT等格式，文件大小限制30MB")
    @ApiResponses({
        @ApiResponse(code = 200, message = "上传成功，返回资料信息"),
        @ApiResponse(code = 400, message = "参数错误或文件格式不支持"),
        @ApiResponse(code = 401, message = "用户未登录")
    })
    public Result<com.gongxingxue.entity.Resource> uploadResource(
            @ApiParam(value = "资料名称", required = true, example = "高等数学复习资料")
            @RequestParam @NotBlank @Size(max = 50) String name,

            @ApiParam(value = "资料描述", required = false, example = "包含重点知识点和练习题")
            @RequestParam(required = false) @Size(max = 200) String description,

            @ApiParam(value = "考试类型（1=考研数学,2=考研英语,3=考研政治,4=考研专业课,5=公务员考试,6=其他）",
                     required = true, example = "1", allowableValues = "1,2,3,4,5,6")
            @RequestParam @NotNull Integer examType,

            @ApiParam(value = "资料文件", required = true)
            @RequestParam("file") MultipartFile file,

            HttpServletRequest request) {

        log.info("收到文件上传请求：名称={}, 描述={}, 考试类型={}, 文件名={}, 文件大小={}字节, 文件类型={}",
                name, description != null ? description : "无", examType,
                file.getOriginalFilename(), file.getSize(), file.getContentType());

        try {
            // Validate file security
            if (!validateFile(file)) {
                return Result.error("文件类型不支持或文件已损坏，请检查文件格式");
            }

            // Get user ID from request attributes (set by auth interceptor)
            Long userId = (Long) request.getAttribute("userId");

            // Fallback: extract user ID directly from JWT token if not set by interceptor
            if (userId == null) {
                String authHeader = request.getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    String token = authHeader.substring(7);
                    try {
                        userId = jwtUtil.getUserIdFromToken(token);
                    } catch (Exception e) {
                        return Result.error("认证失败：无法解析用户令牌");
                    }
                }

                if (userId == null) {
                    return Result.error("认证失败：无法获取用户信息");
                }
            }

            com.gongxingxue.entity.Resource resource = resourceService.uploadResource(name, description, examType, file, userId);
            return Result.success(resource);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取公开资料列表
     */
    @GetMapping("/public")
    @ApiOperation(value = "获取公开资料列表", notes = "获取已审核通过的公开学习资料，支持多种筛选和排序条件")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回分页的资料列表")
    })
    public Result<Page<com.gongxingxue.entity.Resource>> getPublicResources(
            @ApiParam(value = "考试类型筛选", required = false, example = "1")
            @RequestParam(required = false) Integer examType,

            @ApiParam(value = "关键词搜索", required = false, example = "数学")
            @RequestParam(required = false) String keyword,

            @ApiParam(value = "排序方式", required = false, example = "newest",
                     allowableValues = "newest,popular,favorites")
            @RequestParam(required = false) String sortType,

            @ApiParam(value = "文件类型筛选", required = false, example = "pdf")
            @RequestParam(required = false) String fileType,

            @ApiParam(value = "时间范围筛选", required = false, example = "week")
            @RequestParam(required = false) String timeRange,

            @ApiParam(value = "下载量范围筛选", required = false, example = "100+")
            @RequestParam(required = false) String downloadRange,

            @ApiParam(value = "页码", required = false, example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @ApiParam(value = "每页数量", required = false, example = "10")
            @RequestParam(defaultValue = "10") Integer size) {

        Page<com.gongxingxue.entity.Resource> resources = resourceService.getResourceListWithAdvancedFilters(
            examType, keyword, sortType, fileType, timeRange, downloadRange, page, size);

        return Result.success(resources);
    }

    /**
     * Get resource by ID (public access for viewing, authentication optional)
     *
     * 🎯 统一的资料详情接口：
     * - 所有用户都可以查看资料基本信息
     * - 登录用户可以获得额外信息（如是否已收藏）
     * - 下载等操作通过专门的接口处理
     */
    @GetMapping("/{id}")
    @ApiOperation("Get resource by ID")
    public Result<com.gongxingxue.entity.Resource> getResourceById(
            @PathVariable Long id,
            HttpServletRequest request) {

        com.gongxingxue.entity.Resource resource = resourceService.getResourceById(id);

        if (resource != null && resource.getAuditStatus() == 1) {
            // 获取当前用户ID（可能为null，表示未登录）
            Long userId = (Long) request.getAttribute("userId");

            // 如果用户已登录，可以在这里添加额外信息
            // 比如：是否已收藏、是否已下载等
            if (userId != null) {
                // 这里可以添加登录用户的额外信息
                log.debug("已登录用户 {} 查看资料 {}", userId, id);
            } else {
                log.debug("匿名用户查看资料 {}", id);
            }

            return Result.success(resource);
        } else {
            return Result.error("资料不存在或未通过审核");
        }
    }

    /**
     * Get my resources
     */
    @GetMapping("/my")
    @ApiOperation("Get my resources")
    public Result<List<com.gongxingxue.entity.Resource>> getMyResources(HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        List<com.gongxingxue.entity.Resource> resources = resourceService.getResourcesByUserId(userId);

        return Result.success(resources);
    }

    /**
     * Delete resource
     */
    @DeleteMapping("/{id}")
    @ApiOperation("Delete resource")
    public Result<Void> deleteResource(@PathVariable Long id, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        boolean success = resourceService.deleteResource(id, userId);

        if (success) {
            return Result.success("资料删除成功", null);
        } else {
            return Result.error("删除失败，资料不存在或无权限删除");
        }
    }

    /**
     * Download resource
     */
    @GetMapping("/download/{id}")
    @ApiOperation("Download resource")
    public ResponseEntity<org.springframework.core.io.Resource> downloadResource(
            @PathVariable Long id,
            HttpServletRequest request) {

        try {
            // Get resource
            com.gongxingxue.entity.Resource resource = resourceService.getResourceById(id);
            if (resource == null || resource.getAuditStatus() != 1) {
                return ResponseEntity.notFound().build();
            }

            // Get user ID (must be authenticated)
            Long userId = (Long) request.getAttribute("userId");
            if (userId == null) {
                return ResponseEntity.status(401).build();
            }

            // Record download
            String ip = request.getRemoteAddr();
            String userAgent = request.getHeader("User-Agent");
            resourceService.downloadResource(id, userId, ip, userAgent);

            // Load file as resource
            org.springframework.core.io.Resource fileResource = fileStorageService.loadFileAsResource(resource.getFilePath());

            // Determine content type based on file extension
            String contentType = determineContentType(resource.getOriginalFilename());
            log.debug("确定文件Content-Type: {}", contentType);

            // Encode filename for proper handling of Chinese characters
            String encodedFilename = encodeFilename(resource.getOriginalFilename(), request);
            log.debug("编码后的文件名头: {}", encodedFilename);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; " + encodedFilename)
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .body(fileResource);
        } catch (Exception ex) {
            log.error("文件下载失败: {}", ex.getMessage(), ex);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * View resource
     */
    @GetMapping("/view/{id}")
    @ApiOperation("View resource")
    public ResponseEntity<org.springframework.core.io.Resource> viewResource(
            @PathVariable Long id,
            HttpServletRequest request) {

        try {
            // Get resource
            com.gongxingxue.entity.Resource resource = resourceService.getResourceById(id);
            if (resource == null || resource.getAuditStatus() != 1) {
                return ResponseEntity.notFound().build();
            }

            // Check if file can be previewed in browser
            String filename = resource.getOriginalFilename();
            if (!canPreviewInBrowser(filename)) {
                // If file cannot be previewed, redirect to download
                // 使用相对路径，避免硬编码，适应不同部署环境
                String downloadPath = request.getContextPath() + "/resources/download/" + id;
                return ResponseEntity.status(302)
                        .header(HttpHeaders.LOCATION, downloadPath)
                        .build();
            }

            // Load file as resource
            org.springframework.core.io.Resource fileResource = fileStorageService.loadFileAsResource(resource.getFilePath());

            // Determine content type for preview
            String contentType = determinePreviewContentType(filename);
            log.debug("预览文件Content-Type: {}", contentType);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline")
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(fileResource);
        } catch (Exception ex) {
            log.error("文件预览失败: {}", ex.getMessage(), ex);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Determine content type based on file extension
     */
    private String determineContentType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        Map<String, String> contentTypes = new HashMap<>();
        // Office documents
        contentTypes.put("doc", "application/msword");
        contentTypes.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        contentTypes.put("xls", "application/vnd.ms-excel");
        contentTypes.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        contentTypes.put("ppt", "application/vnd.ms-powerpoint");
        contentTypes.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");

        // PDF
        contentTypes.put("pdf", "application/pdf");

        // Text files
        contentTypes.put("txt", "text/plain; charset=utf-8");

        // Images
        contentTypes.put("jpg", "image/jpeg");
        contentTypes.put("jpeg", "image/jpeg");
        contentTypes.put("png", "image/png");
        contentTypes.put("gif", "image/gif");

        // Archives
        contentTypes.put("zip", "application/zip");
        contentTypes.put("rar", "application/x-rar-compressed");
        contentTypes.put("7z", "application/x-7z-compressed");

        // Audio/Video
        contentTypes.put("mp3", "audio/mpeg");
        contentTypes.put("mp4", "video/mp4");

        return contentTypes.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * Check if file can be previewed in browser
     */
    private boolean canPreviewInBrowser(String filename) {
        if (filename == null) {
            return false;
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        // Files that can be previewed in browser
        return extension.equals("pdf") ||
               extension.equals("txt") ||
               extension.equals("jpg") ||
               extension.equals("jpeg") ||
               extension.equals("png") ||
               extension.equals("gif") ||
               extension.equals("mp4") ||
               extension.equals("mp3") ||
               extension.equals("avi") ||
               extension.equals("mov") ||
               extension.equals("wmv") ||
               extension.equals("flv");
    }

    /**
     * Determine content type for preview (optimized for browser display)
     */
    private String determinePreviewContentType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        Map<String, String> previewContentTypes = new HashMap<>();

        // PDF - can be previewed in browser
        previewContentTypes.put("pdf", "application/pdf");

        // Text files - can be previewed in browser
        previewContentTypes.put("txt", "text/plain; charset=utf-8");

        // Images - can be previewed in browser
        previewContentTypes.put("jpg", "image/jpeg");
        previewContentTypes.put("jpeg", "image/jpeg");
        previewContentTypes.put("png", "image/png");
        previewContentTypes.put("gif", "image/gif");

        // Audio/Video - can be previewed in browser
        previewContentTypes.put("mp3", "audio/mpeg");
        previewContentTypes.put("mp4", "video/mp4");

        return previewContentTypes.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * Encode filename for proper handling of Chinese characters
     */
    private String encodeFilename(String filename, HttpServletRequest request) {
        if (filename == null || filename.trim().isEmpty()) {
            return "filename=\"download\"";
        }

        String userAgent = request.getHeader("User-Agent");
        log.debug("原始文件名: {}, User-Agent: {}", filename, userAgent);

        try {
            String encodedFilename;

            if (userAgent != null && userAgent.toLowerCase().contains("msie")) {
                // IE browser
                encodedFilename = "filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20") + "\"";
            } else if (userAgent != null && userAgent.toLowerCase().contains("firefox")) {
                // Firefox browser
                encodedFilename = "filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
            } else {
                // Chrome, Safari and other modern browsers
                // Use both filename and filename* for maximum compatibility
                String asciiFilename = filename.replaceAll("[^\\x00-\\x7F]", "_"); // Replace non-ASCII with underscore
                encodedFilename = "filename=\"" + asciiFilename + "\"; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
            }

            log.debug("编码后的文件名: {}", encodedFilename);
            return encodedFilename;

        } catch (UnsupportedEncodingException e) {
            log.error("文件名编码失败: {}", e.getMessage(), e);
            return "filename=\"" + filename + "\"";
        }
    }

    /**
     * 验证上传文件的安全性
     *
     * 这是一个多层安全验证机制，用于防止恶意文件上传：
     * 1. 文件大小验证 - 防止大文件攻击
     * 2. MIME类型验证 - 检查HTTP请求中声明的文件类型
     * 3. 文件扩展名验证 - 阻止危险的可执行文件
     * 4. 文件头验证（魔数检查）- 检查文件的实际内容，防止文件类型伪造
     *
     * @param file 待验证的上传文件
     * @return true表示文件安全可以上传，false表示文件存在安全风险
     */
    private boolean validateFile(MultipartFile file) {
        // 基础检查：文件不能为空
        if (file == null || file.isEmpty()) {
            log.warn("文件验证失败：文件为空");
            return false;
        }

        // 第一层防护：文件大小检查（虽然Spring Boot已经处理，但这里再次确认）
        // 限制30MB是为了防止大文件攻击，避免服务器内存溢出
        if (file.getSize() > 30 * 1024 * 1024) { // 30MB
            log.warn("文件验证失败：文件大小超过30MB限制，实际大小：{} bytes", file.getSize());
            return false;
        }

        // 第二层防护：MIME类型验证
        // MIME类型是HTTP请求中声明的文件类型，虽然可以伪造，但是第一道防线
        String contentType = file.getContentType();
        if (contentType == null) {
            log.warn("文件验证失败：无法获取文件MIME类型");
            return false;
        }

        String[] allowedMimeTypes = {
            // Documents
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",

            // Text files
            "text/plain",

            // Images
            "image/jpeg",
            "image/png",
            "image/gif",

            // Archives
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed",

            // Audio/Video
            "audio/mpeg",
            "audio/mp3",
            "video/mp4",
            "video/avi",
            "video/quicktime",  // .mov
            "video/x-msvideo",  // .avi
            "video/x-ms-wmv",   // .wmv
            "video/x-flv"       // .flv
        };

        // 检查MIME类型是否在允许列表中
        if (!Arrays.asList(allowedMimeTypes).contains(contentType)) {
            log.warn("文件验证失败：不支持的MIME类型：{}", contentType);
            return false;
        }

        // 第三层防护：文件扩展名验证
        // 即使MIME类型正确，也要检查文件扩展名，防止恶意文件伪装
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            log.warn("文件验证失败：文件名为空");
            return false;
        }

        // 检查危险的文件扩展名
        // 这些扩展名的文件可能包含可执行代码，存在安全风险
        String[] dangerousExtensions = {
            ".exe",  // Windows可执行文件
            ".bat",  // Windows批处理文件
            ".cmd",  // Windows命令文件
            ".scr",  // Windows屏保文件（可执行）
            ".jar",  // Java可执行文件
            ".sh",   // Shell脚本文件
            ".ps1",  // PowerShell脚本文件
            ".vbs"   // VBScript脚本文件
        };

        String lowerFilename = originalFilename.toLowerCase();
        for (String ext : dangerousExtensions) {
            if (lowerFilename.endsWith(ext)) {
                log.warn("文件验证失败：检测到危险文件扩展名：{}", ext);
                return false;
            }
        }

        // 第四层防护：文件头验证（魔数检查）
        // 这是最重要的安全检查，通过读取文件的前几个字节来验证文件的真实类型
        // 即使攻击者修改了文件扩展名和MIME类型，也无法伪造文件头的魔数
        try {
            // 读取文件的字节内容（不会消耗输入流）
            byte[] fileBytes = file.getBytes();
            if (fileBytes.length < 4) {
                log.warn("文件验证失败：文件太小，只有 {} 字节，无法进行文件头验证", fileBytes.length);
                return false;
            }

            log.debug("开始进行文件头验证，MIME类型：{}", contentType);
            log.debug("文件头字节：{:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X} {:02X}",
                fileBytes[0], fileBytes[1], fileBytes[2], fileBytes[3],
                fileBytes.length > 4 ? fileBytes[4] : 0,
                fileBytes.length > 5 ? fileBytes[5] : 0,
                fileBytes.length > 6 ? fileBytes[6] : 0,
                fileBytes.length > 7 ? fileBytes[7] : 0);

            // PDF: %PDF (0x25504446)
            if (contentType.equals("application/pdf")) {
                boolean isValidPdf = fileBytes[0] == 0x25 && fileBytes[1] == 0x50 &&
                                   fileBytes[2] == 0x44 && fileBytes[3] == 0x46;
                log.debug("PDF validation result: {}", isValidPdf);
                return isValidPdf;
            }

            // JPEG: FF D8 FF
            if (contentType.equals("image/jpeg")) {
                boolean isValidJpeg = fileBytes[0] == (byte)0xFF && fileBytes[1] == (byte)0xD8 && fileBytes[2] == (byte)0xFF;
                log.debug("JPEG validation result: {}", isValidJpeg);
                return isValidJpeg;
            }

            // PNG: 89 50 4E 47
            if (contentType.equals("image/png")) {
                boolean isValidPng = fileBytes[0] == (byte)0x89 && fileBytes[1] == 0x50 &&
                                   fileBytes[2] == 0x4E && fileBytes[3] == 0x47;
                log.debug("PNG validation result: {}", isValidPng);
                return isValidPng;
            }

            // ZIP: 50 4B 03 04 or 50 4B 05 06 or 50 4B 07 08
            if (contentType.equals("application/zip")) {
                boolean isValidZip = (fileBytes[0] == 0x50 && fileBytes[1] == 0x4B &&
                                    (fileBytes[2] == 0x03 || fileBytes[2] == 0x05 || fileBytes[2] == 0x07));
                log.debug("ZIP validation result: {}", isValidZip);
                return isValidZip;
            }

            // MS Office documents (newer format) - they are actually ZIP files
            if (contentType.equals("application/vnd.openxmlformats-officedocument.wordprocessingml.document") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
                contentType.equals("application/vnd.openxmlformats-officedocument.presentationml.presentation")) {
                boolean isValidOffice = (fileBytes[0] == 0x50 && fileBytes[1] == 0x4B &&
                                       (fileBytes[2] == 0x03 || fileBytes[2] == 0x05 || fileBytes[2] == 0x07));
                log.debug("MS Office (new format) validation result: {}", isValidOffice);
                return isValidOffice;
            }

            // For other file types that we don't have specific magic number checks,
            // we'll allow them but log for monitoring
            log.debug("No specific magic number check for MIME type: {}, allowing", contentType);
            return true;

        } catch (IOException e) {
            log.error("Error reading file header: {}", e.getMessage());
            return false;
        }
    }
}
