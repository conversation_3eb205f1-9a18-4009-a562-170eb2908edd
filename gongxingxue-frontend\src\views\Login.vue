<template>
  <div class="login-page">
    <div class="login-container">
      <h2 class="title">用户登录</h2>

      <el-form
        ref="loginFormRef"
        :model="loginData"
        :rules="loginRules"
        label-width="0"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginData.username"
            placeholder="用户名"
            prefix-icon="el-icon-user"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginData.password"
            type="password"
            placeholder="密码"
            prefix-icon="el-icon-lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginData.captcha"
              placeholder="验证码"
              prefix-icon="el-icon-picture"
              @keyup.enter="handleLogin"
              style="flex: 1;"
            />
            <img
              :src="captchaUrl"
              @click="refreshCaptcha"
              class="captcha-image"
              title="点击刷新验证码"
              alt="验证码"
            />
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <div>
          <span>还没有账号？</span>
          <router-link to="/register">立即注册</router-link>
        </div>
        <div style="margin-top: 10px;">
          <router-link to="/forgot-password">忘记密码？</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// Form data
const loginData = reactive({
  username: '',
  password: '',
  captcha: ''
})

// Form validation rules
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { min: 4, max: 4, message: '验证码为4位字符', trigger: 'blur' }
  ]
}

// Form ref
const loginFormRef = ref(null)

// Loading state
const loading = ref(false)

// Captcha
const captchaUrl = ref('')

// Refresh captcha
const refreshCaptcha = () => {
  captchaUrl.value = `/api/auth/captcha?t=${Date.now()}`
}

// Handle login
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true

    try {
      console.log('Attempting login with:', {
        username: loginData.username,
        password: loginData.password.replace(/./g, '*') // 隐藏密码
      })

      await userStore.login(loginData.username, loginData.password, loginData.captcha)

      ElMessage.success('登录成功')

      // Redirect to the original requested page or home page
      const redirect = route.query.redirect || '/'
      router.replace(redirect)
    } catch (error) {
      // 只在开发环境输出详细错误信息
      if (import.meta.env.DEV) {
        console.error('Login failed:', error)
      }

      // 刷新验证码
      refreshCaptcha()
      loginData.captcha = ''

      // 显示更详细的错误信息
      if (error.response) {
        if (import.meta.env.DEV) {
          console.error('Error response:', error.response)
        }
        ElMessage.error(`登录失败: ${error.response.data?.message || error.message || '请检查用户名和密码'}`)
      } else if (error.request) {
        if (import.meta.env.DEV) {
          console.error('Error request:', error.request)
        }
        ElMessage.error('网络请求失败，请检查网络连接')
      } else {
        ElMessage.error(error.message || '登录失败，请检查用户名和密码')
      }
    } finally {
      loading.value = false
    }
  })
}

// Load captcha on component mount
onMounted(() => {
  refreshCaptcha()
})
</script>

<style lang="scss" scoped>
.login-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 60px - 60px); /* Subtract header and footer height */

  .login-container {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .title {
      text-align: center;
      margin-bottom: 30px;
      color: #303133;
    }

    .login-button {
      width: 100%;
    }

    .captcha-container {
      display: flex;
      align-items: center;
      gap: 10px;

      .captcha-image {
        width: 120px;
        height: 40px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        cursor: pointer;
        transition: border-color 0.3s;

        &:hover {
          border-color: #409eff;
        }
      }
    }

    .login-footer {
      margin-top: 20px;
      text-align: center;
      font-size: 14px;
      color: #606266;

      a {
        color: #409eff;
        margin-left: 5px;
      }
    }
  }
}
</style>
