package com.gongxingxue.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gongxingxue.common.Result;
import com.gongxingxue.entity.Resource;
import com.gongxingxue.entity.User;
import com.gongxingxue.service.FileStorageService;
import com.gongxingxue.service.ResourceService;
import com.gongxingxue.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
@Api(tags = "管理员功能", description = "管理员专用接口，包括资料审核、用户管理等功能")
public class AdminController {

    private final ResourceService resourceService;
    private final UserService userService;
    private final FileStorageService fileStorageService;

    /**
     * 获取待审核资料列表
     */
    @GetMapping("/resources/pending")
    @ApiOperation(value = "获取待审核资料", notes = "分页获取所有待审核的资料，支持多种筛选条件，仅管理员可访问")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回分页的待审核资料列表"),
        @ApiResponse(code = 401, message = "用户未登录"),
        @ApiResponse(code = 403, message = "权限不足，仅管理员可访问")
    })
    public Result<Page<Resource>> getPendingResources(
            @ApiParam(value = "页码", required = false, example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @ApiParam(value = "每页数量", required = false, example = "10")
            @RequestParam(defaultValue = "10") Integer size,

            @ApiParam(value = "考试类型筛选", required = false, example = "1")
            @RequestParam(required = false) Integer examType,

            @ApiParam(value = "文件类型筛选", required = false, example = "pdf")
            @RequestParam(required = false) String fileType,

            @ApiParam(value = "上传用户筛选", required = false, example = "testuser")
            @RequestParam(required = false) String username,

            @ApiParam(value = "关键词搜索", required = false, example = "数学")
            @RequestParam(required = false) String keyword,

            @ApiParam(value = "开始时间", required = false, example = "2024-01-01 00:00:00")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

            @ApiParam(value = "结束时间", required = false, example = "2024-12-31 23:59:59")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        Page<Resource> resources = resourceService.getPendingResources(page, size, examType, fileType, username, keyword, startTime, endTime);

        return Result.success(resources);
    }

    /**
     * Get approved resources
     */
    @GetMapping("/resources/approved")
    @ApiOperation("Get approved resources")
    public Result<Page<Resource>> getApprovedResources(
            @RequestParam(required = false) Integer examType,
            @RequestParam(required = false) String fileType,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime auditStartTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime auditEndTime) {

        Page<Resource> resources = resourceService.getApprovedResources(examType, fileType, username, keyword, page, size, startTime, endTime, auditStartTime, auditEndTime);

        return Result.success(resources);
    }

    /**
     * 获取已驳回资料列表
     */
    @GetMapping("/resources/rejected")
    @ApiOperation(value = "获取已驳回资料", notes = "分页获取所有已驳回的资料，支持多种筛选条件，仅管理员可访问")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回分页的已驳回资料列表"),
        @ApiResponse(code = 401, message = "用户未登录"),
        @ApiResponse(code = 403, message = "权限不足，仅管理员可访问")
    })
    public Result<Page<Resource>> getRejectedResources(
            @ApiParam(value = "页码", required = false, example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @ApiParam(value = "每页数量", required = false, example = "10")
            @RequestParam(defaultValue = "10") Integer size,

            @ApiParam(value = "考试类型筛选", required = false, example = "1")
            @RequestParam(required = false) Integer examType,

            @ApiParam(value = "文件类型筛选", required = false, example = "pdf")
            @RequestParam(required = false) String fileType,

            @ApiParam(value = "上传用户筛选", required = false, example = "testuser")
            @RequestParam(required = false) String username,

            @ApiParam(value = "关键词搜索", required = false, example = "数学")
            @RequestParam(required = false) String keyword,

            @ApiParam(value = "上传开始时间", required = false, example = "2024-01-01 00:00:00")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,

            @ApiParam(value = "上传结束时间", required = false, example = "2024-12-31 23:59:59")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,

            @ApiParam(value = "审核开始时间", required = false, example = "2024-01-01 00:00:00")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime auditStartTime,

            @ApiParam(value = "审核结束时间", required = false, example = "2024-12-31 23:59:59")
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime auditEndTime) {

        Page<Resource> resources = resourceService.getRejectedResources(examType, fileType, username, keyword, page, size, startTime, endTime, auditStartTime, auditEndTime);

        return Result.success(resources);
    }

    /**
     * Approve resource
     */
    @PutMapping("/resources/{id}/approve")
    @ApiOperation("Approve resource")
    public Result<Void> approveResource(@PathVariable Long id, HttpServletRequest request) {
        Long adminUserId = (Long) request.getAttribute("userId");
        boolean success = resourceService.approveResource(id, adminUserId);

        if (success) {
            return Result.success("Resource approved successfully", null);
        } else {
            return Result.error("Failed to approve resource or resource not found");
        }
    }

    /**
     * Reject resource
     */
    @PutMapping("/resources/{id}/reject")
    @ApiOperation("Reject resource")
    public Result<Void> rejectResource(
            @PathVariable Long id,
            @RequestParam String rejectReason,
            HttpServletRequest request) {

        Long adminUserId = (Long) request.getAttribute("userId");
        boolean success = resourceService.rejectResource(id, rejectReason, adminUserId);

        if (success) {
            return Result.success("Resource rejected successfully", null);
        } else {
            return Result.error("Failed to reject resource or resource not found");
        }
    }

    /**
     * Batch approve resources
     */
    @PutMapping("/resources/batch-approve")
    @ApiOperation("Batch approve resources")
    public Result<Void> batchApproveResources(
            @RequestBody List<Long> resourceIds,
            HttpServletRequest request) {

        Long adminUserId = (Long) request.getAttribute("userId");
        boolean success = resourceService.batchApproveResources(resourceIds, adminUserId);

        if (success) {
            return Result.success("Resources approved successfully", null);
        } else {
            return Result.error("Failed to approve resources");
        }
    }

    /**
     * Batch reject resources
     */
    @PutMapping("/resources/batch-reject")
    @ApiOperation("Batch reject resources")
    public Result<Void> batchRejectResources(
            @RequestBody List<Long> resourceIds,
            @RequestParam String rejectReason,
            HttpServletRequest request) {

        Long adminUserId = (Long) request.getAttribute("userId");
        boolean success = resourceService.batchRejectResources(resourceIds, rejectReason, adminUserId);

        if (success) {
            return Result.success("Resources rejected successfully", null);
        } else {
            return Result.error("Failed to reject resources");
        }
    }

    /**
     * Get user list
     */
    @GetMapping("/users")
    @ApiOperation("Get user list")
    public Result<Page<User>> getUserList(
            @RequestParam(required = false) String username,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        System.out.println("=== getUserList Debug ===");
        System.out.println("username: " + username);
        System.out.println("startTime: " + startTime);
        System.out.println("endTime: " + endTime);

        Page<User> users = userService.getUserList(username, startTime, endTime, page, size);

        // Remove sensitive information
        users.getRecords().forEach(user -> user.setPassword(null));

        return Result.success(users);
    }

    /**
     * Update user status
     */
    @PutMapping("/users/{id}/status")
    @ApiOperation("Update user status")
    public Result<Void> updateUserStatus(
            @PathVariable Long id,
            @RequestParam Integer status) {

        boolean success = userService.updateUserStatus(id, status);

        if (success) {
            return Result.success("User status updated successfully", null);
        } else {
            return Result.error("Failed to update user status or user not found");
        }
    }

    /**
     * Admin preview resource (can preview pending resources)
     */
    @GetMapping("/resources/preview/{id}")
    @ApiOperation("Admin preview resource")
    public ResponseEntity<org.springframework.core.io.Resource> previewResource(
            @PathVariable Long id,
            HttpServletRequest request) {

        try {
            // Get resource (admin can preview any resource, including pending ones)
            Resource resource = resourceService.getResourceById(id);
            if (resource == null) {
                return ResponseEntity.notFound().build();
            }

            // Check if file can be previewed in browser
            String filename = resource.getOriginalFilename();
            if (!canPreviewInBrowser(filename)) {
                // If file cannot be previewed, return a friendly HTML page with download option
                String htmlContent = generatePreviewNotSupportedPage(resource);
                return ResponseEntity.ok()
                        .contentType(MediaType.TEXT_HTML)
                        .body(new org.springframework.core.io.ByteArrayResource(htmlContent.getBytes("UTF-8")));
            }

            // Load file as resource
            org.springframework.core.io.Resource fileResource = fileStorageService.loadFileAsResource(resource.getFilePath());

            // Determine content type for preview
            String contentType = determinePreviewContentType(filename);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline")
                    .header(HttpHeaders.CACHE_CONTROL, "public, max-age=3600")
                    .body(fileResource);
        } catch (Exception ex) {
            System.err.println("Error previewing file: " + ex.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Admin download resource (can download any resource without authentication in preview context)
     */
    @GetMapping("/resources/download/{id}")
    @ApiOperation("Admin download resource")
    public ResponseEntity<org.springframework.core.io.Resource> downloadResource(
            @PathVariable Long id,
            HttpServletRequest request) {

        try {
            // Get resource (admin can download any resource, including pending ones)
            Resource resource = resourceService.getResourceById(id);
            if (resource == null) {
                return ResponseEntity.notFound().build();
            }

            // Load file as resource
            org.springframework.core.io.Resource fileResource = fileStorageService.loadFileAsResource(resource.getFilePath());

            // Determine content type based on file extension
            String contentType = determineContentType(resource.getOriginalFilename());

            // Encode filename for proper handling of Chinese characters
            String encodedFilename = encodeFilename(resource.getOriginalFilename(), request);

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; " + encodedFilename)
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                    .header(HttpHeaders.PRAGMA, "no-cache")
                    .header(HttpHeaders.EXPIRES, "0")
                    .body(fileResource);
        } catch (Exception ex) {
            System.err.println("Error downloading file: " + ex.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Check if file can be previewed in browser
     */
    private boolean canPreviewInBrowser(String filename) {
        if (filename == null) {
            return false;
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        // Files that can be previewed in browser
        return extension.equals("pdf") ||
               extension.equals("txt") ||
               extension.equals("jpg") ||
               extension.equals("jpeg") ||
               extension.equals("png") ||
               extension.equals("gif") ||
               extension.equals("mp4") ||
               extension.equals("mp3");
    }

    /**
     * Determine content type for preview (optimized for browser display)
     */
    private String determinePreviewContentType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        Map<String, String> previewContentTypes = new HashMap<>();

        // PDF - can be previewed in browser
        previewContentTypes.put("pdf", "application/pdf");

        // Text files - can be previewed in browser
        previewContentTypes.put("txt", "text/plain; charset=utf-8");

        // Images - can be previewed in browser
        previewContentTypes.put("jpg", "image/jpeg");
        previewContentTypes.put("jpeg", "image/jpeg");
        previewContentTypes.put("png", "image/png");
        previewContentTypes.put("gif", "image/gif");

        // Audio/Video - can be previewed in browser
        previewContentTypes.put("mp3", "audio/mpeg");
        previewContentTypes.put("mp4", "video/mp4");

        return previewContentTypes.getOrDefault(extension, "application/octet-stream");
    }



    /**
     * Generate HTML page for files that cannot be previewed
     */
    private String generatePreviewNotSupportedPage(Resource resource) {
        String filename = resource.getOriginalFilename();
        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toUpperCase();
        }

        // Format file size
        String fileSize = formatFileSize(resource.getFileSize());

        return "<!DOCTYPE html>" +
                "<html lang='zh-CN'>" +
                "<head>" +
                "    <meta charset='UTF-8'>" +
                "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                "    <title>文件预览 - " + filename + "</title>" +
                "    <style>" +
                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 0; padding: 40px; background: #f5f5f5; }" +
                "        .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }" +
                "        .icon { font-size: 64px; color: #409eff; margin-bottom: 20px; }" +
                "        .title { font-size: 24px; color: #303133; margin-bottom: 10px; }" +
                "        .subtitle { font-size: 16px; color: #606266; margin-bottom: 30px; }" +
                "        .file-info { background: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 30px; text-align: left; }" +
                "        .file-info-item { margin-bottom: 10px; }" +
                "        .file-info-label { font-weight: bold; color: #303133; display: inline-block; width: 80px; }" +
                "        .file-info-value { color: #606266; }" +
                "        .download-btn { background: #409eff; color: white; padding: 12px 24px; border: none; border-radius: 4px; font-size: 16px; cursor: pointer; text-decoration: none; display: inline-block; }" +
                "        .download-btn:hover { background: #337ecc; }" +
                "        .download-btn:active { background: #2b6cb0; }" +
                "        .supported-formats { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ebeef5; }" +
                "        .supported-formats h4 { color: #303133; margin-bottom: 10px; }" +
                "        .formats-list { color: #606266; font-size: 14px; }" +
                "    </style>" +
                "</head>" +
                "<body>" +
                "    <div class='container'>" +
                "        <div class='icon'>📄</div>" +
                "        <h1 class='title'>无法预览此文件</h1>" +
                "        <p class='subtitle'>" + extension + " 格式的文件暂不支持在线预览</p>" +
                "        <div class='file-info'>" +
                "            <div class='file-info-item'>" +
                "                <span class='file-info-label'>文件名：</span>" +
                "                <span class='file-info-value'>" + filename + "</span>" +
                "            </div>" +
                "            <div class='file-info-item'>" +
                "                <span class='file-info-label'>文件类型：</span>" +
                "                <span class='file-info-value'>" + extension + " 文件</span>" +
                "            </div>" +
                "            <div class='file-info-item'>" +
                "                <span class='file-info-label'>文件大小：</span>" +
                "                <span class='file-info-value'>" + fileSize + "</span>" +
                "            </div>" +
                "        </div>" +
                "        <a href='/api/admin/resources/download/" + resource.getId() + "' class='download-btn' download='" + escapeHtmlAttribute(resource.getOriginalFilename()) + "'>📥 下载文件</a>" +
                "        <div class='supported-formats'>" +
                "            <h4>支持预览的文件格式：</h4>" +
                "            <p class='formats-list'>PDF、TXT、JPG、JPEG、PNG、GIF、MP3、MP4</p>" +
                "        </div>" +
                "    </div>" +
                "</body>" +
                "</html>";
    }

    /**
     * Format file size to human readable format
     */
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize == 0) {
            return "未知";
        }

        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * Determine content type based on file extension
     */
    private String determineContentType(String filename) {
        if (filename == null) {
            return "application/octet-stream";
        }

        String extension = "";
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            extension = filename.substring(lastDotIndex + 1).toLowerCase();
        }

        Map<String, String> contentTypes = new HashMap<>();
        // Office documents
        contentTypes.put("doc", "application/msword");
        contentTypes.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        contentTypes.put("xls", "application/vnd.ms-excel");
        contentTypes.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        contentTypes.put("ppt", "application/vnd.ms-powerpoint");
        contentTypes.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");

        // PDF
        contentTypes.put("pdf", "application/pdf");

        // Text files
        contentTypes.put("txt", "text/plain; charset=utf-8");

        // Images
        contentTypes.put("jpg", "image/jpeg");
        contentTypes.put("jpeg", "image/jpeg");
        contentTypes.put("png", "image/png");
        contentTypes.put("gif", "image/gif");

        // Archives
        contentTypes.put("zip", "application/zip");
        contentTypes.put("rar", "application/x-rar-compressed");
        contentTypes.put("7z", "application/x-7z-compressed");

        // Audio/Video
        contentTypes.put("mp3", "audio/mpeg");
        contentTypes.put("mp4", "video/mp4");

        return contentTypes.getOrDefault(extension, "application/octet-stream");
    }

    /**
     * Encode filename for proper handling of Chinese characters
     */
    private String encodeFilename(String filename, HttpServletRequest request) {
        if (filename == null || filename.trim().isEmpty()) {
            return "filename=\"download\"";
        }

        String userAgent = request.getHeader("User-Agent");

        try {
            String encodedFilename;

            if (userAgent != null && userAgent.toLowerCase().contains("msie")) {
                // IE browser
                encodedFilename = "filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20") + "\"";
            } else if (userAgent != null && userAgent.toLowerCase().contains("firefox")) {
                // Firefox browser
                encodedFilename = "filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
            } else {
                // Chrome, Safari and other modern browsers
                // Use both filename and filename* for maximum compatibility
                String asciiFilename = filename.replaceAll("[^\\x00-\\x7F]", "_"); // Replace non-ASCII with underscore
                encodedFilename = "filename=\"" + asciiFilename + "\"; filename*=UTF-8''" + URLEncoder.encode(filename, StandardCharsets.UTF_8.toString()).replace("+", "%20");
            }

            return encodedFilename;

        } catch (UnsupportedEncodingException e) {
            return "filename=\"" + filename + "\"";
        }
    }

    /**
     * Escape string for safe use in HTML attributes
     */
    private String escapeHtmlAttribute(String str) {
        if (str == null) {
            return "";
        }

        // 转义HTML属性中的特殊字符
        return str
                .replace("&", "&amp;")   // & 必须最先转义
                .replace("\"", "&quot;") // 双引号
                .replace("'", "&#39;")   // 单引号
                .replace("<", "&lt;")    // 小于号
                .replace(">", "&gt;");   // 大于号
    }
}
