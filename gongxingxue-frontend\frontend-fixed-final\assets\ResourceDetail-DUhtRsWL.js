const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-BC3U7MsG.js","assets/index-yrzYoO3-.css"])))=>i.map(i=>d[i]);
import{u as se,c as U,E as f,D as ue,k as me,r as w,o as de,g as b,b as p,l as r,d as o,i as B,e as l,f as m,m as x,t as d,j as oe,G as ae,h as J,F as te,v as pe,p as _e,q as ce,s as he,H as ye,I as ee,_ as we,J as Ce}from"./index-BC3U7MsG.js";import{a as ke,d as be}from"./resource-CWJ1Qb9i.js";import{g as ge,a as ie,b as xe,d as Te}from"./comment-DlqTXJ-w.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";function Re(){const g=se(),A=me(),T=U(()=>g.isLoggedIn),R=U(()=>g.user),C=U(()=>{var u;return((u=g.user)==null?void 0:u.role)===1}),n=async(u="此操作",k=!0)=>{try{await ue.confirm(`${u}需要登录，是否前往登录页面？`,"需要登录",{confirmButtonText:"去登录",cancelButtonText:"取消",type:"info",center:!0}),A.push({path:"/login",query:{redirect:A.currentRoute.value.fullPath}})}catch{k||f.info("操作已取消")}};return{isLoggedIn:T,currentUser:R,isAdmin:C,showLoginPrompt:n,checkAuthAndExecute:async(u,k={})=>{const{requireAuth:L=!0,requireAdmin:$=!1,actionName:D="此操作",showPrompt:M=!0}=k;if(!L)return await u();if(!T.value)return M?await n(D):f.warning(`${D}需要登录`),!1;if($&&!C.value)return f.error("权限不足，需要管理员权限"),!1;try{return await u()}catch(I){return console.error("操作执行失败:",I),f.error("操作失败，请稍后重试"),!1}},hasPermission:u=>{switch(u){case"login":return T.value;case"admin":return C.value;default:return!0}},getActionConfig:u=>({download:{requireAuth:!0,actionName:"下载资料",icon:"Download",text:T.value?"下载":"登录后下载"},favorite:{requireAuth:!0,actionName:"收藏资料",icon:"Star",text:T.value?"收藏":"登录后收藏"},comment:{requireAuth:!0,actionName:"发表评论",icon:"ChatDotRound",text:T.value?"发表评论":"登录后评论"},upload:{requireAuth:!0,actionName:"上传资料",icon:"Upload",text:T.value?"上传资料":"登录后上传"},admin:{requireAuth:!0,requireAdmin:!0,actionName:"管理操作",icon:"Setting",text:"管理"}})[u]||{requireAuth:!1,actionName:"操作",text:"操作"}}}const $e=["data-comment-id"],Ie={class:"comment-header"},Ae={class:"user-info"},De={class:"user-details"},qe={class:"username"},Ee={class:"comment-time"},Se={key:0,class:"comment-actions"},Le={class:"comment-content"},Ue={key:0,class:"comment-replies"},Ne=["data-comment-id"],Pe={class:"reply-header"},Be={class:"user-info"},Fe={class:"user-details"},ze={class:"username"},Ve={class:"comment-time"},Me={key:0,class:"comment-actions"},je={class:"reply-content"},Oe={key:1,class:"load-replies"},He={__name:"CommentItem",props:{comment:{type:Object,required:!0},showActions:{type:Boolean,default:!0}},emits:["reply","delete","refresh-replies"],setup(g,{expose:A,emit:T}){const R=g,C=T,n=se(),E=w([]),S=w(!1),N=w(!1),u=U(()=>{var h,F;const c=((h=R.comment.user)==null?void 0:h.nickname)||((F=R.comment.user)==null?void 0:F.username)||"";return c?c.charAt(0).toUpperCase():"U"}),k=U(()=>n.isLoggedIn&&(n.user.id===R.comment.userId||n.isAdmin)),L=U(()=>!N.value&&R.comment.replyCount>0),$=c=>c?c.charAt(0).toUpperCase():"U",D=c=>n.isLoggedIn&&(n.user.id===c.userId||n.isAdmin),M=()=>{C("reply",R.comment)},I=c=>{C("reply",c)},_=()=>{C("delete",R.comment.id)},ne=c=>{C("delete",c.id)},O=async()=>{if(!S.value){S.value=!0;try{const c=await ge(R.comment.id);E.value=c.data,N.value=!0}catch(c){console.error("Failed to load replies:",c)}finally{S.value=!1}}};return A({refreshReplies:async()=>{N.value=!1,await O()}}),de(()=>{O()}),(c,h)=>{var K,Q,W;const F=b("el-avatar"),z=b("el-button");return r(),p("div",{class:"comment-item","data-comment-id":g.comment.id},[o("div",Ie,[o("div",Ae,[l(F,{size:40,src:(K=g.comment.user)==null?void 0:K.avatar},{default:m(()=>[x(d(u.value),1)]),_:1},8,["src"]),o("div",De,[o("div",qe,d(((Q=g.comment.user)==null?void 0:Q.nickname)||((W=g.comment.user)==null?void 0:W.username)||"匿名用户"),1),o("div",Ee,d(oe(ae)(g.comment.createTime)),1)])]),g.showActions?(r(),p("div",Se,[l(z,{type:"text",size:"small",onClick:M},{default:m(()=>h[0]||(h[0]=[x("回复")])),_:1,__:[0]}),k.value?(r(),J(z,{key:0,type:"text",size:"small",onClick:_},{default:m(()=>h[1]||(h[1]=[x("删除")])),_:1,__:[1]})):B("",!0)])):B("",!0)]),o("div",Le,[o("p",null,d(g.comment.content),1)]),E.value.length>0?(r(),p("div",Ue,[(r(!0),p(te,null,pe(E.value,v=>{var H,X,Y;return r(),p("div",{class:"reply-item",key:v.id,"data-comment-id":v.id},[o("div",Pe,[o("div",Be,[l(F,{size:30,src:(H=v.user)==null?void 0:H.avatar},{default:m(()=>{var e,t;return[x(d($(((e=v.user)==null?void 0:e.nickname)||((t=v.user)==null?void 0:t.username))),1)]}),_:2},1032,["src"]),o("div",Fe,[o("div",ze,d(((X=v.user)==null?void 0:X.nickname)||((Y=v.user)==null?void 0:Y.username)||"匿名用户"),1),o("div",Ve,d(oe(ae)(v.createTime)),1)])]),g.showActions?(r(),p("div",Me,[l(z,{type:"text",size:"small",onClick:e=>I(v)},{default:m(()=>h[2]||(h[2]=[x("回复")])),_:2,__:[2]},1032,["onClick"]),D(v)?(r(),J(z,{key:0,type:"text",size:"small",onClick:e=>ne(v)},{default:m(()=>h[3]||(h[3]=[x("删除")])),_:2,__:[3]},1032,["onClick"])):B("",!0)])):B("",!0)]),o("div",je,[o("p",null,d(v.content),1)])],8,Ne)}),128))])):B("",!0),L.value?(r(),p("div",Oe,[l(z,{type:"text",onClick:O},{default:m(()=>[x(d(S.value?"加载中...":"查看更多回复"),1)]),_:1})])):B("",!0)],8,$e)}}},Ge=ve(He,[["__scopeId","data-v-54cf9188"]]),Je={class:"resource-detail"},Ke={class:"resource-header"},Qe={class:"resource-title"},We={class:"resource-meta"},Xe={class:"meta-item"},Ye={class:"meta-item"},Ze={class:"meta-item"},et={class:"resource-actions"},tt={class:"resource-description card"},ot={class:"resource-comments card"},nt={key:0,class:"comment-form"},at={class:"comment-form-actions"},st={key:1,class:"login-to-comment"},lt={class:"comments-list"},rt={key:0,class:"pagination"},ct={__name:"ResourceDetail",setup(g){const A=_e(),T=me();se();const{isLoggedIn:R,checkAuthAndExecute:C}=Re(),n=w(null),E=w(!1),S=w([]),N=w(!1),u=w(0),k=w(1),L=w(10),$=w(""),D=w(null),M=w(0),I=U(()=>Number(A.params.id)),_=U(()=>A.query.commentId?Number(A.query.commentId):null),ne=U(()=>n.value?["考研","考公","法考","教资","其他"][n.value.examType]||"未知":""),O=async()=>{E.value=!0;try{const e=await ke(I.value);n.value=e.data}catch(e){console.error("Failed to fetch resource details:",e),f.error("获取资料详情失败")}finally{E.value=!1}},j=async()=>{N.value=!0;try{const e=await ie(I.value,k.value,L.value);S.value=e.data.records,u.value=e.data.total,M.value++,_.value?(console.log("Found target comment ID in URL:",_.value),await ee(),Y()):console.log("No target comment ID in URL, route.query:",A.query)}catch(e){console.error("Failed to fetch comments:",e)}finally{N.value=!1}},c=async()=>await C(async()=>{try{const e=await be(I.value);if(!e||!e.data)throw new Error("下载响应为空");const t=e.data;if(!(t instanceof Blob)||t.size===0)throw new Error("下载的文件为空或格式错误");console.log("Downloaded blob size:",t.size,"bytes"),console.log("Downloaded blob type:",t.type);let a=n.value.originalFilename||n.value.name||`resource_${I.value}`;const s=window.URL.createObjectURL(t),i=document.createElement("a");i.href=s,i.download=a,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(s),console.log("Download initiated for:",a,"Size:",t.size,"bytes")}catch(e){console.error("Download failed:",e),f.error("下载失败："+(e.message||"请稍后重试"))}},{actionName:"下载资料"}),h=async()=>{var s;const t=(s=(n.value.originalFilename||n.value.name).split(".").pop())==null?void 0:s.toLowerCase();if(["pdf","txt","jpg","jpeg","png","gif","mp4","mp3"].includes(t))return await C(async()=>{try{const{getFileBaseUrl:i}=await we(async()=>{const{getFileBaseUrl:P}=await import("./index-BC3U7MsG.js").then(G=>G.a4);return{getFileBaseUrl:P}},__vite__mapDeps([0,1])),q=`${i()}/api/resources/view/${I.value}`,V=Ce();if(V){const P=`${q}?token=${encodeURIComponent(V)}`;window.open(P,"_blank")}else f.error("请先登录后再预览")}catch(i){console.error("Preview failed:",i),f.error("预览失败："+(i.message||"请稍后重试"))}},{actionName:"预览资料"});f.warning({message:`${t.toUpperCase()} 文件无法在线预览，将在 2 秒后自动下载`,duration:2e3,showClose:!0}),setTimeout(()=>{c()},2e3)},F=async()=>{if($.value.trim())return await C(async()=>{try{const e={resourceId:I.value,content:$.value};D.value&&(e.parentId=D.value.id),await xe(e),f.success(D.value?"回复成功":"评论成功"),await new Promise(t=>setTimeout(t,200)),await j(),$.value="",D.value=null,n.value&&(n.value.commentCount+=1)}catch(e){console.error("Failed to submit comment:",e),f.error("评论失败，请稍后重试")}},{actionName:"发表评论"})},z=e=>{var s,i;D.value=e;const t=((s=e.user)==null?void 0:s.nickname)||((i=e.user)==null?void 0:i.username)||`用户${e.userId}`;$.value=`@${t} `;const a=document.querySelector(".comment-form");a&&a.scrollIntoView({behavior:"smooth"})},K=async e=>{try{await ue.confirm("确定要删除这条评论吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Te(e),f.success("评论已删除"),j(),n.value&&(n.value.commentCount-=1)}catch(t){t!=="cancel"&&(console.error("Failed to delete comment:",t),f.error("删除评论失败"))}},Q=e=>{k.value=e,j()},W=async()=>{if(!_.value)return null;console.log("Searching for comment",_.value,"across all pages");const e=Math.ceil(u.value/L.value);console.log("Total pages to search:",e);for(let t=1;t<=e;t++)try{console.log("Searching page",t);const s=(await ie(I.value,t,L.value)).data.records;if(s.find(y=>String(y.id)===String(_.value)))return console.log("Found target comment (top-level) on page",t),{page:t,parentId:null};for(const y of s)if(y.replyCount>0)try{if((await ge(y.id)).data.find(G=>String(G.id)===String(_.value)))return console.log("Found target comment (reply) on page",t,"under parent",y.id),{page:t,parentId:y.id}}catch(q){console.error("Error loading replies for comment",y.id,q)}}catch(a){console.error("Error searching page",t,a)}return console.log("Target comment not found in any page"),null},v=async e=>{console.log("Ensuring replies for parent",e,"are loaded...");const t=document.querySelector(`[data-comment-id="${e}"]`);if(t){const a=t.querySelector(".load-replies .el-button");a&&a.textContent.includes("查看更多回复")?(console.log("Clicking load more replies button for parent",e),a.click(),await new Promise(s=>setTimeout(s,1e3))):console.log("No load more button found for parent",e,"replies might already be loaded")}else console.log("Parent element not found for ID",e)},H=async()=>{console.log("Ensuring all replies are loaded...");const e=document.querySelectorAll(".load-replies .el-button");console.log("Found",e.length,"load more buttons");for(const t of e)t.textContent.includes("查看更多回复")&&(console.log("Clicking load more replies button"),t.click(),await new Promise(a=>setTimeout(a,500)))},X=async(e=15)=>{for(let t=0;t<e;t++){const a=document.querySelector(`[data-comment-id="${_.value}"]`);if(a)return console.log("Target element found after",t+1,"attempts"),a;await new Promise(s=>setTimeout(s,300))}return null},Y=async()=>{if(console.log("scrollToTargetComment called, targetCommentId:",_.value),!_.value){console.log("No target comment ID found");return}let e=document.querySelector(`[data-comment-id="${_.value}"]`);if(console.log("Target element found on current page:",e),e||(console.log("Target not found, trying to load all replies first..."),await H(),await ee(),e=document.querySelector(`[data-comment-id="${_.value}"]`),console.log("Target element found after loading replies:",e)),!e){console.log("Target not found on current page, searching other pages...");const t=await W();if(t){const{page:a,parentId:s}=t;a!==k.value&&(console.log("Switching to page",a),k.value=a,await j(),await ee()),s?(console.log("Target is a reply under parent",s,"ensuring parent replies are loaded"),await v(s)):await H(),await ee(),console.log("After page switch and reply loading, checking DOM...");const i=document.querySelectorAll("[data-comment-id]");console.log("All comment elements found after page switch:",i.length),i.forEach((y,q)=>{const V=y.getAttribute("data-comment-id"),P=y.classList.contains("reply-item");console.log(`  ${q+1}. Comment ID: ${V} (${P?"reply":"top-level"})`)}),e=await X()}}if(e){console.log("Scrolling to target comment and adding highlight"),e.scrollIntoView({behavior:"smooth",block:"center"}),e.classList.add("highlight-comment"),setTimeout(()=>{e.classList.remove("highlight-comment"),console.log("Highlight removed")},3e3);const t={...A.query};delete t.commentId,T.replace({query:t})}else console.log("Target element still not found after searching all pages"),f.warning("未找到指定的评论，可能已被删除")};return de(()=>{O(),j()}),(e,t)=>{const a=b("el-icon-download"),s=b("el-icon"),i=b("el-icon-chat-dot-round"),y=b("el-icon-clock"),q=b("el-button"),V=b("el-icon-view"),P=b("el-input"),G=b("router-link"),fe=b("el-pagination"),le=b("el-empty"),re=he("loading");return ce((r(),p("div",Je,[n.value?(r(),p(te,{key:0},[o("div",Ke,[o("div",Qe,[o("span",{class:ye(["exam-type-tag",`exam-type-${n.value.examType}`])},d(ne.value),3),o("h1",null,d(n.value.name),1)]),o("div",We,[o("div",Xe,[l(s,null,{default:m(()=>[l(a)]),_:1}),o("span",null,d(n.value.downloadCount)+" 下载",1)]),o("div",Ye,[l(s,null,{default:m(()=>[l(i)]),_:1}),o("span",null,d(n.value.commentCount)+" 评论",1)]),o("div",Ze,[l(s,null,{default:m(()=>[l(y)]),_:1}),o("span",null,d(oe(ae)(n.value.auditTime)),1)])])]),o("div",et,[l(q,{type:"primary",onClick:c},{default:m(()=>[l(s,null,{default:m(()=>[l(a)]),_:1}),t[1]||(t[1]=x(" 下载资料 "))]),_:1,__:[1]}),l(q,{type:"success",onClick:h},{default:m(()=>[l(s,null,{default:m(()=>[l(V)]),_:1}),t[2]||(t[2]=x(" 在线查看 "))]),_:1,__:[2]})]),o("div",tt,[t[3]||(t[3]=o("h3",null,"资料简介",-1)),o("p",null,d(n.value.description||"暂无简介"),1)]),o("div",ot,[o("h3",null,"评论区 ("+d(n.value.commentCount)+")",1),oe(R)?(r(),p("div",nt,[l(P,{modelValue:$.value,"onUpdate:modelValue":t[0]||(t[0]=Z=>$.value=Z),type:"textarea",rows:3,placeholder:"发表您的评论...",maxlength:"500","show-word-limit":""},null,8,["modelValue"]),o("div",at,[l(q,{type:"primary",disabled:!$.value.trim(),onClick:F},{default:m(()=>t[4]||(t[4]=[x(" 发表评论 ")])),_:1,__:[4]},8,["disabled"])])])):(r(),p("div",st,[l(G,{to:"/login"},{default:m(()=>t[5]||(t[5]=[x("登录")])),_:1,__:[5]}),t[6]||(t[6]=x(" 后参与评论 "))])),ce((r(),p("div",lt,[S.value.length>0?(r(),p(te,{key:0},[(r(!0),p(te,null,pe(S.value,Z=>(r(),J(Ge,{key:`${Z.id}-${M.value}`,comment:Z,onReply:z,onDelete:K},null,8,["comment"]))),128)),u.value>L.value?(r(),p("div",rt,[l(fe,{background:"",layout:"prev, pager, next",total:u.value,"page-size":L.value,"current-page":k.value,onCurrentChange:Q},null,8,["total","page-size","current-page"])])):B("",!0)],64)):(r(),J(le,{key:1,description:"暂无评论"}))])),[[re,N.value]])])],64)):E.value?B("",!0):(r(),J(le,{key:1,description:"资料不存在或已被删除"}))])),[[re,E.value]])}}},pt=ve(ct,[["__scopeId","data-v-96fca01a"]]);export{pt as default};
