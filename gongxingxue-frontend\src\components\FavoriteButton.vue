<template>
  <el-button
    :type="isFavorited ? 'warning' : 'default'"
    :icon="isFavorited ? StarFilled : Star"
    :loading="loading"
    :size="size"
    @click="handleToggle"
    class="favorite-button"
  >
    {{ isFavorited ? '已收藏' : '收藏' }}
    <span v-if="showCount && favoriteCount > 0" class="count">
      ({{ favoriteCount }})
    </span>
  </el-button>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted } from 'vue'
import { Star, StarFilled } from '@element-plus/icons-vue'
import { toggleFavorite, checkFavorited } from '../api/favorite'
import { ElMessage } from 'element-plus'
import { useUserStore } from '../store/user'
import { useFavorites } from '../composables/useFavorites'

const props = defineProps({
  resourceId: {
    type: Number,
    required: true
  },
  initialFavorited: {
    type: Boolean,
    default: false
  },
  favoriteCount: {
    type: Number,
    default: 0
  },
  showCount: {
    type: Boolean,
    default: true
  },
  size: {
    type: String,
    default: 'default'
  }
})

const emit = defineEmits(['update:favorited', 'update:count'])

const userStore = useUserStore()
const { toggleFavoriteState, onFavoriteChange } = useFavorites()
const loading = ref(false)
const isFavorited = ref(props.initialFavorited)

// Watch for prop changes
watch(() => props.initialFavorited, (newValue) => {
  isFavorited.value = newValue
})

// Handle toggle favorite
const handleToggle = async () => {
  // Check if user is logged in
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录后再收藏')
    return
  }

  loading.value = true
  try {
    const response = await toggleFavorite(props.resourceId)
    if (response.success) {
      isFavorited.value = response.data
      ElMessage.success(response.message)

      // Update global favorite state
      toggleFavoriteState(props.resourceId, response.data, {
        id: props.resourceId,
        favoriteCount: props.favoriteCount
      })

      // Emit events to parent
      emit('update:favorited', response.data)

      // Update count (increment or decrement)
      const newCount = response.data
        ? props.favoriteCount + 1
        : Math.max(0, props.favoriteCount - 1)
      emit('update:count', newCount)
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('Toggle favorite error:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// Check favorite status on mount (if user is logged in)
const checkFavoriteStatus = async () => {
  // 确保用户已登录且有token
  if (!userStore.isLoggedIn || !userStore.token) {
    isFavorited.value = false
    return
  }

  try {
    const response = await checkFavorited(props.resourceId)
    if (response.success) {
      isFavorited.value = response.data
      emit('update:favorited', response.data)
    }
  } catch (error) {
    console.error('Check favorite status error:', error)
    // 如果是401错误，说明token无效，设置为未收藏状态
    if (error.response?.status === 401) {
      isFavorited.value = false
    }
  }
}

// Listen to global favorite changes
const unsubscribe = onFavoriteChange((resourceId, favorited) => {
  if (resourceId === props.resourceId) {
    isFavorited.value = favorited
    emit('update:favorited', favorited)
  }
})

// Check status when component mounts or user login status changes
watch(() => userStore.isLoggedIn, (isLoggedIn, oldValue) => {
  if (isLoggedIn && userStore.token) {
    // 只有在登录状态真正改变时才检查，避免初始化时的无效调用
    if (oldValue !== undefined) {
      checkFavoriteStatus()
    }
  } else {
    isFavorited.value = false
  }
}, { immediate: true })

// 在组件挂载后检查收藏状态（如果已登录）
onMounted(() => {
  if (userStore.isLoggedIn && userStore.token) {
    checkFavoriteStatus()
  }
})

// Cleanup on unmount
onUnmounted(() => {
  unsubscribe()
})
</script>

<style lang="scss" scoped>
.favorite-button {
  .count {
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.8;
  }

  &:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
  }
}
</style>
