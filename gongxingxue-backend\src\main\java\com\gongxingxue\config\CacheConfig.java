package com.gongxingxue.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * 缓存配置类
 *
 * 使用简单的内存缓存，适合单机部署
 * 优势：
 * 1. 无需额外依赖（不需要Redis）
 * 2. 配置简单，立即可用
 * 3. 性能提升明显
 * 4. 适合中小型应用
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * 配置缓存管理器
     *
     * 使用ConcurrentMapCacheManager：
     * - 基于ConcurrentHashMap实现
     * - 线程安全
     * - 适合单机应用
     * - 重启后缓存会丢失（这是正常的）
     */
    @Bean
    public CacheManager cacheManager() {
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager();

        // 预定义缓存名称
        cacheManager.setCacheNames(Arrays.asList(
            "resourceList",    // 资源列表缓存
            "users",          // 用户信息缓存
            "hotResources",   // 热门资源缓存
            "statistics"      // 统计信息缓存
        ));

        // 允许动态创建缓存
        cacheManager.setAllowNullValues(false);

        return cacheManager;
    }
}
