package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.StudyPlan;
import com.gongxingxue.entity.StudyTask;
import com.gongxingxue.mapper.StudyPlanMapper;
import com.gongxingxue.mapper.StudyTaskMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Study plan service
 */
@Service
@RequiredArgsConstructor
public class StudyPlanService extends ServiceImpl<StudyPlanMapper, StudyPlan> {

    private final StudyTaskMapper studyTaskMapper;

    /**
     * Create study plan
     */
    public StudyPlan createStudyPlan(String name, String description, Integer planType, 
                                     LocalDateTime startDate, LocalDateTime endDate, Long userId) {
        StudyPlan plan = new StudyPlan()
                .setName(name)
                .setDescription(description)
                .setPlanType(planType)
                .setStartDate(startDate)
                .setEndDate(endDate)
                .setUserId(userId)
                .setCreateTime(LocalDateTime.now());
        
        // Save plan
        save(plan);
        
        return plan;
    }

    /**
     * Get study plans by user ID
     */
    public List<StudyPlan> getStudyPlansByUserId(Long userId) {
        LambdaQueryWrapper<StudyPlan> queryWrapper = new LambdaQueryWrapper<>();
        
        // Filter by user ID
        queryWrapper.eq(StudyPlan::getUserId, userId);
        
        // Sort by creation time
        queryWrapper.orderByDesc(StudyPlan::getCreateTime);
        
        return list(queryWrapper);
    }

    /**
     * Get study plan by ID
     */
    public StudyPlan getStudyPlanById(Long planId, Long userId) {
        LambdaQueryWrapper<StudyPlan> queryWrapper = new LambdaQueryWrapper<>();
        
        // Filter by ID and user ID
        queryWrapper.eq(StudyPlan::getId, planId);
        queryWrapper.eq(StudyPlan::getUserId, userId);
        
        return getOne(queryWrapper);
    }

    /**
     * Update study plan
     */
    public boolean updateStudyPlan(Long planId, String name, String description, Integer planType, 
                                  LocalDateTime startDate, LocalDateTime endDate, Long userId) {
        StudyPlan plan = getStudyPlanById(planId, userId);
        if (plan == null) {
            return false;
        }
        
        plan.setName(name);
        plan.setDescription(description);
        plan.setPlanType(planType);
        plan.setStartDate(startDate);
        plan.setEndDate(endDate);
        plan.setUpdateTime(LocalDateTime.now());
        
        return updateById(plan);
    }

    /**
     * Delete study plan
     */
    @Transactional
    public boolean deleteStudyPlan(Long planId, Long userId) {
        StudyPlan plan = getStudyPlanById(planId, userId);
        if (plan == null) {
            return false;
        }
        
        // Delete tasks
        LambdaQueryWrapper<StudyTask> taskQueryWrapper = new LambdaQueryWrapper<>();
        taskQueryWrapper.eq(StudyTask::getPlanId, planId);
        studyTaskMapper.delete(taskQueryWrapper);
        
        // Delete plan
        return removeById(planId);
    }

    /**
     * Add task to plan
     */
    public StudyTask addTask(Long planId, String name, String description, Long resourceId, 
                            LocalDateTime scheduledDate, Long userId) {
        // Check if plan exists and belongs to user
        StudyPlan plan = getStudyPlanById(planId, userId);
        if (plan == null) {
            throw new RuntimeException("Study plan not found or access denied");
        }
        
        StudyTask task = new StudyTask()
                .setPlanId(planId)
                .setName(name)
                .setDescription(description)
                .setResourceId(resourceId)
                .setScheduledDate(scheduledDate)
                .setStatus(0) // Not completed
                .setCreateTime(LocalDateTime.now());
        
        // Save task
        studyTaskMapper.insert(task);
        
        return task;
    }

    /**
     * Get tasks by plan ID
     */
    public List<StudyTask> getTasksByPlanId(Long planId, Long userId) {
        // Check if plan exists and belongs to user
        StudyPlan plan = getStudyPlanById(planId, userId);
        if (plan == null) {
            throw new RuntimeException("Study plan not found or access denied");
        }
        
        LambdaQueryWrapper<StudyTask> queryWrapper = new LambdaQueryWrapper<>();
        
        // Filter by plan ID
        queryWrapper.eq(StudyTask::getPlanId, planId);
        
        // Sort by scheduled date
        queryWrapper.orderByAsc(StudyTask::getScheduledDate);
        
        return studyTaskMapper.selectList(queryWrapper);
    }

    /**
     * Complete task
     */
    public boolean completeTask(Long taskId, Long userId) {
        StudyTask task = studyTaskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }
        
        // Check if plan belongs to user
        StudyPlan plan = getStudyPlanById(task.getPlanId(), userId);
        if (plan == null) {
            return false;
        }
        
        task.setStatus(1); // Completed
        task.setCompletionTime(LocalDateTime.now());
        
        return studyTaskMapper.updateById(task) > 0;
    }

    /**
     * Delete task
     */
    public boolean deleteTask(Long taskId, Long userId) {
        StudyTask task = studyTaskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }
        
        // Check if plan belongs to user
        StudyPlan plan = getStudyPlanById(task.getPlanId(), userId);
        if (plan == null) {
            return false;
        }
        
        return studyTaskMapper.deleteById(taskId) > 0;
    }
}
