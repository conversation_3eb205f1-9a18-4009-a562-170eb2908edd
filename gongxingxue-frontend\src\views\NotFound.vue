<template>
  <div class="not-found">
    <div class="not-found-container">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <el-icon :size="120" color="#409EFF">
            <DocumentDelete />
          </el-icon>
        </div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        <p class="error-suggestion">
          您可以：
        </p>
        <ul class="suggestion-list">
          <li>检查网址是否正确</li>
          <li>返回首页重新开始</li>
          <li>使用搜索功能查找资料</li>
        </ul>
        
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button size="large" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
          <el-button size="large" @click="goSearch">
            <el-icon><Search /></el-icon>
            搜索资料
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { DocumentDelete, House, ArrowLeft, Search } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const goSearch = () => {
  router.push('/search')
}
</script>

<style lang="scss" scoped>
.not-found {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;

  .not-found-container {
    text-align: center;
    max-width: 600px;
    width: 100%;
  }

  .error-illustration {
    margin-bottom: 40px;
    position: relative;

    .error-code {
      font-size: 120px;
      font-weight: bold;
      color: #409EFF;
      opacity: 0.1;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
    }

    .error-icon {
      position: relative;
      z-index: 2;
    }
  }

  .error-content {
    .error-title {
      font-size: 32px;
      color: #303133;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .error-description {
      font-size: 16px;
      color: #606266;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .error-suggestion {
      font-size: 14px;
      color: #909399;
      margin-bottom: 12px;
      text-align: left;
      max-width: 300px;
      margin-left: auto;
      margin-right: auto;
    }

    .suggestion-list {
      text-align: left;
      max-width: 300px;
      margin: 0 auto 32px;
      padding-left: 20px;

      li {
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;

      .el-button {
        min-width: 120px;
      }
    }
  }
}

@media (max-width: 768px) {
  .not-found {
    .error-illustration {
      .error-code {
        font-size: 80px;
      }
    }

    .error-content {
      .error-title {
        font-size: 24px;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 200px;
        }
      }
    }
  }
}
</style>
