<template>
  <div class="forgot-password-page">
    <div class="forgot-password-container">
      <h2 class="title">找回密码</h2>

      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-width="0"
        @submit.prevent="handleSendResetEmail"
      >
        <el-form-item prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入绑定的邮箱地址"
            prefix-icon="el-icon-message"
            :disabled="loading"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            class="reset-button"
            @click="handleSendResetEmail"
          >
            发送重置邮件
          </el-button>
        </el-form-item>
      </el-form>

      <div class="forgot-password-footer">
        <div>
          <span>记起密码了？</span>
          <router-link to="/login">返回登录</router-link>
        </div>
        <div style="margin-top: 10px;">
          <span>还没有账号？</span>
          <router-link to="/register">立即注册</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { sendPasswordResetEmail } from '../api/auth'

const router = useRouter()

// Form data
const forgotPasswordForm = reactive({
  email: ''
})

// Form validation rules
const forgotPasswordRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// Form ref
const forgotPasswordFormRef = ref(null)

// Loading state
const loading = ref(false)

// Handle send reset email
const handleSendResetEmail = async () => {
  if (!forgotPasswordFormRef.value) return

  await forgotPasswordFormRef.value.validate(async (valid) => {
    if (!valid) return

    loading.value = true

    try {
      await sendPasswordResetEmail(forgotPasswordForm.email)
      
      ElMessage.success('密码重置邮件已发送，请查收邮箱')
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 3000)
    } catch (error) {
      console.error('Send reset email failed:', error)
      ElMessage.error(error.message || '发送重置邮件失败，请稍后重试')
    } finally {
      loading.value = false
    }
  })
}
</script>

<style lang="scss" scoped>
.forgot-password-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 60px - 60px); /* Subtract header and footer height */

  .forgot-password-container {
    width: 100%;
    max-width: 400px;
    padding: 30px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .title {
      text-align: center;
      margin-bottom: 30px;
      color: #303133;
    }

    .reset-button {
      width: 100%;
    }

    .forgot-password-footer {
      margin-top: 20px;
      text-align: center;
      font-size: 14px;
      color: #606266;

      a {
        color: #409eff;
        margin-left: 5px;
      }
    }
  }
}
</style>
