package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.Resource;
import com.gongxingxue.entity.User;
import com.gongxingxue.entity.UserFavorite;
import com.gongxingxue.mapper.UserFavoriteMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * User favorite service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserFavoriteService extends ServiceImpl<UserFavoriteMapper, UserFavorite> {

    private final ResourceService resourceService;
    private final UserService userService;

    /**
     * 添加收藏
     *
     * 收藏功能的复杂逻辑处理：
     * 1. 防重复收藏：检查用户是否已经收藏了该资料
     * 2. 资料状态验证：只能收藏已审核通过的资料
     * 3. 软删除恢复：如果之前收藏过但被删除，则恢复收藏记录
     * 4. 计数同步：更新资料的收藏计数
     *
     * 设计考虑：
     * - 使用软删除而不是物理删除，便于数据恢复和统计分析
     * - 通过事务确保数据一致性
     * - 绕过逻辑删除过滤器来处理已删除的记录
     *
     * @param userId 用户ID
     * @param resourceId 资料ID
     * @return true表示收藏成功，false表示收藏失败（已收藏或资料不存在）
     */
    @Transactional
    public boolean addFavorite(Long userId, Long resourceId) {
        // 第一步：检查是否已经收藏（只检查未删除的记录）
        if (isFavorited(userId, resourceId)) {
            log.debug("用户{}已经收藏了资料{}，无需重复收藏", userId, resourceId);
            return false;
        }

        // 第二步：验证资料是否存在且已审核通过
        Resource resource = resourceService.getById(resourceId);
        if (resource == null || resource.getAuditStatus() != 1) {
            log.warn("收藏失败：资料{}不存在或未审核通过，状态：{}",
                resourceId, resource != null ? resource.getAuditStatus() : "null");
            return false;
        }

        // 第三步：检查是否存在已删除的收藏记录
        // 使用原生查询绕过MyBatis-Plus的逻辑删除过滤器
        UserFavorite existingFavorite = baseMapper.findByUserIdAndResourceId(userId, resourceId);

        boolean result;
        if (existingFavorite != null && existingFavorite.getDeleted() == 1) {
            // 情况1：存在已删除的收藏记录，恢复该记录
            // 这样做的好处是保持数据的连续性，避免主键冲突
            log.debug("发现用户{}对资料{}的已删除收藏记录，准备恢复", userId, resourceId);
            int updateCount = baseMapper.restoreFavorite(existingFavorite.getId(), LocalDateTime.now());
            result = updateCount > 0;
        } else {
            // 情况2：不存在收藏记录，创建新记录
            UserFavorite favorite = new UserFavorite()
                    .setUserId(userId)
                    .setResourceId(resourceId)
                    .setCreateTime(LocalDateTime.now())
                    .setDeleted(0);

            result = save(favorite);
            log.debug("为用户{}创建资料{}的新收藏记录", userId, resourceId);
        }

        // 第四步：更新资料的收藏计数
        // 只有收藏操作成功后才更新计数，确保数据一致性
        if (result) {
            updateResourceFavoriteCount(resourceId);
            log.info("用户{}成功收藏资料{}", userId, resourceId);
        }

        return result;
    }

    /**
     * Remove favorite
     */
    @Transactional
    public boolean removeFavorite(Long userId, Long resourceId) {
        LambdaQueryWrapper<UserFavorite> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserFavorite::getUserId, userId)
                   .eq(UserFavorite::getResourceId, resourceId)
                   .eq(UserFavorite::getDeleted, 0);

        UserFavorite favorite = getOne(queryWrapper);

        if (favorite == null) {
            return false;
        }

        // Soft delete using update wrapper to ensure deleted field is updated
        LambdaUpdateWrapper<UserFavorite> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserFavorite::getId, favorite.getId())
                    .set(UserFavorite::getDeleted, 1);

        boolean result = update(updateWrapper);

        // Update favorite count in resource table
        if (result) {
            updateResourceFavoriteCount(resourceId);
        }

        return result;
    }

    /**
     * Toggle favorite status
     * @return true if now favorited, false if now unfavorited
     */
    @Transactional
    public boolean toggleFavorite(Long userId, Long resourceId) {
        if (isFavorited(userId, resourceId)) {
            removeFavorite(userId, resourceId);
            return false; // Now unfavorited
        } else {
            addFavorite(userId, resourceId);
            return true;  // Now favorited
        }
    }

    /**
     * Check if user has favorited the resource
     */
    public boolean isFavorited(Long userId, Long resourceId) {
        return baseMapper.isFavorited(userId, resourceId);
    }

    /**
     * Get user's favorite resources with pagination
     */
    public Page<Resource> getUserFavorites(Long userId, Integer page, Integer size) {
        // First, get all valid favorite records (without pagination)
        LambdaQueryWrapper<UserFavorite> allQueryWrapper = new LambdaQueryWrapper<>();
        allQueryWrapper.eq(UserFavorite::getUserId, userId)
                      .eq(UserFavorite::getDeleted, 0)
                      .orderByDesc(UserFavorite::getCreateTime);

        List<UserFavorite> allFavorites = list(allQueryWrapper);

        // Filter out favorites that point to deleted or non-approved resources
        List<Resource> validResources = allFavorites.stream()
                .map(favorite -> {
                    Resource resource = resourceService.getById(favorite.getResourceId());
                    if (resource != null && resource.getAuditStatus() == 1) {
                        // Load user information
                        User user = userService.getUserById(resource.getUserId());
                        if (user != null) {
                            resource.setUsername(user.getUsername());
                        }
                        return resource;
                    }
                    return null;
                })
                .filter(resource -> resource != null)
                .collect(Collectors.toList());

        // Apply pagination to valid resources
        int total = validResources.size();
        int start = (page - 1) * size;
        int end = Math.min(start + size, total);

        List<Resource> pagedResources;
        if (start >= total) {
            // If start index is beyond total, return empty list
            pagedResources = new ArrayList<>();
        } else {
            pagedResources = validResources.subList(start, end);
        }

        // Create result page
        Page<Resource> result = new Page<>(page, size);
        result.setRecords(pagedResources);
        result.setTotal(total);
        result.setPages((long) Math.ceil((double) total / size));

        return result;
    }

    /**
     * Update resource favorite count
     */
    private void updateResourceFavoriteCount(Long resourceId) {
        int count = baseMapper.getFavoriteCount(resourceId);
        Resource resource = resourceService.getById(resourceId);
        if (resource != null) {
            resource.setFavoriteCount(count);
            resourceService.updateById(resource);
        }
    }
}
