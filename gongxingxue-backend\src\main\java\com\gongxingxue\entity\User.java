package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * User entity
 */
@Data
@Accessors(chain = true)
@TableName("user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Username
     */
    private String username;

    /**
     * Password (encrypted)
     */
    private String password;

    /**
     * Nickname
     */
    private String nickname;

    /**
     * Avatar URL
     */
    private String avatar;

    /**
     * Email
     */
    private String email;

    /**
     * Phone number
     */
    private String phone;

    /**
     * User role: 0=regular user, 1=admin
     */
    private Integer role;

    /**
     * Registration IP
     */
    private String registerIp;

    /**
     * Last login IP
     */
    private String lastLoginIp;

    /**
     * Last login time
     */
    private LocalDateTime lastLoginTime;

    /**
     * Account status: 0=normal, 1=disabled
     */
    private Integer status;

    /**
     * Creation time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * Logical delete flag: 0=not deleted, 1=deleted
     */
    @TableLogic
    private Integer deleted;
}
