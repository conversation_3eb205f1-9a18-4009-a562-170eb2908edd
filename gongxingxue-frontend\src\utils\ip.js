/**
 * IP地址格式化工具函数
 */

/**
 * 格式化IP地址显示
 * @param {string} ip - 原始IP地址
 * @param {boolean} showLocalLabel - 是否显示本地访问标签，默认true
 * @returns {string} 格式化后的IP地址
 */
export function formatIpAddress(ip, showLocalLabel = true) {
  if (!ip) {
    return '未知'
  }
  
  // 处理IPv6本地回环地址
  if (ip === '0:0:0:0:0:0:0:1' || ip === '::1') {
    return showLocalLabel ? '127.0.0.1 (本地访问)' : '127.0.0.1'
  }
  
  // 处理IPv4本地回环地址
  if (ip === '127.0.0.1' || ip === 'localhost') {
    return showLocalLabel ? '127.0.0.1 (本地访问)' : '127.0.0.1'
  }
  
  // 处理多个IP的情况（通过代理时可能出现）
  if (ip.includes(',')) {
    ip = ip.split(',')[0].trim()
  }
  
  return ip
}

/**
 * 判断是否为本地IP地址
 * @param {string} ip - IP地址
 * @returns {boolean} 是否为本地IP
 */
export function isLocalIp(ip) {
  if (!ip) return false
  
  return ip === '0:0:0:0:0:0:0:1' || 
         ip === '::1' || 
         ip === '127.0.0.1' || 
         ip === 'localhost'
}

/**
 * 获取IP地址类型
 * @param {string} ip - IP地址
 * @returns {string} IP地址类型：'ipv4', 'ipv6', 'local', 'unknown'
 */
export function getIpType(ip) {
  if (!ip) return 'unknown'
  
  // 本地地址
  if (isLocalIp(ip)) return 'local'
  
  // IPv4地址
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/
  if (ipv4Regex.test(ip)) return 'ipv4'
  
  // IPv6地址
  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
  if (ipv6Regex.test(ip)) return 'ipv6'
  
  return 'unknown'
}

/**
 * 格式化IP地址用于显示（带图标）
 * @param {string} ip - 原始IP地址
 * @returns {object} 包含格式化IP和类型信息的对象
 */
export function formatIpWithIcon(ip) {
  const formattedIp = formatIpAddress(ip)
  const type = getIpType(ip)
  
  let icon = ''
  let color = ''
  
  switch (type) {
    case 'local':
      icon = '🏠'
      color = '#909399'
      break
    case 'ipv4':
      icon = '🌐'
      color = '#409eff'
      break
    case 'ipv6':
      icon = '🌍'
      color = '#67c23a'
      break
    default:
      icon = '❓'
      color = '#f56c6c'
  }
  
  return {
    ip: formattedIp,
    type,
    icon,
    color
  }
}
