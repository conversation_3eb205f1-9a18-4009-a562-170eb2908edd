package com.gongxingxue.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gongxingxue.common.Result;
import com.gongxingxue.entity.Comment;
import com.gongxingxue.service.CommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 评论管理控制器
 */
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
@Api(tags = "评论管理", description = "学习资料评论相关接口")
public class CommentController {

    private final CommentService commentService;



    /**
     * 获取资料的评论列表
     */
    @GetMapping("/resource/{resourceId}")
    @ApiOperation(value = "获取资料评论", notes = "分页获取指定资料的所有评论，包括顶级评论和回复")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回分页的评论列表"),
        @ApiResponse(code = 404, message = "资料不存在")
    })
    public Result<Page<Comment>> getCommentsByResourceId(
            @ApiParam(value = "资料ID", required = true, example = "1")
            @PathVariable Long resourceId,

            @ApiParam(value = "页码", required = false, example = "1")
            @RequestParam(defaultValue = "1") Integer page,

            @ApiParam(value = "每页数量", required = false, example = "10")
            @RequestParam(defaultValue = "10") Integer size) {

        System.out.println("Getting comments for resource: " + resourceId);
        System.out.println("Page: " + page + ", Size: " + size);

        try {
            Page<Comment> comments = commentService.getCommentsByResourceId(resourceId, page, size);
            return Result.success(comments);
        } catch (Exception e) {
            System.err.println("Error getting comments: " + e.getMessage());
            e.printStackTrace();
            return Result.error("Failed to get comments: " + e.getMessage());
        }
    }

    /**
     * 添加评论
     */
    @PostMapping
    @ApiOperation(value = "添加评论", notes = "为指定资料添加评论或回复其他评论，需要用户登录")
    @ApiResponses({
        @ApiResponse(code = 200, message = "评论添加成功，返回评论信息"),
        @ApiResponse(code = 400, message = "参数错误或评论内容为空"),
        @ApiResponse(code = 401, message = "用户未登录")
    })
    public Result<Comment> addComment(
            @ApiParam(value = "评论请求信息", required = true)
            @RequestBody CommentRequest request,
            HttpServletRequest httpRequest) {



        // Get user ID from request attributes (set by auth interceptor)
        Long userId = (Long) httpRequest.getAttribute("userId");
        if (userId == null) {
            return Result.error("User not authenticated");
        }

        try {
            Comment comment = commentService.addComment(
                request.getResourceId(),
                userId,
                request.getContent(),
                request.getParentId()
            );
            return Result.success(comment);
        } catch (Exception e) {
            System.err.println("Error adding comment: " + e.getMessage());
            e.printStackTrace();
            return Result.error("Failed to add comment: " + e.getMessage());
        }
    }

    /**
     * 评论请求数据传输对象
     */
    @ApiModel(description = "评论请求信息")
    public static class CommentRequest {
        @ApiModelProperty(value = "资料ID", required = true, example = "1")
        private Long resourceId;

        @ApiModelProperty(value = "评论内容", required = true, example = "这个资料很有用！")
        private String content;

        @ApiModelProperty(value = "父评论ID（回复评论时使用）", required = false, example = "2")
        private Long parentId;

        // Getters and setters
        public Long getResourceId() { return resourceId; }
        public void setResourceId(Long resourceId) { this.resourceId = resourceId; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public Long getParentId() { return parentId; }
        public void setParentId(Long parentId) { this.parentId = parentId; }
    }

    /**
     * Get replies by parent comment ID
     */
    @GetMapping("/replies/{parentId}")
    public Result<List<Comment>> getRepliesByParentId(@PathVariable Long parentId) {
        System.out.println("Getting replies for parent comment: " + parentId);

        try {
            List<Comment> replies = commentService.getRepliesByParentId(parentId);
            return Result.success(replies);
        } catch (Exception e) {
            System.err.println("Error getting replies: " + e.getMessage());
            e.printStackTrace();
            return Result.error("Failed to get replies: " + e.getMessage());
        }
    }

    /**
     * Get my comments
     */
    @GetMapping("/my")
    public Result<Page<Comment>> getMyComments(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            HttpServletRequest request) {

        System.out.println("Getting my comments - Page: " + page + ", Size: " + size);

        // Get user ID from request attributes (set by auth interceptor)
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("User not authenticated");
        }

        try {
            Page<Comment> comments = commentService.getCommentsByUserId(userId, page, size);
            return Result.success(comments);
        } catch (Exception e) {
            System.err.println("Error getting my comments: " + e.getMessage());
            e.printStackTrace();
            return Result.error("Failed to get my comments: " + e.getMessage());
        }
    }

    /**
     * Delete comment
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteComment(@PathVariable Long id, HttpServletRequest request) {
        System.out.println("Deleting comment: " + id);

        // Get user ID from request attributes (set by auth interceptor)
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("User not authenticated");
        }

        try {
            boolean success = commentService.deleteComment(id, userId);
            if (success) {
                return Result.success("Comment deleted successfully");
            } else {
                return Result.error("Failed to delete comment or comment not found");
            }
        } catch (Exception e) {
            System.err.println("Error deleting comment: " + e.getMessage());
            e.printStackTrace();
            return Result.error("Failed to delete comment: " + e.getMessage());
        }
    }
}
