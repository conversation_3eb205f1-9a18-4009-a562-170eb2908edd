package com.gongxingxue.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Verification code service
 */
@Service
@Slf4j
public class VerificationCodeService {

    // 存储验证码的Map，key为邮箱，value为验证码信息
    private final Map<String, VerificationCodeInfo> codeStorage = new ConcurrentHashMap<>();

    /**
     * Store verification code
     */
    public void storeCode(String email, String code, int expireMinutes) {
        LocalDateTime expireTime = LocalDateTime.now().plusMinutes(expireMinutes);
        VerificationCodeInfo info = new VerificationCodeInfo(code, expireTime);
        codeStorage.put(email, info);
        log.info("Verification code stored for email: {}", email);
    }

    /**
     * Verify code
     */
    public boolean verifyCode(String email, String inputCode) {
        VerificationCodeInfo info = codeStorage.get(email);
        
        if (info == null) {
            log.warn("No verification code found for email: {}", email);
            return false;
        }

        // Check if expired
        if (LocalDateTime.now().isAfter(info.getExpireTime())) {
            codeStorage.remove(email);
            log.warn("Verification code expired for email: {}", email);
            return false;
        }

        // Check if code matches
        boolean isValid = info.getCode().equals(inputCode);
        
        if (isValid) {
            // Remove code after successful verification
            codeStorage.remove(email);
            log.info("Verification code verified successfully for email: {}", email);
        } else {
            log.warn("Invalid verification code for email: {}", email);
        }

        return isValid;
    }

    /**
     * Remove expired codes (cleanup task)
     */
    public void cleanupExpiredCodes() {
        LocalDateTime now = LocalDateTime.now();
        codeStorage.entrySet().removeIf(entry -> {
            boolean expired = now.isAfter(entry.getValue().getExpireTime());
            if (expired) {
                log.debug("Removed expired verification code for email: {}", entry.getKey());
            }
            return expired;
        });
    }

    /**
     * Check if code exists for email
     */
    public boolean hasValidCode(String email) {
        VerificationCodeInfo info = codeStorage.get(email);
        if (info == null) {
            return false;
        }
        
        // Check if expired
        if (LocalDateTime.now().isAfter(info.getExpireTime())) {
            codeStorage.remove(email);
            return false;
        }
        
        return true;
    }

    /**
     * Get all keys (for token verification)
     */
    public java.util.Set<String> getAllKeys() {
        return codeStorage.keySet();
    }

    /**
     * Get stored code by key
     */
    public String getStoredCode(String key) {
        VerificationCodeInfo info = codeStorage.get(key);
        if (info != null && LocalDateTime.now().isBefore(info.getExpireTime())) {
            return info.getCode();
        }
        return null;
    }

    /**
     * Find email by reset token
     */
    public String findEmailByResetToken(String token) {
        for (Map.Entry<String, VerificationCodeInfo> entry : codeStorage.entrySet()) {
            String key = entry.getKey();
            VerificationCodeInfo info = entry.getValue();

            if (key.startsWith("reset_") &&
                info.getCode().equals(token) &&
                LocalDateTime.now().isBefore(info.getExpireTime())) {
                // 提取邮箱地址（去掉"reset_"前缀）
                return key.substring(6);
            }
        }
        return null;
    }

    /**
     * Verification code information
     */
    private static class VerificationCodeInfo {
        private final String code;
        private final LocalDateTime expireTime;

        public VerificationCodeInfo(String code, LocalDateTime expireTime) {
            this.code = code;
            this.expireTime = expireTime;
        }

        public String getCode() {
            return code;
        }

        public LocalDateTime getExpireTime() {
            return expireTime;
        }
    }
}
