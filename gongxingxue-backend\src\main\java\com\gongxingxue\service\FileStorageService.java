package com.gongxingxue.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * File storage service
 */
@Slf4j
@Service
public class FileStorageService {

    private final Path fileStorageLocation;

    /**
     * Constructor
     */
    public FileStorageService(@Value("${file.upload-dir}") String uploadDir) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (IOException ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    /**
     * Store file
     * 增强版本：添加了更好的日志和错误处理
     */
    public String storeFile(MultipartFile file) {
        log.info("存储文件到目录: {}", this.fileStorageLocation.toAbsolutePath());

        // Normalize file name
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        log.debug("原始文件名: {}", originalFilename);

        // Generate unique filename
        String fileExtension = "";
        if (originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        String filename = UUID.randomUUID().toString() + fileExtension;
        log.debug("生成的文件名: {}", filename);

        try {
            // Check if the filename contains invalid characters
            if (filename.contains("..")) {
                throw new RuntimeException("文件名包含非法路径序列: " + filename);
            }

            // Check if directory exists
            if (!Files.exists(this.fileStorageLocation)) {
                log.info("创建存储目录: {}", this.fileStorageLocation);
                Files.createDirectories(this.fileStorageLocation);
            }

            // Copy file to the target location
            Path targetLocation = this.fileStorageLocation.resolve(filename);
            log.debug("目标存储位置: {}", targetLocation.toAbsolutePath());

            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            log.info("文件存储成功: {}", targetLocation.toAbsolutePath());

            // 检查存储空间
            checkStorageSpace();

            return filename;
        } catch (IOException ex) {
            log.error("文件存储失败: {}", ex.getMessage(), ex);
            throw new RuntimeException("无法存储文件 " + filename + "，请重试！", ex);
        }
    }

    /**
     * Load file as resource
     */
    public Resource loadFileAsResource(String filename) {
        try {
            Path filePath = this.fileStorageLocation.resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                return resource;
            } else {
                throw new RuntimeException("文件不存在: " + filename);
            }
        } catch (MalformedURLException ex) {
            throw new RuntimeException("文件不存在: " + filename, ex);
        }
    }

    /**
     * Delete file
     */
    public boolean deleteFile(String filename) {
        try {
            Path filePath = this.fileStorageLocation.resolve(filename).normalize();
            return Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            throw new RuntimeException("删除文件失败: " + filename, ex);
        }
    }

    /**
     * 检查存储空间
     * 简单的存储空间监控，当空间不足时发出警告
     */
    private void checkStorageSpace() {
        try {
            // 获取存储目录所在的文件系统信息
            Path storageRoot = this.fileStorageLocation.getRoot();
            if (storageRoot == null) {
                storageRoot = this.fileStorageLocation;
            }

            // 获取可用空间（字节）
            long freeSpace = Files.getFileStore(storageRoot).getUsableSpace();
            long totalSpace = Files.getFileStore(storageRoot).getTotalSpace();

            // 转换为GB
            double freeSpaceGB = freeSpace / (1024.0 * 1024.0 * 1024.0);
            double totalSpaceGB = totalSpace / (1024.0 * 1024.0 * 1024.0);
            double usagePercent = ((totalSpace - freeSpace) * 100.0) / totalSpace;

            log.info("存储空间: {:.1f}GB/{:.1f}GB (使用率: {:.1f}%)",
                    totalSpaceGB - freeSpaceGB, totalSpaceGB, usagePercent);

            // 空间不足警告
            if (freeSpaceGB < 1.0) { // 少于1GB
                log.warn("存储空间不足1GB，请及时清理或扩容！");
            } else if (usagePercent > 90) { // 使用率超过90%
                log.warn("存储空间使用率超过90%，建议及时清理！");
            }

        } catch (IOException ex) {
            log.error("无法检查存储空间: {}", ex.getMessage());
        }
    }

    /**
     * 获取存储统计信息
     * 提供给管理员查看的存储状态
     */
    public String getStorageStats() {
        try {
            Path storageRoot = this.fileStorageLocation.getRoot();
            if (storageRoot == null) {
                storageRoot = this.fileStorageLocation;
            }

            long freeSpace = Files.getFileStore(storageRoot).getUsableSpace();
            long totalSpace = Files.getFileStore(storageRoot).getTotalSpace();
            long usedSpace = totalSpace - freeSpace;

            // 计算上传目录中的文件数量和大小
            long fileCount = 0;
            long uploadDirSize = 0;

            if (Files.exists(this.fileStorageLocation)) {
                fileCount = Files.list(this.fileStorageLocation).count();
                uploadDirSize = Files.walk(this.fileStorageLocation)
                    .filter(Files::isRegularFile)
                    .mapToLong(path -> {
                        try {
                            return Files.size(path);
                        } catch (IOException e) {
                            return 0;
                        }
                    })
                    .sum();
            }

            return String.format(
                "存储统计:\n" +
                "- 上传目录: %s\n" +
                "- 文件数量: %d 个\n" +
                "- 上传目录大小: %.2f MB\n" +
                "- 磁盘总空间: %.1f GB\n" +
                "- 磁盘已用空间: %.1f GB\n" +
                "- 磁盘可用空间: %.1f GB\n" +
                "- 磁盘使用率: %.1f%%",
                this.fileStorageLocation.toAbsolutePath(),
                fileCount,
                uploadDirSize / (1024.0 * 1024.0),
                totalSpace / (1024.0 * 1024.0 * 1024.0),
                usedSpace / (1024.0 * 1024.0 * 1024.0),
                freeSpace / (1024.0 * 1024.0 * 1024.0),
                (usedSpace * 100.0) / totalSpace
            );

        } catch (IOException ex) {
            return "无法获取存储统计信息: " + ex.getMessage();
        }
    }
}
