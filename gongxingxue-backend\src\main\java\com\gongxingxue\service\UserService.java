package com.gongxingxue.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gongxingxue.entity.User;
import com.gongxingxue.mapper.UserMapper;
import com.gongxingxue.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * User service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserService extends ServiceImpl<UserMapper, User> {

    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;

    /**
     * Register a new user
     */
    public User register(String username, String password, String nickname, String registerIp) {
        // Check if username already exists
        if (usernameExists(username)) {
            throw new RuntimeException("用户名已存在");
        }

        // Create new user
        User user = new User()
                .setUsername(username)
                .setPassword(encryptPassword(password))
                .setNickname(nickname != null ? nickname : username)
                .setRole(0) // Regular user
                .setRegisterIp(registerIp)
                .setStatus(0) // Normal status
                .setCreateTime(LocalDateTime.now());

        // Save user
        save(user);

        return user;
    }

    /**
     * Login
     */
    public Map<String, Object> login(String username, String password, String loginIp) {
        // Find user by username
        User user = getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));

        // Check if user exists
        if (user == null) {
            throw new RuntimeException("用户名或密码错误");
        }

        // Check if password is correct
        if (!verifyPassword(password, user.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // Check if user is disabled
        if (user.getStatus() != 0) {
            throw new RuntimeException("账户已被禁用");
        }

        // Update last login info
        user.setLastLoginIp(loginIp);
        user.setLastLoginTime(LocalDateTime.now());
        updateById(user);

        // Generate JWT token
        String token = jwtUtil.generateToken(user.getUsername(), user.getId(), user.getRole());

        // Create response
        Map<String, Object> response = new HashMap<>();
        response.put("token", token);
        response.put("user", user);

        return response;
    }

    /**
     * Get user by ID
     * 缓存策略：用户信息缓存30分钟，减少重复查询
     */
    @Cacheable(value = "users", key = "'user_' + #userId")
    public User getUserById(Long userId) {
        return getById(userId);
    }

    /**
     * Update user profile
     * 缓存策略：更新后清除用户缓存，确保数据一致性
     */
    @CacheEvict(value = "users", key = "'user_' + #userId")
    public boolean updateProfile(Long userId, String nickname, String avatar, String email, String phone) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }

        user.setNickname(nickname);
        user.setAvatar(avatar);
        user.setEmail(email);
        user.setPhone(phone);

        return updateById(user);
    }

    /**
     * Change password
     */
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }

        // Verify old password
        if (!verifyPassword(oldPassword, user.getPassword())) {
            throw new RuntimeException("原密码错误");
        }

        // Update password
        user.setPassword(encryptPassword(newPassword));

        return updateById(user);
    }

    /**
     * Get user list with pagination
     */
    public Page<User> getUserList(String username, LocalDateTime startTime, LocalDateTime endTime, Integer page, Integer size) {
        Page<User> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // Add conditions
        if (username != null && !username.isEmpty()) {
            queryWrapper.like(User::getUsername, username);
        }

        if (startTime != null && endTime != null) {
            queryWrapper.between(User::getCreateTime, startTime, endTime);
        } else if (startTime != null) {
            queryWrapper.ge(User::getCreateTime, startTime);
        } else if (endTime != null) {
            queryWrapper.le(User::getCreateTime, endTime);
        }

        // Sort by creation time
        queryWrapper.orderByDesc(User::getCreateTime);

        return page(pageParam, queryWrapper);
    }

    /**
     * Update user status
     */
    public boolean updateUserStatus(Long userId, Integer status) {
        User user = getById(userId);
        if (user == null) {
            return false;
        }

        user.setStatus(status);

        return updateById(user);
    }

    /**
     * Check if username exists
     */
    private boolean usernameExists(String username) {
        return count(new LambdaQueryWrapper<User>().eq(User::getUsername, username)) > 0;
    }

    /**
     * Encrypt password using BCrypt
     * BCrypt自动生成盐值，相同密码每次加密结果都不同，更安全
     */
    private String encryptPassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * Verify password - 兼容MD5和BCrypt
     * 为了平滑升级，同时支持旧的MD5密码和新的BCrypt密码
     */
    private boolean verifyPassword(String rawPassword, String encryptedPassword) {
        // 检查是否是BCrypt格式（以$2a$开头）
        if (encryptedPassword.startsWith("$2a$") || encryptedPassword.startsWith("$2b$") || encryptedPassword.startsWith("$2y$")) {
            // 使用BCrypt验证
            return passwordEncoder.matches(rawPassword, encryptedPassword);
        } else {
            // 兼容旧的MD5密码
            String md5Hash = org.springframework.util.DigestUtils.md5DigestAsHex(rawPassword.getBytes());
            boolean isValid = md5Hash.equals(encryptedPassword);

            // 如果MD5验证成功，自动升级为BCrypt
            if (isValid) {
                // 注意：这里需要在事务中更新密码
                upgradePasswordToBCrypt(rawPassword, encryptedPassword);
            }

            return isValid;
        }
    }

    /**
     * 将MD5密码升级为BCrypt
     * 在用户登录时自动升级密码加密方式
     */
    private void upgradePasswordToBCrypt(String rawPassword, String oldEncryptedPassword) {
        try {
            // 查找使用旧密码的用户
            User user = getOne(new LambdaQueryWrapper<User>().eq(User::getPassword, oldEncryptedPassword));
            if (user != null) {
                // 使用BCrypt重新加密密码
                String newEncryptedPassword = passwordEncoder.encode(rawPassword);
                user.setPassword(newEncryptedPassword);
                updateById(user);

                log.info("用户 {} 的密码已自动升级为BCrypt加密", user.getUsername());
            }
        } catch (Exception e) {
            log.error("升级用户密码加密方式失败", e);
            // 不抛出异常，避免影响登录流程
        }
    }

    /**
     * Check if email is already bound to another user
     */
    public boolean isEmailBound(String email, Long currentUserId) {
        User existingUser = getOne(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email)
                .ne(User::getId, currentUserId));
        return existingUser != null;
    }

    /**
     * Bind email to user
     * 缓存策略：绑定后清除用户缓存，确保数据一致性
     */
    @CacheEvict(value = "users", key = "'user_' + #userId")
    public boolean bindEmail(Long userId, String email) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // Check if email is already bound to another user
        if (isEmailBound(email, userId)) {
            throw new RuntimeException("该邮箱已被其他用户绑定");
        }

        user.setEmail(email);
        return updateById(user);
    }

    /**
     * Unbind email from user
     * 缓存策略：解绑后清除用户缓存，确保数据一致性
     */
    @CacheEvict(value = "users", key = "'user_' + #userId")
    public boolean unbindEmail(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // Use update wrapper to explicitly set email to null
        return update(new LambdaUpdateWrapper<User>()
                .eq(User::getId, userId)
                .set(User::getEmail, null));
    }

    /**
     * Find user by email
     */
    public User findByEmail(String email) {
        return getOne(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, email));
    }

    /**
     * Reset user password
     */
    public boolean resetPassword(Long userId, String newPassword) {
        User user = getById(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 加密新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        user.setPassword(encodedPassword);

        return updateById(user);
    }
}
