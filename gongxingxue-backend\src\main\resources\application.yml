server:
  port: 8081
  servlet:
    context-path: /api

spring:
  application:
    name: gongxingxue-backend

  # 环境配置 - 通过环境变量或启动参数指定
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

  # File Upload Configuration
  servlet:
    multipart:
      enabled: true
      max-file-size: 30MB
      max-request-size: 30MB

  # Jackson Configuration
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # MVC Configuration - Fix for Swagger with Spring Boot 2.7.x
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER



# MyBatis-Plus Configuration
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.gongxingxue.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 注意：敏感配置已移至各环境配置文件中
# 请使用 application-dev.yml, application-test.yml, application-prod.yml
