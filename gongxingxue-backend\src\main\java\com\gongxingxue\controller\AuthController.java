package com.gongxingxue.controller;

import com.gongxingxue.common.Result;
import com.gongxingxue.entity.User;
import com.gongxingxue.service.EmailService;
import com.gongxingxue.service.UserService;
import com.gongxingxue.service.VerificationCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.CrossOrigin;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.Map;
import java.util.Random;

/**
 * Authentication controller
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Api(tags = "Authentication API")
public class AuthController {

    private final UserService userService;
    private final EmailService emailService;
    private final VerificationCodeService verificationCodeService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @ApiOperation(value = "用户注册", notes = "注册新用户账户，需要提供用户名、密码、昵称和验证码")
    @ApiResponses({
        @ApiResponse(code = 200, message = "注册成功"),
        @ApiResponse(code = 400, message = "参数错误、用户名已存在或验证码错误")
    })
    public Result<Void> register(
            @ApiParam(value = "用户名（长度6-20个字符）", required = true, example = "testuser")
            @RequestParam @NotBlank @Size(min = 6, max = 20, message = "用户名长度必须在6-20个字符之间") String username,

            @ApiParam(value = "密码（长度8-16个字符，必须包含字母和数字）", required = true, example = "password123")
            @RequestParam @NotBlank @Size(min = 8, max = 16, message = "密码长度必须在8-16个字符之间")
            @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).+$", message = "密码必须包含字母和数字") String password,

            @ApiParam(value = "昵称", required = false, example = "测试用户")
            @RequestParam(required = false) String nickname,

            @ApiParam(value = "验证码", required = true, example = "ABCD")
            @RequestParam @NotBlank String captcha,

            HttpServletRequest request) {

        try {
            // Validate captcha
            String sessionCaptcha = (String) request.getSession().getAttribute("captcha");
            if (sessionCaptcha == null || !sessionCaptcha.equalsIgnoreCase(captcha)) {
                return Result.error("验证码错误");
            }

            // Clear captcha from session (prevent reuse)
            request.getSession().removeAttribute("captcha");

            // Get client IP
            String ip = getClientIp(request);

            // Register user
            userService.register(username, password, nickname, ip);

            return Result.success("注册成功", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 生成验证码图片
     */
    @GetMapping("/captcha")
    @CrossOrigin(origins = "*")
    @ApiOperation(value = "生成验证码", notes = "生成4位随机字符的图形验证码，用于登录和注册时的人机验证")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功生成验证码图片，返回PNG格式图片")
    })
    public void getCaptcha(HttpServletRequest request, HttpServletResponse response)
            throws IOException {

        // Generate random 4-character code
        String code = generateRandomCode();

        // Store in session
        request.getSession().setAttribute("captcha", code);

        // Create captcha image
        BufferedImage image = createCaptchaImage(code);

        // Set response headers
        response.setContentType("image/png");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("Expires", 0);

        // Add CORS headers
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "*");

        // Output image
        ImageIO.write(image, "png", response.getOutputStream());
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @ApiOperation(value = "用户登录", notes = "使用用户名、密码和验证码进行登录认证")
    @ApiResponses({
        @ApiResponse(code = 200, message = "登录成功，返回用户信息和JWT token"),
        @ApiResponse(code = 400, message = "参数错误或验证码错误"),
        @ApiResponse(code = 401, message = "用户名或密码错误")
    })
    public Result<Map<String, Object>> login(
            @ApiParam(value = "用户名", required = true, example = "admin")
            @RequestParam @NotBlank String username,

            @ApiParam(value = "密码", required = true, example = "123456")
            @RequestParam @NotBlank String password,

            @ApiParam(value = "验证码", required = true, example = "ABCD")
            @RequestParam @NotBlank String captcha,

            HttpServletRequest request) {

        try {
            // Validate captcha
            String sessionCaptcha = (String) request.getSession().getAttribute("captcha");
            if (sessionCaptcha == null || !sessionCaptcha.equalsIgnoreCase(captcha)) {
                return Result.error("验证码错误");
            }

            // Clear captcha from session (prevent reuse)
            request.getSession().removeAttribute("captcha");

            // Get client IP
            String ip = getClientIp(request);

            // Login
            Map<String, Object> result = userService.login(username, password, ip);

            return Result.success("登录成功", result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Generate random captcha code
     */
    private String generateRandomCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < 4; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }

        return code.toString();
    }

    /**
     * Create captcha image
     */
    private BufferedImage createCaptchaImage(String code) {
        int width = 120;
        int height = 40;

        // Create image
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();

        // Set rendering hints for better quality
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        // Set background
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);

        // Set font
        g.setFont(new Font("Arial", Font.BOLD, 20));

        Random random = new Random();

        // Draw characters
        for (int i = 0; i < code.length(); i++) {
            // Random color for each character
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));

            // Random position with slight variation
            int x = 20 + i * 20 + random.nextInt(10);
            int y = 25 + random.nextInt(10);

            // Draw character
            g.drawString(String.valueOf(code.charAt(i)), x, y);
        }

        // Add noise lines
        for (int i = 0; i < 5; i++) {
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            int x1 = random.nextInt(width);
            int y1 = random.nextInt(height);
            int x2 = random.nextInt(width);
            int y2 = random.nextInt(height);
            g.drawLine(x1, y1, x2, y2);
        }

        // Add noise dots
        for (int i = 0; i < 20; i++) {
            g.setColor(new Color(random.nextInt(255), random.nextInt(255), random.nextInt(255)));
            int x = random.nextInt(width);
            int y = random.nextInt(height);
            g.fillOval(x, y, 2, 2);
        }

        g.dispose();
        return image;
    }

    /**
     * Get client IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 格式化IP地址显示
        return formatIpAddress(ip);
    }

    /**
     * 格式化IP地址显示
     */
    private String formatIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return "未知";
        }

        // 处理IPv6本地回环地址
        if ("0:0:0:0:0:0:0:1".equals(ip) || "::1".equals(ip)) {
            return "127.0.0.1"; // 本地访问
        }

        // 处理IPv4本地回环地址
        if ("127.0.0.1".equals(ip) || "localhost".equals(ip)) {
            return "127.0.0.1"; // 本地访问
        }

        // 处理多个IP的情况（通过代理时可能出现）
        if (ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /**
     * Send password reset email
     */
    @PostMapping("/forgot-password")
    @ApiOperation("Send password reset email")
    public Result<Void> forgotPassword(@RequestParam @NotBlank @Email String email) {
        try {
            // Check if user exists with this email
            User user = userService.findByEmail(email);
            if (user == null) {
                return Result.error("该邮箱未绑定任何账户");
            }

            // Generate reset token (use verification code service for simplicity)
            String resetToken = emailService.generateVerificationCode() +
                               emailService.generateVerificationCode(); // 12位重置令牌

            // Store reset token with 30 minutes expiration
            verificationCodeService.storeCode("reset_" + email, resetToken, 30);

            // Send password reset email
            emailService.sendPasswordResetEmail(email, resetToken);

            return Result.success("密码重置邮件已发送，请查收邮箱", null);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * Verify reset token
     */
    @GetMapping("/verify-reset-token")
    @ApiOperation("Verify password reset token")
    public Result<Void> verifyResetToken(@RequestParam @NotBlank String token) {
        try {
            // 查找所有可能的重置token
            boolean tokenValid = false;

            // 遍历所有存储的重置token，查找匹配的
            // 这里使用简单的方式，实际生产环境可能需要更复杂的token管理
            for (String key : verificationCodeService.getAllKeys()) {
                if (key.startsWith("reset_")) {
                    String storedToken = verificationCodeService.getStoredCode(key);
                    if (token.equals(storedToken)) {
                        tokenValid = true;
                        break;
                    }
                }
            }

            if (tokenValid) {
                return Result.success("Token验证成功", null);
            } else {
                return Result.error("Token无效或已过期");
            }
        } catch (Exception e) {
            return Result.error("Token验证失败");
        }
    }

    /**
     * Reset password
     */
    @PostMapping("/reset-password")
    @ApiOperation("Reset password with token")
    public Result<Void> resetPassword(
            @RequestParam @NotBlank String token,
            @RequestParam @NotBlank @Size(min = 8, max = 16, message = "Password must be 8-16 characters")
            @Pattern(regexp = "^(?=.*[a-zA-Z])(?=.*\\d).+$", message = "Password must contain both letters and numbers") String newPassword) {

        try {
            // 通过token查找对应的邮箱
            String email = verificationCodeService.findEmailByResetToken(token);
            if (email == null) {
                return Result.error("重置令牌无效或已过期");
            }

            // 查找用户
            User user = userService.findByEmail(email);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 重置密码
            boolean success = userService.resetPassword(user.getId(), newPassword);
            if (success) {
                // 清除重置令牌
                verificationCodeService.verifyCode("reset_" + email, token);
                return Result.success("密码重置成功", null);
            } else {
                return Result.error("密码重置失败");
            }
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}
