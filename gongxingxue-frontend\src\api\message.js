import request from '../utils/request'

/**
 * Get user messages
 * @param {number} page - Page number
 * @param {number} size - Page size
 * @param {string} type - Message type filter ('unread' for unread only)
 * @returns {Promise} - API response
 */
export function getMessages(page = 1, size = 10, type = null) {
  const params = { page, size }
  if (type) {
    params.type = type
  }
  
  return request({
    url: '/messages',
    method: 'get',
    params
  })
}

/**
 * Get unread message count
 * @returns {Promise} - API response
 */
export function getUnreadCount() {
  return request({
    url: '/messages/unread-count',
    method: 'get'
  })
}

/**
 * Mark message as read
 * @param {number} id - Message ID
 * @returns {Promise} - API response
 */
export function markAsRead(id) {
  return request({
    url: `/messages/${id}/read`,
    method: 'post'
  })
}

/**
 * Mark all messages as read
 * @returns {Promise} - API response
 */
export function markAllAsRead() {
  return request({
    url: '/messages/read-all',
    method: 'post'
  })
}
