<template>
  <div class="server-error">
    <div class="error-container">
      <div class="error-illustration">
        <div class="error-code">500</div>
        <div class="error-icon">
          <el-icon :size="120" color="#F56C6C">
            <Warning />
          </el-icon>
        </div>
      </div>
      
      <div class="error-content">
        <h1 class="error-title">服务器错误</h1>
        <p class="error-description">
          抱歉，服务器遇到了一些问题，我们正在努力修复中。
        </p>
        <p class="error-suggestion">
          您可以：
        </p>
        <ul class="suggestion-list">
          <li>稍后再试</li>
          <li>刷新页面</li>
          <li>返回首页</li>
          <li>如果问题持续存在，请联系管理员</li>
        </ul>
        
        <div class="action-buttons">
          <el-button type="primary" size="large" @click="refresh">
            <el-icon><Refresh /></el-icon>
            刷新页面
          </el-button>
          <el-button size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          <el-button size="large" @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
        </div>
        
        <div class="error-details" v-if="errorMessage">
          <el-collapse>
            <el-collapse-item title="错误详情" name="1">
              <pre class="error-message">{{ errorMessage }}</pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Warning, Refresh, House, ArrowLeft } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

// 从路由参数获取错误信息
const errorMessage = ref(route.query.error || '')

const refresh = () => {
  window.location.reload()
}

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>

<style lang="scss" scoped>
.server-error {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  padding: 20px;

  .error-container {
    text-align: center;
    max-width: 600px;
    width: 100%;
  }

  .error-illustration {
    margin-bottom: 40px;
    position: relative;

    .error-code {
      font-size: 120px;
      font-weight: bold;
      color: #F56C6C;
      opacity: 0.1;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
    }

    .error-icon {
      position: relative;
      z-index: 2;
    }
  }

  .error-content {
    .error-title {
      font-size: 32px;
      color: #303133;
      margin-bottom: 16px;
      font-weight: 600;
    }

    .error-description {
      font-size: 16px;
      color: #606266;
      margin-bottom: 24px;
      line-height: 1.6;
    }

    .error-suggestion {
      font-size: 14px;
      color: #909399;
      margin-bottom: 12px;
      text-align: left;
      max-width: 350px;
      margin-left: auto;
      margin-right: auto;
    }

    .suggestion-list {
      text-align: left;
      max-width: 350px;
      margin: 0 auto 32px;
      padding-left: 20px;

      li {
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
        line-height: 1.5;
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 32px;

      .el-button {
        min-width: 120px;
      }
    }

    .error-details {
      max-width: 500px;
      margin: 0 auto;
      text-align: left;

      .error-message {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 4px;
        font-size: 12px;
        color: #666;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
}

@media (max-width: 768px) {
  .server-error {
    .error-illustration {
      .error-code {
        font-size: 80px;
      }
    }

    .error-content {
      .error-title {
        font-size: 24px;
      }

      .action-buttons {
        flex-direction: column;
        align-items: center;

        .el-button {
          width: 200px;
        }
      }
    }
  }
}
</style>
