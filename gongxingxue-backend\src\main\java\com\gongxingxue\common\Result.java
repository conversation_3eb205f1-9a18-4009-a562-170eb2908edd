package com.gongxingxue.common;

import lombok.Data;

import java.io.Serializable;

/**
 * Common API response class
 */
@Data
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Status code
     */
    private Integer code;

    /**
     * Message
     */
    private String message;

    /**
     * Data
     */
    private T data;

    /**
     * Success flag
     */
    private boolean success;

    /**
     * Timestamp
     */
    private long timestamp;

    /**
     * Private constructor
     */
    private Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * Success response with data
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    /**
     * Success response with message and data
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage(message);
        result.setData(data);
        result.setSuccess(true);
        return result;
    }

    /**
     * Error response with message
     */
    public static <T> Result<T> error(String message) {
        return error(500, message);
    }

    /**
     * Error response with code and message
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        result.setSuccess(false);
        return result;
    }
}
