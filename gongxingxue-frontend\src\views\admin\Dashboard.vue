<template>
  <div class="admin-dashboard">
    <h2 class="page-title">管理后台</h2>

    <div class="dashboard-cards">
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <span>待审核资料</span>
                <el-button type="primary" size="small" @click="$router.push('/admin/resources')">
                  查看详情
                </el-button>
              </div>
            </template>
            <div class="card-content" v-loading="loadingPending">
              <div class="stat-number">{{ pendingCount }}</div>
              <div class="stat-label">个资料待审核</div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <span>用户总数</span>
                <el-button type="primary" size="small" @click="$router.push('/admin/users')">
                  查看详情
                </el-button>
              </div>
            </template>
            <div class="card-content" v-loading="loadingUsers">
              <div class="stat-number">{{ userCount }}</div>
              <div class="stat-label">个注册用户</div>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <el-card class="dashboard-card">
            <template #header>
              <div class="card-header">
                <span>资料总数</span>
                <el-button type="primary" size="small" @click="$router.push('/admin/resources')">
                  查看详情
                </el-button>
              </div>
            </template>
            <div class="card-content" v-loading="loadingResources">
              <div class="stat-number">{{ resourceCount }}</div>
              <div class="stat-label">个已审核资料</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="recent-resources">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>最近待审核资料</span>
            <el-button type="primary" size="small" @click="$router.push('/admin/resources')">
              查看全部
            </el-button>
          </div>
        </template>
        <div v-loading="loadingRecentPending">
          <el-table :data="recentPendingResources" style="width: 100%">
            <el-table-column prop="name" label="资料名称" min-width="200" />
            <el-table-column prop="examType" label="考试类型" width="100">
              <template #default="{ row }">
                <el-tag
                  :type="getExamTypeTag(row.examType)"
                  :class="{ 'custom-purple': row.examType === 4 }"
                >
                  {{ getExamTypeText(row.examType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button type="success" size="small" @click="handleApprove(row)">
                  通过
                </el-button>
                <el-button type="danger" size="small" @click="handleReject(row)">
                  驳回
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-empty v-if="recentPendingResources.length === 0" description="暂无待审核资料" />
        </div>
      </el-card>
    </div>

    <!-- Reject Dialog -->
    <el-dialog v-model="rejectDialogVisible" title="驳回资料" width="500px">
      <el-form>
        <el-form-item label="驳回原因" required>
          <el-input
            v-model="rejectReason"
            type="textarea"
            :rows="3"
            placeholder="请输入驳回原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="rejectDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="confirmReject">
          确认驳回
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPendingResources, approveResource, rejectResource } from '../../api/admin'
import { getUserList } from '../../api/admin'
import { getPublicResources } from '../../api/resource'
import { formatDate } from '../../utils/auth'

// Data
const pendingCount = ref(0)
const userCount = ref(0)
const resourceCount = ref(0)
const recentPendingResources = ref([])

// Loading states
const loadingPending = ref(false)
const loadingUsers = ref(false)
const loadingResources = ref(false)
const loadingRecentPending = ref(false)
const submitting = ref(false)

// Reject dialog
const rejectDialogVisible = ref(false)
const rejectReason = ref('')
const currentResource = ref(null)

// Fetch pending resources count
const fetchPendingCount = async () => {
  loadingPending.value = true
  try {
    const response = await getPendingResources({ page: 1, size: 1 })
    if (response.success) {
      pendingCount.value = response.data.total
    }
  } catch (error) {
    console.error('Failed to fetch pending count:', error)
    pendingCount.value = 0
  } finally {
    loadingPending.value = false
  }
}

// Fetch user count
const fetchUserCount = async () => {
  loadingUsers.value = true
  try {
    const response = await getUserList({ page: 1, size: 1 })
    if (response.success) {
      userCount.value = response.data.total
    }
  } catch (error) {
    console.error('Failed to fetch user count:', error)
    userCount.value = 0
  } finally {
    loadingUsers.value = false
  }
}

// Fetch resource count
const fetchResourceCount = async () => {
  loadingResources.value = true
  try {
    const response = await getPublicResources({ page: 1, size: 1 })
    if (response.success) {
      resourceCount.value = response.data.total
    }
  } catch (error) {
    console.error('Failed to fetch resource count:', error)
    resourceCount.value = 0
  } finally {
    loadingResources.value = false
  }
}

// Fetch recent pending resources
const fetchRecentPendingResources = async () => {
  loadingRecentPending.value = true
  try {
    const response = await getPendingResources({ page: 1, size: 5 })
    if (response.success) {
      recentPendingResources.value = response.data.records
    }
  } catch (error) {
    console.error('Failed to fetch recent pending resources:', error)
    recentPendingResources.value = []
  } finally {
    loadingRecentPending.value = false
  }
}

// Get exam type text
const getExamTypeText = (examType) => {
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return examTypes[examType] || '未知'
}

// Get exam type tag type
const getExamTypeTag = (examType) => {
  const tagTypes = ['primary', 'success', 'warning', 'danger', '']
  return tagTypes[examType] || ''
}

// Handle approve
const handleApprove = async (resource) => {
  try {
    await ElMessageBox.confirm(
      `确定要通过资料 '${resource.name}' 吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    await approveResource(resource.id)
    ElMessage.success('资料已通过审核')

    // Refresh data
    fetchPendingCount()
    fetchResourceCount()
    fetchRecentPendingResources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to approve resource:', error)
      ElMessage.error('操作失败')
    }
  }
}

// Handle reject
const handleReject = (resource) => {
  currentResource.value = resource
  rejectReason.value = ''
  rejectDialogVisible.value = true
}

// Confirm reject
const confirmReject = async () => {
  if (!rejectReason.value.trim()) {
    ElMessage.warning('请输入驳回原因')
    return
  }

  submitting.value = true
  try {
    await rejectResource(currentResource.value.id, rejectReason.value)
    ElMessage.success('资料已驳回')
    rejectDialogVisible.value = false

    // Refresh data
    fetchPendingCount()
    fetchRecentPendingResources()
  } catch (error) {
    console.error('Failed to reject resource:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// Fetch data on component mount
onMounted(() => {
  fetchPendingCount()
  fetchUserCount()
  fetchResourceCount()
  fetchRecentPendingResources()
})
</script>

<style lang="scss" scoped>
.admin-dashboard {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .dashboard-cards {
    margin-bottom: 20px;

    .dashboard-card {
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-content {
        text-align: center;
        padding: 20px 0;

        .stat-number {
          font-size: 36px;
          font-weight: 600;
          color: #409eff;
          margin-bottom: 10px;
        }

        .stat-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .recent-resources {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }

  // 自定义紫色标签样式 - 用于"其他"类型，模仿Element Plus的设计
  :deep(.el-tag) {
    &.custom-purple {
      background-color: #f4f0ff;
      border: 1px solid #d4c5f9;
      color: #7c3aed;

      // 保持与Element Plus标签一致的样式
      border-radius: 4px;
      font-size: 12px;
      line-height: 1;
      white-space: nowrap;

      // 悬停效果
      &:hover {
        background-color: #ede9fe;
        border-color: #c4b5fd;
      }
    }
  }
}
</style>
