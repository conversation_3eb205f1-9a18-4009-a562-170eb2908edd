import{x as C,r as w,p as B,o as T,B as E,b as P,d as u,h as q,e as s,g as a,f as r,y as F,k as M,l as _,m as g,z as N,C as $,E as b}from"./index-BCDD51NH.js";import{_ as z}from"./_plugin-vue_export-helper-DlAUqK2U.js";const K={class:"reset-password-page"},U={class:"reset-password-container"},A={key:0,class:"error-message"},I={style:{"margin-top":"20px","text-align":"center"}},S={class:"reset-password-footer"},Z={__name:"ResetPassword",setup(j){const k=M(),x=B(),o=C({newPassword:"",confirmPassword:""}),R={newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:8,max:16,message:"密码长度必须在8-16个字符之间",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d).+$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(t,e,d)=>{e!==o.newPassword?d(new Error("两次输入的密码不一致")):d()},trigger:"blur"}]},p=w(null),l=w(!1),n=w(!0),m=x.query.token;T(async()=>{if(!m){n.value=!1;return}try{await E(m),n.value=!0}catch(t){console.error("Token verification failed:",t),n.value=!1}});const c=async()=>{p.value&&await p.value.validate(async t=>{if(t){l.value=!0;try{await $(m,o.newPassword),b.success("密码重置成功，请使用新密码登录"),setTimeout(()=>{k.push("/login")},2e3)}catch(e){console.error("Reset password failed:",e),b.error(e.message||"密码重置失败，请稍后重试")}finally{l.value=!1}}})};return(t,e)=>{const d=a("el-alert"),v=a("el-button"),y=a("el-input"),f=a("el-form-item"),V=a("el-form"),h=a("router-link");return _(),P("div",K,[u("div",U,[e[6]||(e[6]=u("h2",{class:"title"},"重置密码",-1)),n.value?(_(),q(V,{key:1,ref_key:"resetPasswordFormRef",ref:p,model:o,rules:R,"label-width":"0",onSubmit:F(c,["prevent"])},{default:r(()=>[s(f,{prop:"newPassword"},{default:r(()=>[s(y,{modelValue:o.newPassword,"onUpdate:modelValue":e[1]||(e[1]=i=>o.newPassword=i),type:"password",placeholder:"请输入新密码","prefix-icon":"el-icon-lock","show-password":"",disabled:l.value},null,8,["modelValue","disabled"])]),_:1}),s(f,{prop:"confirmPassword"},{default:r(()=>[s(y,{modelValue:o.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=i=>o.confirmPassword=i),type:"password",placeholder:"请确认新密码","prefix-icon":"el-icon-lock","show-password":"",disabled:l.value,onKeyup:N(c,["enter"])},null,8,["modelValue","disabled"])]),_:1}),s(f,null,{default:r(()=>[s(v,{type:"primary",loading:l.value,class:"reset-button",onClick:c},{default:r(()=>e[4]||(e[4]=[g(" 重置密码 ")])),_:1,__:[4]},8,["loading"])]),_:1})]),_:1},8,["model"])):(_(),P("div",A,[s(d,{title:"链接无效或已过期",type:"error",description:"密码重置链接无效或已过期，请重新申请密码重置。","show-icon":"",closable:!1}),u("div",I,[s(v,{type:"primary",onClick:e[0]||(e[0]=i=>t.$router.push("/forgot-password"))},{default:r(()=>e[3]||(e[3]=[g(" 重新申请 ")])),_:1,__:[3]})])])),u("div",S,[s(h,{to:"/login"},{default:r(()=>e[5]||(e[5]=[g("返回登录")])),_:1,__:[5]})])])])}}},J=z(Z,[["__scopeId","data-v-6bb2898f"]]);export{J as default};
