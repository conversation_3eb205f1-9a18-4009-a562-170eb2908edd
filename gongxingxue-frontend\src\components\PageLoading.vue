<template>
  <div class="page-loading">
    <div class="loading-content">
      <!-- 骨架屏效果 -->
      <div class="skeleton-container">
        <div class="skeleton-header">
          <div class="skeleton-line skeleton-title"></div>
          <div class="skeleton-line skeleton-subtitle"></div>
        </div>
        
        <div class="skeleton-body">
          <div class="skeleton-card" v-for="i in 3" :key="i">
            <div class="skeleton-line skeleton-card-title"></div>
            <div class="skeleton-line skeleton-card-content"></div>
            <div class="skeleton-line skeleton-card-content short"></div>
          </div>
        </div>
      </div>
      
      <!-- 加载指示器 -->
      <div class="loading-indicator">
        <LoadingSpinner size="medium" :text="loadingText" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LoadingSpinner from './LoadingSpinner.vue'

const props = defineProps({
  text: {
    type: String,
    default: '加载中...'
  }
})

const loadingText = ref(props.text)

// 模拟加载文本变化
const loadingTexts = [
  '加载中...',
  '正在获取数据...',
  '即将完成...'
]

let textIndex = 0
onMounted(() => {
  const interval = setInterval(() => {
    textIndex = (textIndex + 1) % loadingTexts.length
    loadingText.value = loadingTexts[textIndex]
  }, 1500)
  
  // 清理定时器
  setTimeout(() => {
    clearInterval(interval)
  }, 10000)
})
</script>

<style lang="scss" scoped>
.page-loading {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-content {
  width: 100%;
  max-width: 800px;
  position: relative;
}

.skeleton-container {
  opacity: 0.3;
}

.skeleton-header {
  margin-bottom: 32px;
  text-align: center;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 12px;
  
  &.skeleton-title {
    height: 32px;
    width: 60%;
    margin: 0 auto 16px;
  }
  
  &.skeleton-subtitle {
    height: 20px;
    width: 40%;
    margin: 0 auto;
  }
}

.skeleton-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.skeleton-card {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  
  .skeleton-card-title {
    height: 24px;
    width: 80%;
    margin-bottom: 16px;
  }
  
  .skeleton-card-content {
    height: 16px;
    width: 100%;
    
    &.short {
      width: 60%;
    }
  }
}

.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@media (max-width: 768px) {
  .skeleton-body {
    grid-template-columns: 1fr;
  }
  
  .loading-indicator {
    padding: 24px;
  }
}
</style>
