import{g as E,a as W,b as X,r as Y}from"./admin-BR7V4uEf.js";import{g as Z}from"./resource-Dv4iJ0T9.js";import{r as n,o as ee,b as m,d as o,e as a,f as s,g as r,s as te,l as i,q as C,t as f,m as d,h as se,i as ae,H as le,j as oe,G as ne,D as re,E as v}from"./index-BwbJZous.js";import{_ as de}from"./_plugin-vue_export-helper-DlAUqK2U.js";const ue={class:"admin-dashboard"},ie={class:"dashboard-cards"},ce={class:"card-header"},pe={class:"card-content"},me={class:"stat-number"},fe={class:"card-header"},ve={class:"card-content"},_e={class:"stat-number"},ge={class:"card-header"},ye={class:"card-content"},be={class:"stat-number"},Ce={class:"recent-resources"},xe={class:"card-header"},we={__name:"Dashboard",setup(Te){const x=n(0),w=n(0),T=n(0),_=n([]),h=n(!1),k=n(!1),R=n(!1),$=n(!1),z=n(!1),c=n(!1),p=n(""),B=n(null),V=async()=>{h.value=!0;try{const t=await E({page:1,size:1});t.success&&(x.value=t.data.total)}catch(t){console.error("Failed to fetch pending count:",t),x.value=0}finally{h.value=!1}},F=async()=>{k.value=!0;try{const t=await W({page:1,size:1});t.success&&(w.value=t.data.total)}catch(t){console.error("Failed to fetch user count:",t),w.value=0}finally{k.value=!1}},P=async()=>{R.value=!0;try{const t=await Z({page:1,size:1});t.success&&(T.value=t.data.total)}catch(t){console.error("Failed to fetch resource count:",t),T.value=0}finally{R.value=!1}},D=async()=>{$.value=!0;try{const t=await E({page:1,size:5});t.success&&(_.value=t.data.records)}catch(t){console.error("Failed to fetch recent pending resources:",t),_.value=[]}finally{$.value=!1}},U=t=>["考研","考公","法考","教资","其他"][t]||"未知",N=t=>["primary","success","warning","danger",""][t]||"",M=async t=>{try{await re.confirm(`确定要通过资料 '${t.name}' 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),await X(t.id),v.success("资料已通过审核"),V(),P(),D()}catch(e){e!=="cancel"&&(console.error("Failed to approve resource:",e),v.error("操作失败"))}},q=t=>{B.value=t,p.value="",c.value=!0},A=async()=>{if(!p.value.trim()){v.warning("请输入驳回原因");return}z.value=!0;try{await Y(B.value.id,p.value),v.success("资料已驳回"),c.value=!1,V(),D()}catch(t){console.error("Failed to reject resource:",t),v.error("操作失败")}finally{z.value=!1}};return ee(()=>{V(),F(),P(),D()}),(t,e)=>{const u=r("el-button"),g=r("el-card"),j=r("el-col"),G=r("el-row"),y=r("el-table-column"),H=r("el-tag"),I=r("el-table"),L=r("el-empty"),S=r("el-input"),J=r("el-form-item"),K=r("el-form"),O=r("el-dialog"),b=te("loading");return i(),m("div",ue,[e[22]||(e[22]=o("h2",{class:"page-title"},"管理后台",-1)),o("div",ie,[a(G,{gutter:20},{default:s(()=>[a(j,{xs:24,sm:12,md:8},{default:s(()=>[a(g,{class:"dashboard-card"},{header:s(()=>[o("div",ce,[e[8]||(e[8]=o("span",null,"待审核资料",-1)),a(u,{type:"primary",size:"small",onClick:e[0]||(e[0]=l=>t.$router.push("/admin/resources"))},{default:s(()=>e[7]||(e[7]=[d(" 查看详情 ")])),_:1,__:[7]})])]),default:s(()=>[C((i(),m("div",pe,[o("div",me,f(x.value),1),e[9]||(e[9]=o("div",{class:"stat-label"},"个资料待审核",-1))])),[[b,h.value]])]),_:1})]),_:1}),a(j,{xs:24,sm:12,md:8},{default:s(()=>[a(g,{class:"dashboard-card"},{header:s(()=>[o("div",fe,[e[11]||(e[11]=o("span",null,"用户总数",-1)),a(u,{type:"primary",size:"small",onClick:e[1]||(e[1]=l=>t.$router.push("/admin/users"))},{default:s(()=>e[10]||(e[10]=[d(" 查看详情 ")])),_:1,__:[10]})])]),default:s(()=>[C((i(),m("div",ve,[o("div",_e,f(w.value),1),e[12]||(e[12]=o("div",{class:"stat-label"},"个注册用户",-1))])),[[b,k.value]])]),_:1})]),_:1}),a(j,{xs:24,sm:12,md:8},{default:s(()=>[a(g,{class:"dashboard-card"},{header:s(()=>[o("div",ge,[e[14]||(e[14]=o("span",null,"资料总数",-1)),a(u,{type:"primary",size:"small",onClick:e[2]||(e[2]=l=>t.$router.push("/admin/resources"))},{default:s(()=>e[13]||(e[13]=[d(" 查看详情 ")])),_:1,__:[13]})])]),default:s(()=>[C((i(),m("div",ye,[o("div",be,f(T.value),1),e[15]||(e[15]=o("div",{class:"stat-label"},"个已审核资料",-1))])),[[b,R.value]])]),_:1})]),_:1})]),_:1})]),o("div",Ce,[a(g,null,{header:s(()=>[o("div",xe,[e[17]||(e[17]=o("span",null,"最近待审核资料",-1)),a(u,{type:"primary",size:"small",onClick:e[3]||(e[3]=l=>t.$router.push("/admin/resources"))},{default:s(()=>e[16]||(e[16]=[d(" 查看全部 ")])),_:1,__:[16]})])]),default:s(()=>[C((i(),m("div",null,[a(I,{data:_.value,style:{width:"100%"}},{default:s(()=>[a(y,{prop:"name",label:"资料名称","min-width":"200"}),a(y,{prop:"examType",label:"考试类型",width:"100"},{default:s(({row:l})=>[a(H,{type:N(l.examType),class:le({"custom-purple":l.examType===4})},{default:s(()=>[d(f(U(l.examType)),1)]),_:2},1032,["type","class"])]),_:1}),a(y,{prop:"createTime",label:"上传时间",width:"180"},{default:s(({row:l})=>[d(f(oe(ne)(l.createTime)),1)]),_:1}),a(y,{label:"操作",width:"200",fixed:"right"},{default:s(({row:l})=>[a(u,{type:"success",size:"small",onClick:Q=>M(l)},{default:s(()=>e[18]||(e[18]=[d(" 通过 ")])),_:2,__:[18]},1032,["onClick"]),a(u,{type:"danger",size:"small",onClick:Q=>q(l)},{default:s(()=>e[19]||(e[19]=[d(" 驳回 ")])),_:2,__:[19]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),_.value.length===0?(i(),se(L,{key:0,description:"暂无待审核资料"})):ae("",!0)])),[[b,$.value]])]),_:1})]),a(O,{modelValue:c.value,"onUpdate:modelValue":e[6]||(e[6]=l=>c.value=l),title:"驳回资料",width:"500px"},{footer:s(()=>[a(u,{onClick:e[5]||(e[5]=l=>c.value=!1)},{default:s(()=>e[20]||(e[20]=[d("取消")])),_:1,__:[20]}),a(u,{type:"primary",loading:z.value,onClick:A},{default:s(()=>e[21]||(e[21]=[d(" 确认驳回 ")])),_:1,__:[21]},8,["loading"])]),default:s(()=>[a(K,null,{default:s(()=>[a(J,{label:"驳回原因",required:""},{default:s(()=>[a(S,{modelValue:p.value,"onUpdate:modelValue":e[4]||(e[4]=l=>p.value=l),type:"textarea",rows:3,placeholder:"请输入驳回原因"},null,8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}}},ze=de(we,[["__scopeId","data-v-1346373c"]]);export{ze as default};
