package com.gongxingxue.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gongxingxue.common.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * Admin role interceptor
 */
@Component
@RequiredArgsConstructor
public class AdminInterceptor implements HandlerInterceptor {

    private final ObjectMapper objectMapper;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Get user role from request attributes (set by AuthInterceptor)
        Integer userRole = (Integer) request.getAttribute("userRole");
        
        // Check if user has admin role (role = 1)
        if (userRole == null || userRole != 1) {
            sendErrorResponse(response, 403, "Forbidden: Admin access required");
            return false;
        }
        
        return true;
    }

    /**
     * Send error response
     */
    private void sendErrorResponse(HttpServletResponse response, int status, String message) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(objectMapper.writeValueAsString(Result.error(status, message)));
    }
}
