import{u as C,x as R,r as g,o as U,b as B,d as a,e as s,f as r,y as E,g as n,k as K,p as L,l as M,z as h,m as _,E as p}from"./index-C6vAxNDy.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"login-page"},D={class:"login-container"},F={class:"captcha-container"},$=["src"],z={class:"login-footer"},A={style:{"margin-top":"10px"}},I={__name:"Login",setup(T){const y=K(),x=L(),V=C(),o=R({username:"",password:"",captcha:""}),b={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:4,message:"验证码为4位字符",trigger:"blur"}]},c=g(null),d=g(!1),f=g(""),m=()=>{f.value=`/api/auth/captcha?t=${Date.now()}`},i=async()=>{c.value&&await c.value.validate(async w=>{var e;if(w){d.value=!0;try{console.log("Attempting login with:",{username:o.username,password:o.password.replace(/./g,"*")}),await V.login(o.username,o.password,o.captcha),p.success("登录成功");const t=x.query.redirect||"/";y.replace(t)}catch(t){m(),o.captcha="",t.response?p.error(`登录失败: ${((e=t.response.data)==null?void 0:e.message)||t.message||"请检查用户名和密码"}`):t.request?p.error("网络请求失败，请检查网络连接"):p.error(t.message||"登录失败，请检查用户名和密码")}finally{d.value=!1}}})};return U(()=>{m()}),(w,e)=>{const t=n("el-input"),u=n("el-form-item"),k=n("el-button"),q=n("el-form"),v=n("router-link");return M(),B("div",S,[a("div",D,[e[7]||(e[7]=a("h2",{class:"title"},"用户登录",-1)),s(q,{ref_key:"loginFormRef",ref:c,model:o,rules:b,"label-width":"0",onSubmit:E(i,["prevent"])},{default:r(()=>[s(u,{prop:"username"},{default:r(()=>[s(t,{modelValue:o.username,"onUpdate:modelValue":e[0]||(e[0]=l=>o.username=l),placeholder:"用户名","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),s(u,{prop:"password"},{default:r(()=>[s(t,{modelValue:o.password,"onUpdate:modelValue":e[1]||(e[1]=l=>o.password=l),type:"password",placeholder:"密码","prefix-icon":"el-icon-lock","show-password":"",onKeyup:h(i,["enter"])},null,8,["modelValue"])]),_:1}),s(u,{prop:"captcha"},{default:r(()=>[a("div",F,[s(t,{modelValue:o.captcha,"onUpdate:modelValue":e[2]||(e[2]=l=>o.captcha=l),placeholder:"验证码","prefix-icon":"el-icon-picture",onKeyup:h(i,["enter"]),style:{flex:"1"}},null,8,["modelValue"]),a("img",{src:f.value,onClick:m,class:"captcha-image",title:"点击刷新验证码",alt:"验证码"},null,8,$)])]),_:1}),s(u,null,{default:r(()=>[s(k,{type:"primary",loading:d.value,class:"login-button",onClick:i},{default:r(()=>e[3]||(e[3]=[_(" 登录 ")])),_:1,__:[3]},8,["loading"])]),_:1})]),_:1},8,["model"]),a("div",z,[a("div",null,[e[5]||(e[5]=a("span",null,"还没有账号？",-1)),s(v,{to:"/register"},{default:r(()=>e[4]||(e[4]=[_("立即注册")])),_:1,__:[4]})]),a("div",A,[s(v,{to:"/forgot-password"},{default:r(()=>e[6]||(e[6]=[_("忘记密码？")])),_:1,__:[6]})])])])])}}},H=N(I,[["__scopeId","data-v-0c0b0547"]]);export{H as default};
