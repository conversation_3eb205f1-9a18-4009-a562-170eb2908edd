package com.gongxingxue.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 文件工具类
 * 
 * 提供UUID文件名相关的实用方法
 */
@Slf4j
@Component
public class FileUtil {

    @Value("${file.upload-dir}")
    private String uploadDir;

    /**
     * 验证UUID格式是否正确
     * 
     * @param uuid 要验证的UUID字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidUUID(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 尝试解析UUID，如果格式错误会抛出异常
            UUID.fromString(uuid);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 从文件名中提取UUID部分
     * 
     * @param filename 完整文件名（如：e5e151a3-bb06-4554-9757-4796ce2ea1e6.pdf）
     * @return UUID部分（如：e5e151a3-bb06-4554-9757-4796ce2ea1e6）
     */
    public static String extractUUIDFromFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return null;
        }
        
        // 查找最后一个点的位置
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return filename.substring(0, lastDotIndex);
        } else {
            return filename; // 没有扩展名的情况
        }
    }

    /**
     * 从文件名中提取扩展名
     * 
     * @param filename 完整文件名
     * @return 扩展名（包含点，如：.pdf）
     */
    public static String extractExtensionFromFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex);
        } else {
            return ""; // 没有扩展名
        }
    }

    /**
     * 检查文件是否存在
     * 
     * @param filename UUID文件名
     * @return true表示文件存在，false表示不存在
     */
    public boolean fileExists(String filename) {
        try {
            Path filePath = Paths.get(uploadDir).resolve(filename);
            return Files.exists(filePath);
        } catch (Exception e) {
            log.error("检查文件是否存在时出错: {}", filename, e);
            return false;
        }
    }

    /**
     * 获取文件大小
     * 
     * @param filename UUID文件名
     * @return 文件大小（字节），如果文件不存在返回-1
     */
    public long getFileSize(String filename) {
        try {
            Path filePath = Paths.get(uploadDir).resolve(filename);
            if (Files.exists(filePath)) {
                return Files.size(filePath);
            } else {
                return -1;
            }
        } catch (IOException e) {
            log.error("获取文件大小时出错: {}", filename, e);
            return -1;
        }
    }

    /**
     * 格式化文件大小
     * 
     * @param sizeInBytes 文件大小（字节）
     * @return 格式化后的大小字符串（如：1.5 MB）
     */
    public static String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 0) {
            return "未知";
        }
        
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.1f KB", sizeInBytes / 1024.0);
        } else if (sizeInBytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", sizeInBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", sizeInBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 列出所有UUID文件
     * 
     * @return UUID文件名列表
     */
    public List<String> listAllUUIDFiles() {
        List<String> uuidFiles = new ArrayList<>();
        
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (Files.exists(uploadPath)) {
                Files.list(uploadPath)
                    .filter(Files::isRegularFile)
                    .forEach(file -> {
                        String filename = file.getFileName().toString();
                        String uuid = extractUUIDFromFilename(filename);
                        if (isValidUUID(uuid)) {
                            uuidFiles.add(filename);
                        }
                    });
            }
        } catch (IOException e) {
            log.error("列出UUID文件时出错", e);
        }
        
        return uuidFiles;
    }

    /**
     * 查找指定UUID的文件
     * 
     * @param uuid UUID字符串
     * @return 完整的文件名，如果找不到返回null
     */
    public String findFileByUUID(String uuid) {
        if (!isValidUUID(uuid)) {
            return null;
        }
        
        try {
            Path uploadPath = Paths.get(uploadDir);
            if (Files.exists(uploadPath)) {
                return Files.list(uploadPath)
                    .filter(Files::isRegularFile)
                    .map(file -> file.getFileName().toString())
                    .filter(filename -> {
                        String fileUuid = extractUUIDFromFilename(filename);
                        return uuid.equals(fileUuid);
                    })
                    .findFirst()
                    .orElse(null);
            }
        } catch (IOException e) {
            log.error("查找UUID文件时出错: {}", uuid, e);
        }
        
        return null;
    }

    /**
     * 获取文件的详细信息
     * 
     * @param filename UUID文件名
     * @return 文件信息字符串
     */
    public String getFileInfo(String filename) {
        if (!fileExists(filename)) {
            return "文件不存在: " + filename;
        }
        
        String uuid = extractUUIDFromFilename(filename);
        String extension = extractExtensionFromFilename(filename);
        long size = getFileSize(filename);
        String formattedSize = formatFileSize(size);
        
        return String.format(
            "文件信息:\n" +
            "- 文件名: %s\n" +
            "- UUID: %s\n" +
            "- 扩展名: %s\n" +
            "- 文件大小: %s (%d 字节)\n" +
            "- 存储路径: %s",
            filename, uuid, extension, formattedSize, size,
            Paths.get(uploadDir).resolve(filename).toAbsolutePath()
        );
    }
}
