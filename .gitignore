# 考研/考公资料共享平台 - Git忽略文件

# ===========================================
# 环境变量和敏感配置文件
# ===========================================
.env
.env.local
.env.test
.env.production
.env.*.local

# ===========================================
# 日志文件
# ===========================================
*.log
logs/
gongxingxue-backend/logs/

# ===========================================
# 上传文件目录
# ===========================================
uploads/
gongxingxue-backend/uploads/

# ===========================================
# 数据库备份文件
# ===========================================
*.sql
backup/*.sql

# ===========================================
# Java相关
# ===========================================
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Maven
gongxingxue-backend/target/
gongxingxue-backend/.mvn/wrapper/maven-wrapper.jar

# ===========================================
# Node.js相关
# ===========================================
node_modules/
gongxingxue-frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
gongxingxue-frontend/dist/
gongxingxue-frontend/build/

# ===========================================
# IDE相关
# ===========================================
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr

# Eclipse
.project
.classpath
.c9/
*.launch
.settings/
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders

# VS Code
.vscode/
*.code-workspace

# ===========================================
# 操作系统相关
# ===========================================
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~

# ===========================================
# 临时文件
# ===========================================
*.tmp
*.temp
*.cache
.cache/

# ===========================================
# 部署相关
# ===========================================
# 部署脚本中的敏感信息
deploy.sh
deploy.bat

# SSL证书
*.pem
*.key
*.crt
*.csr

# ===========================================
# 其他
# ===========================================
# 测试覆盖率报告
coverage/
*.lcov

# 依赖分析
.nyc_output

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache
