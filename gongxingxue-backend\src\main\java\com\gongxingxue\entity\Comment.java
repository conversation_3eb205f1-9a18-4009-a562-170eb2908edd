package com.gongxingxue.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Comment entity
 */
@Data
@Accessors(chain = true)
@TableName("comment")
public class Comment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * Primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Resource ID
     */
    private Long resourceId;

    /**
     * User ID
     */
    private Long userId;

    /**
     * Comment content
     */
    private String content;

    /**
     * Parent comment ID (for replies)
     */
    private Long parentId;

    /**
     * Creation time
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * Update time
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * Logical delete flag: 0=not deleted, 1=deleted
     */
    @TableLogic
    private Integer deleted;

    /**
     * User information (not stored in database, populated at runtime)
     */
    @TableField(exist = false)
    private User user;

    /**
     * Reply count (not stored in database, populated at runtime)
     */
    @TableField(exist = false)
    private Integer replyCount;

    /**
     * Resource name (not stored in database, populated at runtime)
     */
    @TableField(exist = false)
    private String resourceName;
}
