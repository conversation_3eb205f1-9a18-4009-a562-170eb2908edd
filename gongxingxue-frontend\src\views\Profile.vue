<template>
  <div class="profile">
    <h2 class="page-title">个人中心</h2>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="个人资料" name="info">
        <div class="profile-info" v-loading="loading">
          <el-form
            ref="profileFormRef"
            :model="profileForm"
            :rules="profileRules"
            label-width="100px"
          >
            <el-form-item label="用户名">
              <el-input v-model="profileForm.username" disabled />
            </el-form-item>

            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="profileForm.nickname" placeholder="请输入昵称" />
            </el-form-item>

            <el-form-item label="头像" prop="avatar">
              <el-input v-model="profileForm.avatar" placeholder="请输入头像URL" />
              <div class="avatar-preview">
                <el-avatar :size="64" :src="profileForm.avatar">
                  {{ avatarFallback }}
                </el-avatar>
              </div>
            </el-form-item>

            <el-form-item label="邮箱">
              <el-input v-model="profileForm.email" disabled />
              <div class="email-hint">
                <span v-if="!profileForm.email" style="color: #999;">未绑定邮箱</span>
                <span v-else style="color: #67c23a;">已绑定邮箱</span>
                <span style="margin-left: 10px;">
                  <router-link to="#" @click="activeTab = 'email'" style="color: #409eff;">
                    {{ profileForm.email ? '更换邮箱' : '绑定邮箱' }}
                  </router-link>
                </span>
              </div>
            </el-form-item>

            <el-form-item label="手机号" prop="phone">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="handleUpdateProfile">
                保存修改
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="修改密码" name="password">
        <div class="change-password">
          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="100px"
          >
            <el-form-item label="原密码" prop="oldPassword">
              <el-input
                v-model="passwordForm.oldPassword"
                type="password"
                placeholder="请输入原密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请确认新密码"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" :loading="submittingPassword" @click="handleChangePassword">
                修改密码
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="邮箱绑定" name="email">
        <div class="email-binding">
          <el-card class="email-binding-card">
            <template #header>
              <div class="card-header">
                <span>邮箱绑定</span>
                <el-tag v-if="!user.email" type="warning">未绑定</el-tag>
                <el-tag v-else type="success">已绑定</el-tag>
              </div>
            </template>

            <div v-if="!user.email" class="bind-email">
              <div class="benefits">
                <p style="margin-bottom: 15px; font-weight: 500;">绑定邮箱后可以：</p>
                <ul style="margin: 0; padding-left: 20px; color: #666;">
                  <li>🔐 使用邮箱找回密码</li>
                  <li>📧 接收重要通知</li>
                  <li>🛡️ 提升账号安全性</li>
                </ul>
              </div>

              <el-form
                ref="emailBindFormRef"
                :model="emailBindForm"
                :rules="emailBindRules"
                label-width="80px"
                style="margin-top: 20px;"
              >
                <el-form-item label="邮箱" prop="email">
                  <el-input
                    v-model="emailBindForm.email"
                    placeholder="请输入邮箱地址"
                    :disabled="sendingCode"
                  />
                </el-form-item>

                <el-form-item label="验证码" prop="code">
                  <div style="display: flex; gap: 10px;">
                    <el-input
                      v-model="emailBindForm.code"
                      placeholder="请输入验证码"
                      style="flex: 1;"
                      :disabled="!codeSent"
                    />
                    <el-button
                      :disabled="!emailBindForm.email || sendingCode || (countdown > 0)"
                      :loading="sendingCode"
                      @click="sendVerificationCode"
                    >
                      {{ countdown > 0 ? `${countdown}s后重发` : '发送验证码' }}
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item>
                  <el-button
                    type="primary"
                    :loading="bindingEmail"
                    :disabled="!codeSent"
                    @click="handleBindEmail"
                  >
                    绑定邮箱
                  </el-button>
                </el-form-item>
              </el-form>
            </div>

            <div v-else class="email-info">
              <p style="margin-bottom: 15px;">
                <strong>已绑定邮箱：</strong>{{ user.email }}
              </p>
              <el-button @click="showUnbindConfirm">解绑邮箱</el-button>
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <el-tab-pane label="账号信息" name="account">
        <div class="account-info">
          <el-descriptions title="账号信息" :column="1" border>
            <el-descriptions-item label="用户名">{{ user.username }}</el-descriptions-item>
            <el-descriptions-item label="用户角色">{{ userRole }}</el-descriptions-item>
            <el-descriptions-item label="注册时间">{{ formatDate(user.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="最后登录时间">{{ formatDate(user.lastLoginTime) }}</el-descriptions-item>
            <el-descriptions-item label="最后登录IP">{{ formatIpAddress(user.lastLoginIp) }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '../store/user'
import { getCurrentUser, updateProfile, changePassword } from '../api/auth'
import { sendEmailCode, bindEmail, unbindEmail } from '../api/user'
import { formatDate } from '../utils/auth'
import { formatIpAddress } from '../utils/ip'

const userStore = useUserStore()

// Data
const activeTab = ref('info')
const loading = ref(false)
const submitting = ref(false)
const submittingPassword = ref(false)
const user = ref({})

// Forms
const profileFormRef = ref(null)
const passwordFormRef = ref(null)

const profileForm = reactive({
  username: '',
  nickname: '',
  avatar: '',
  email: '',
  phone: ''
})

const passwordForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// Email binding form
const emailBindFormRef = ref(null)
const emailBindForm = reactive({
  email: '',
  code: ''
})

// Email binding states
const sendingCode = ref(false)
const bindingEmail = ref(false)
const codeSent = ref(false)
const countdown = ref(0)
let countdownTimer = null

// Computed
const avatarFallback = computed(() => {
  const nickname = profileForm.nickname || user.value.username || ''
  return nickname ? nickname.charAt(0).toUpperCase() : 'U'
})

const userRole = computed(() => {
  return user.value.role === 1 ? '管理员' : '普通用户'
})



// Form validation rules
const profileRules = {
  nickname: [
    { max: 20, message: '昵称不能超过20个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
}

const validateConfirmPassword = (rule, value, callback) => {
  if (value !== passwordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const passwordRules = {
  oldPassword: [
    { required: true, message: '请输入原密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 8, max: 16, message: '密码长度必须在8-16个字符之间', trigger: 'blur' },
    { pattern: /^(?=.*[a-zA-Z])(?=.*\d).+$/, message: '密码必须包含字母和数字', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// Email binding validation rules
const emailBindRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

// Fetch user info
const fetchUserInfo = async () => {
  loading.value = true
  try {
    const response = await getCurrentUser()
    user.value = response.data

    // Initialize profile form
    profileForm.username = user.value.username
    profileForm.nickname = user.value.nickname || ''
    profileForm.avatar = user.value.avatar || ''
    profileForm.email = user.value.email || ''
    profileForm.phone = user.value.phone || ''
  } catch (error) {
    console.error('Failed to fetch user info:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// Handle update profile
const handleUpdateProfile = async () => {
  if (!profileFormRef.value) return

  await profileFormRef.value.validate(async (valid) => {
    if (!valid) return

    submitting.value = true

    try {
      await updateProfile({
        nickname: profileForm.nickname,
        avatar: profileForm.avatar,
        email: profileForm.email,
        phone: profileForm.phone
      })

      ElMessage.success('个人资料更新成功')

      // Refresh user info
      await fetchUserInfo()

      // Update user store
      await userStore.fetchCurrentUser()
    } catch (error) {
      console.error('Failed to update profile:', error)
      ElMessage.error('更新个人资料失败')
    } finally {
      submitting.value = false
    }
  })
}

// Handle change password
const handleChangePassword = async () => {
  if (!passwordFormRef.value) return

  await passwordFormRef.value.validate(async (valid) => {
    if (!valid) return

    submittingPassword.value = true

    try {
      await changePassword(passwordForm.oldPassword, passwordForm.newPassword)

      ElMessage.success('密码修改成功，请重新登录')

      // Clear password form
      passwordForm.oldPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''

      // Logout
      userStore.logout()

      // Redirect to login page
      window.location.href = '/login'
    } catch (error) {
      console.error('Failed to change password:', error)
      ElMessage.error(error.message || '修改密码失败')
    } finally {
      submittingPassword.value = false
    }
  })
}

// Send email verification code
const sendVerificationCode = async () => {
  if (!emailBindForm.email) {
    ElMessage.error('请先输入邮箱地址')
    return
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(emailBindForm.email)) {
    ElMessage.error('请输入正确的邮箱地址')
    return
  }

  sendingCode.value = true

  try {
    await sendEmailCode(emailBindForm.email)
    ElMessage.success('验证码已发送到您的邮箱')
    codeSent.value = true

    // Start countdown
    countdown.value = 60
    countdownTimer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer)
        countdownTimer = null
      }
    }, 1000)
  } catch (error) {
    console.error('Failed to send verification code:', error)
    ElMessage.error(error.message || '发送验证码失败')
  } finally {
    sendingCode.value = false
  }
}

// Handle bind email
const handleBindEmail = async () => {
  if (!emailBindFormRef.value) return

  await emailBindFormRef.value.validate(async (valid) => {
    if (!valid) return

    bindingEmail.value = true

    try {
      await bindEmail(emailBindForm.email, emailBindForm.code)
      ElMessage.success('邮箱绑定成功')

      // Reset form
      emailBindForm.email = ''
      emailBindForm.code = ''
      codeSent.value = false

      // Refresh user info
      await fetchUserInfo()
      await userStore.fetchCurrentUser()
    } catch (error) {
      console.error('Failed to bind email:', error)
      ElMessage.error(error.message || '邮箱绑定失败')
    } finally {
      bindingEmail.value = false
    }
  })
}

// Show unbind confirmation
const showUnbindConfirm = () => {
  ElMessageBox.confirm(
    '解绑邮箱后将无法使用邮箱找回密码，确定要解绑吗？',
    '确认解绑',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      console.log('Starting unbind email process...')
      await unbindEmail()
      console.log('Unbind email API call successful')

      ElMessage.success('邮箱解绑成功')

      // Refresh user info with explicit logging
      console.log('Refreshing user info...')
      await fetchUserInfo()
      console.log('fetchUserInfo completed, user.email:', user.value.email)

      console.log('Updating user store...')
      await userStore.fetchCurrentUser()
      console.log('userStore.fetchCurrentUser completed, store user.email:', userStore.user?.email)

      // Force reactivity update
      user.value = { ...user.value }
      console.log('Force reactivity update completed')

    } catch (error) {
      console.error('Failed to unbind email:', error)
      ElMessage.error(error.message || '邮箱解绑失败')
    }
  }).catch(() => {
    // User cancelled
    console.log('User cancelled unbind operation')
  })
}

// Fetch data on component mount
onMounted(() => {
  fetchUserInfo()
})
</script>

<style lang="scss" scoped>
.profile {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .el-tabs {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
  }

  .profile-info, .change-password, .account-info {
    padding: 20px 0;
    max-width: 600px;
  }

  .avatar-preview {
    margin-top: 10px;
  }

  .email-hint {
    margin-top: 5px;
    font-size: 12px;
  }

  .email-binding {
    padding: 20px 0;
    max-width: 600px;

    .email-binding-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .benefits {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #409eff;
      }

      .bind-email, .email-info {
        padding: 20px 0;
      }
    }
  }
}
</style>
