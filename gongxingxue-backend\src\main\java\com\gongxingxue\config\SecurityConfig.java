package com.gongxingxue.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 安全配置类
 * 
 * 提供密码加密等安全相关的Bean配置
 */
@Configuration
public class SecurityConfig {

    /**
     * 密码加密器
     * 
     * 使用BCrypt算法进行密码加密：
     * - BCrypt是目前最安全的密码加密算法之一
     * - 自动生成盐值，相同密码加密结果不同
     * - 计算成本可调，可以抵御暴力破解
     * - Spring Security官方推荐
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        // 使用强度为12的BCrypt加密
        // 强度越高越安全，但计算时间也越长
        // 12是安全性和性能的平衡点
        return new BCryptPasswordEncoder(12);
    }
}
