import{u as k,x as P,r as g,o as U,b as R,d as n,e as s,f as a,y as C,g as d,k as E,l as q,z as B,m as _,E as w}from"./index-BCDD51NH.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";const M={class:"register-page"},N={class:"register-container"},S={class:"captcha-container"},z=["src"],K={class:"register-footer"},$={__name:"Register",setup(A){const v=E(),h=k(),r=P({username:"",password:"",confirmPassword:"",nickname:"",captcha:""}),V={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{validator:(i,e,o)=>{e.length<6||e.length>20?o(new Error("用户名长度必须在6-20个字符之间")):o()},trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{validator:(i,e,o)=>{e.length<8||e.length>16?o(new Error("密码长度必须在8-16个字符之间")):/^(?=.*[a-zA-Z])(?=.*\d).+$/.test(e)?o():o(new Error("密码必须包含字母和数字"))},trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(i,e,o)=>{e!==r.password?o(new Error("两次输入的密码不一致")):o()},trigger:"blur"}],nickname:[{max:20,message:"昵称不能超过20个字符",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{min:4,max:4,message:"验证码为4位字符",trigger:"blur"}]},u=g(null),p=g(!1),f=g(""),m=()=>{f.value=`/api/auth/captcha?t=${Date.now()}`},c=async()=>{u.value&&await u.value.validate(async i=>{if(i){p.value=!0;try{await h.register(r.username,r.password,r.nickname||void 0,r.captcha),w.success("注册成功，请登录"),v.push("/login")}catch(e){console.error("Registration failed:",e),m(),r.captcha="",w.error(e.message||"注册失败，请稍后重试")}finally{p.value=!1}}})};return U(()=>{m()}),(i,e)=>{const o=d("el-input"),l=d("el-form-item"),x=d("el-button"),y=d("el-form"),b=d("router-link");return q(),R("div",M,[n("div",N,[e[8]||(e[8]=n("h2",{class:"title"},"用户注册",-1)),s(y,{ref_key:"registerFormRef",ref:u,model:r,rules:V,"label-width":"0",onSubmit:C(c,["prevent"])},{default:a(()=>[s(l,{prop:"username"},{default:a(()=>[s(o,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=t=>r.username=t),placeholder:"用户名 (6-20位字符)","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),s(l,{prop:"password"},{default:a(()=>[s(o,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=t=>r.password=t),type:"password",placeholder:"密码 (8-16位数字+字母组合)","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue"])]),_:1}),s(l,{prop:"confirmPassword"},{default:a(()=>[s(o,{modelValue:r.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=t=>r.confirmPassword=t),type:"password",placeholder:"确认密码","prefix-icon":"el-icon-lock","show-password":""},null,8,["modelValue"])]),_:1}),s(l,{prop:"nickname"},{default:a(()=>[s(o,{modelValue:r.nickname,"onUpdate:modelValue":e[3]||(e[3]=t=>r.nickname=t),placeholder:"昵称 (选填)","prefix-icon":"el-icon-user"},null,8,["modelValue"])]),_:1}),s(l,{prop:"captcha"},{default:a(()=>[n("div",S,[s(o,{modelValue:r.captcha,"onUpdate:modelValue":e[4]||(e[4]=t=>r.captcha=t),placeholder:"验证码","prefix-icon":"el-icon-picture",onKeyup:B(c,["enter"]),style:{flex:"1"}},null,8,["modelValue"]),n("img",{src:f.value,onClick:m,class:"captcha-image",title:"点击刷新验证码",alt:"验证码"},null,8,z)])]),_:1}),s(l,null,{default:a(()=>[s(x,{type:"primary",loading:p.value,class:"register-button",onClick:c},{default:a(()=>e[5]||(e[5]=[_(" 注册 ")])),_:1,__:[5]},8,["loading"])]),_:1})]),_:1},8,["model"]),n("div",K,[e[7]||(e[7]=n("span",null,"已有账号？",-1)),s(b,{to:"/login"},{default:a(()=>e[6]||(e[6]=[_("立即登录")])),_:1,__:[6]})])])])}}},G=F($,[["__scopeId","data-v-cb712094"]]);export{G as default};
