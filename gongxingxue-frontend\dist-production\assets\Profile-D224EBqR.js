import{J,u as ne,r as b,x as O,c as D,o as ie,b as R,d as u,e as o,f as a,g as y,Y as de,E as _,s as ue,l as P,q as me,m as p,t as h,h as H,j as A,G as M,Z as ce,$ as pe,D as fe}from"./index-BC3U7MsG.js";import{f as ge}from"./ip-BIZqVrX-.js";import{_ as we}from"./_plugin-vue_export-helper-DlAUqK2U.js";function ve(k){return console.log("Send email code request:",{email:k}),new Promise((c,s)=>{const r=new XMLHttpRequest,t=`/api/users/email/send-code?email=${encodeURIComponent(k)}`;r.open("POST",t,!0);const m=J();m&&r.setRequestHeader("Authorization",`Bearer ${m}`),r.onload=function(){if(r.status>=200&&r.status<300)try{const l=JSON.parse(r.responseText);console.log("Send email code success response:",l),c(l)}catch(l){console.error("Error parsing response:",l),s(new Error("Invalid response format"))}else{console.error("Send email code failed with status:",r.status),console.error("Response text:",r.responseText);try{const l=JSON.parse(r.responseText);s(new Error(l.message||`Request failed: ${r.status} ${r.statusText}`))}catch{s(new Error(`Request failed: ${r.status} ${r.statusText}`))}}},r.onerror=function(){console.error("Network error during send email code"),s(new Error("Network error during send email code"))},r.send()})}function _e(k,c){return console.log("Bind email request:",{email:k,code:c}),new Promise((s,r)=>{const t=new XMLHttpRequest,m=`/api/users/email/bind?email=${encodeURIComponent(k)}&code=${encodeURIComponent(c)}`;t.open("POST",m,!0);const l=J();l&&t.setRequestHeader("Authorization",`Bearer ${l}`),t.onload=function(){if(t.status>=200&&t.status<300)try{const x=JSON.parse(t.responseText);console.log("Bind email success response:",x),s(x)}catch(x){console.error("Error parsing response:",x),r(new Error("Invalid response format"))}else{console.error("Bind email failed with status:",t.status),console.error("Response text:",t.responseText);try{const x=JSON.parse(t.responseText);r(new Error(x.message||`Request failed: ${t.status} ${t.statusText}`))}catch{r(new Error(`Request failed: ${t.status} ${t.statusText}`))}}},t.onerror=function(){console.error("Network error during bind email"),r(new Error("Network error during bind email"))},t.send()})}function be(){return console.log("Unbind email request"),new Promise((k,c)=>{const s=new XMLHttpRequest;s.open("DELETE","/api/users/email/unbind",!0);const t=J();t&&s.setRequestHeader("Authorization",`Bearer ${t}`),s.onload=function(){if(s.status>=200&&s.status<300)try{const m=JSON.parse(s.responseText);console.log("Unbind email success response:",m),k(m)}catch(m){console.error("Error parsing response:",m),c(new Error("Invalid response format"))}else{console.error("Unbind email failed with status:",s.status),console.error("Response text:",s.responseText);try{const m=JSON.parse(s.responseText);c(new Error(m.message||`Request failed: ${s.status} ${s.statusText}`))}catch{c(new Error(`Request failed: ${s.status} ${s.statusText}`))}}},s.onerror=function(){console.error("Network error during unbind email"),c(new Error("Network error during unbind email"))},s.send()})}const ye={class:"profile"},xe={class:"profile-info"},he={class:"avatar-preview"},ke={class:"email-hint"},Pe={key:0,style:{color:"#999"}},Ve={key:1,style:{color:"#67c23a"}},Re={style:{"margin-left":"10px"}},Ue={class:"change-password"},Ee={class:"email-binding"},Te={class:"card-header"},Ce={key:0,class:"bind-email"},$e={style:{display:"flex",gap:"10px"}},qe={key:1,class:"email-info"},Be={style:{"margin-bottom":"15px"}},Fe={class:"account-info"},Se={__name:"Profile",setup(k){const c=ne(),s=b("info"),r=b(!1),t=b(!1),m=b(!1),l=b({}),x=b(null),B=b(null),n=O({username:"",nickname:"",avatar:"",email:"",phone:""}),f=O({oldPassword:"",newPassword:"",confirmPassword:""}),F=b(null),g=O({email:"",code:""}),U=b(!1),S=b(!1),C=b(!1),V=b(0);let I=null;const z=D(()=>{const d=n.nickname||l.value.username||"";return d?d.charAt(0).toUpperCase():"U"}),X=D(()=>l.value.role===1?"管理员":"普通用户"),Z={nickname:[{max:20,message:"昵称不能超过20个字符",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}]},G={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{min:8,max:16,message:"密码长度必须在8-16个字符之间",trigger:"blur"},{pattern:/^(?=.*[a-zA-Z])(?=.*\d).+$/,message:"密码必须包含字母和数字",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(d,e,w)=>{e!==f.newPassword?w(new Error("两次输入的密码不一致")):w()},trigger:"blur"}]},Y={email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:6,message:"验证码为6位数字",trigger:"blur"}]},$=async()=>{r.value=!0;try{const d=await de();l.value=d.data,n.username=l.value.username,n.nickname=l.value.nickname||"",n.avatar=l.value.avatar||"",n.email=l.value.email||"",n.phone=l.value.phone||""}catch(d){console.error("Failed to fetch user info:",d),_.error("获取用户信息失败")}finally{r.value=!1}},K=async()=>{x.value&&await x.value.validate(async d=>{if(d){t.value=!0;try{await ce({nickname:n.nickname,avatar:n.avatar,email:n.email,phone:n.phone}),_.success("个人资料更新成功"),await $(),await c.fetchCurrentUser()}catch(e){console.error("Failed to update profile:",e),_.error("更新个人资料失败")}finally{t.value=!1}}})},Q=async()=>{B.value&&await B.value.validate(async d=>{if(d){m.value=!0;try{await pe(f.oldPassword,f.newPassword),_.success("密码修改成功，请重新登录"),f.oldPassword="",f.newPassword="",f.confirmPassword="",c.logout(),window.location.href="/login"}catch(e){console.error("Failed to change password:",e),_.error(e.message||"修改密码失败")}finally{m.value=!1}}})},W=async()=>{if(!g.email){_.error("请先输入邮箱地址");return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g.email)){_.error("请输入正确的邮箱地址");return}U.value=!0;try{await ve(g.email),_.success("验证码已发送到您的邮箱"),C.value=!0,V.value=60,I=setInterval(()=>{V.value--,V.value<=0&&(clearInterval(I),I=null)},1e3)}catch(e){console.error("Failed to send verification code:",e),_.error(e.message||"发送验证码失败")}finally{U.value=!1}},j=async()=>{F.value&&await F.value.validate(async d=>{if(d){S.value=!0;try{await _e(g.email,g.code),_.success("邮箱绑定成功"),g.email="",g.code="",C.value=!1,await $(),await c.fetchCurrentUser()}catch(e){console.error("Failed to bind email:",e),_.error(e.message||"邮箱绑定失败")}finally{S.value=!1}}})},ee=()=>{fe.confirm("解绑邮箱后将无法使用邮箱找回密码，确定要解绑吗？","确认解绑",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{var d;try{console.log("Starting unbind email process..."),await be(),console.log("Unbind email API call successful"),_.success("邮箱解绑成功"),console.log("Refreshing user info..."),await $(),console.log("fetchUserInfo completed, user.email:",l.value.email),console.log("Updating user store..."),await c.fetchCurrentUser(),console.log("userStore.fetchCurrentUser completed, store user.email:",(d=c.user)==null?void 0:d.email),l.value={...l.value},console.log("Force reactivity update completed")}catch(e){console.error("Failed to unbind email:",e),_.error(e.message||"邮箱解绑失败")}}).catch(()=>{console.log("User cancelled unbind operation")})};return ie(()=>{$()}),(d,e)=>{const w=y("el-input"),v=y("el-form-item"),oe=y("el-avatar"),ae=y("router-link"),E=y("el-button"),N=y("el-form"),q=y("el-tab-pane"),L=y("el-tag"),le=y("el-card"),T=y("el-descriptions-item"),se=y("el-descriptions"),re=y("el-tabs"),te=ue("loading");return P(),R("div",ye,[e[21]||(e[21]=u("h2",{class:"page-title"},"个人中心",-1)),o(re,{modelValue:s.value,"onUpdate:modelValue":e[11]||(e[11]=i=>s.value=i)},{default:a(()=>[o(q,{label:"个人资料",name:"info"},{default:a(()=>[me((P(),R("div",xe,[o(N,{ref_key:"profileFormRef",ref:x,model:n,rules:Z,"label-width":"100px"},{default:a(()=>[o(v,{label:"用户名"},{default:a(()=>[o(w,{modelValue:n.username,"onUpdate:modelValue":e[0]||(e[0]=i=>n.username=i),disabled:""},null,8,["modelValue"])]),_:1}),o(v,{label:"昵称",prop:"nickname"},{default:a(()=>[o(w,{modelValue:n.nickname,"onUpdate:modelValue":e[1]||(e[1]=i=>n.nickname=i),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),o(v,{label:"头像",prop:"avatar"},{default:a(()=>[o(w,{modelValue:n.avatar,"onUpdate:modelValue":e[2]||(e[2]=i=>n.avatar=i),placeholder:"请输入头像URL"},null,8,["modelValue"]),u("div",he,[o(oe,{size:64,src:n.avatar},{default:a(()=>[p(h(z.value),1)]),_:1},8,["src"])])]),_:1}),o(v,{label:"邮箱"},{default:a(()=>[o(w,{modelValue:n.email,"onUpdate:modelValue":e[3]||(e[3]=i=>n.email=i),disabled:""},null,8,["modelValue"]),u("div",ke,[n.email?(P(),R("span",Ve,"已绑定邮箱")):(P(),R("span",Pe,"未绑定邮箱")),u("span",Re,[o(ae,{to:"#",onClick:e[4]||(e[4]=i=>s.value="email"),style:{color:"#409eff"}},{default:a(()=>[p(h(n.email?"更换邮箱":"绑定邮箱"),1)]),_:1})])])]),_:1}),o(v,{label:"手机号",prop:"phone"},{default:a(()=>[o(w,{modelValue:n.phone,"onUpdate:modelValue":e[5]||(e[5]=i=>n.phone=i),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1}),o(v,null,{default:a(()=>[o(E,{type:"primary",loading:t.value,onClick:K},{default:a(()=>e[12]||(e[12]=[p(" 保存修改 ")])),_:1,__:[12]},8,["loading"])]),_:1})]),_:1},8,["model"])])),[[te,r.value]])]),_:1}),o(q,{label:"修改密码",name:"password"},{default:a(()=>[u("div",Ue,[o(N,{ref_key:"passwordFormRef",ref:B,model:f,rules:G,"label-width":"100px"},{default:a(()=>[o(v,{label:"原密码",prop:"oldPassword"},{default:a(()=>[o(w,{modelValue:f.oldPassword,"onUpdate:modelValue":e[6]||(e[6]=i=>f.oldPassword=i),type:"password",placeholder:"请输入原密码","show-password":""},null,8,["modelValue"])]),_:1}),o(v,{label:"新密码",prop:"newPassword"},{default:a(()=>[o(w,{modelValue:f.newPassword,"onUpdate:modelValue":e[7]||(e[7]=i=>f.newPassword=i),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(v,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[o(w,{modelValue:f.confirmPassword,"onUpdate:modelValue":e[8]||(e[8]=i=>f.confirmPassword=i),type:"password",placeholder:"请确认新密码","show-password":""},null,8,["modelValue"])]),_:1}),o(v,null,{default:a(()=>[o(E,{type:"primary",loading:m.value,onClick:Q},{default:a(()=>e[13]||(e[13]=[p(" 修改密码 ")])),_:1,__:[13]},8,["loading"])]),_:1})]),_:1},8,["model"])])]),_:1}),o(q,{label:"邮箱绑定",name:"email"},{default:a(()=>[u("div",Ee,[o(le,{class:"email-binding-card"},{header:a(()=>[u("div",Te,[e[16]||(e[16]=u("span",null,"邮箱绑定",-1)),l.value.email?(P(),H(L,{key:1,type:"success"},{default:a(()=>e[15]||(e[15]=[p("已绑定")])),_:1,__:[15]})):(P(),H(L,{key:0,type:"warning"},{default:a(()=>e[14]||(e[14]=[p("未绑定")])),_:1,__:[14]}))])]),default:a(()=>[l.value.email?(P(),R("div",qe,[u("p",Be,[e[19]||(e[19]=u("strong",null,"已绑定邮箱：",-1)),p(h(l.value.email),1)]),o(E,{onClick:ee},{default:a(()=>e[20]||(e[20]=[p("解绑邮箱")])),_:1,__:[20]})])):(P(),R("div",Ce,[e[18]||(e[18]=u("div",{class:"benefits"},[u("p",{style:{"margin-bottom":"15px","font-weight":"500"}},"绑定邮箱后可以："),u("ul",{style:{margin:"0","padding-left":"20px",color:"#666"}},[u("li",null,"🔐 使用邮箱找回密码"),u("li",null,"📧 接收重要通知"),u("li",null,"🛡️ 提升账号安全性")])],-1)),o(N,{ref_key:"emailBindFormRef",ref:F,model:g,rules:Y,"label-width":"80px",style:{"margin-top":"20px"}},{default:a(()=>[o(v,{label:"邮箱",prop:"email"},{default:a(()=>[o(w,{modelValue:g.email,"onUpdate:modelValue":e[9]||(e[9]=i=>g.email=i),placeholder:"请输入邮箱地址",disabled:U.value},null,8,["modelValue","disabled"])]),_:1}),o(v,{label:"验证码",prop:"code"},{default:a(()=>[u("div",$e,[o(w,{modelValue:g.code,"onUpdate:modelValue":e[10]||(e[10]=i=>g.code=i),placeholder:"请输入验证码",style:{flex:"1"},disabled:!C.value},null,8,["modelValue","disabled"]),o(E,{disabled:!g.email||U.value||V.value>0,loading:U.value,onClick:W},{default:a(()=>[p(h(V.value>0?`${V.value}s后重发`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),o(v,null,{default:a(()=>[o(E,{type:"primary",loading:S.value,disabled:!C.value,onClick:j},{default:a(()=>e[17]||(e[17]=[p(" 绑定邮箱 ")])),_:1,__:[17]},8,["loading","disabled"])]),_:1})]),_:1},8,["model"])]))]),_:1})])]),_:1}),o(q,{label:"账号信息",name:"account"},{default:a(()=>[u("div",Fe,[o(se,{title:"账号信息",column:1,border:""},{default:a(()=>[o(T,{label:"用户名"},{default:a(()=>[p(h(l.value.username),1)]),_:1}),o(T,{label:"用户角色"},{default:a(()=>[p(h(X.value),1)]),_:1}),o(T,{label:"注册时间"},{default:a(()=>[p(h(A(M)(l.value.createTime)),1)]),_:1}),o(T,{label:"最后登录时间"},{default:a(()=>[p(h(A(M)(l.value.lastLoginTime)),1)]),_:1}),o(T,{label:"最后登录IP"},{default:a(()=>[p(h(A(ge)(l.value.lastLoginIp)),1)]),_:1})]),_:1})])]),_:1})]),_:1},8,["modelValue"])])}}},Je=we(Se,[["__scopeId","data-v-cee06388"]]);export{Je as default};
