<template>
  <div class="my-resources">
    <h2 class="page-title">我的资料</h2>

    <div class="resources-container" v-loading="loading">
      <template v-if="resources.length > 0">
        <el-table :data="resources" style="width: 100%">
          <el-table-column prop="name" label="资料名称" min-width="200">
            <template #default="{ row }">
              <router-link
                v-if="row.auditStatus === 1"
                :to="{ name: 'ResourceDetail', params: { id: row.id } }"
                class="resource-link"
              >
                {{ row.name }}
              </router-link>
              <span v-else>{{ row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="examType" label="考试类型" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getExamTypeTag(row.examType)"
                :class="{ 'custom-purple': row.examType === 4 }"
              >
                {{ getExamTypeText(row.examType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="文件信息" width="120">
            <template #default="{ row }">
              <div class="file-info">
                <span class="file-type">{{ getFileTypeDisplay(row.originalFilename) }}</span>
                <span class="file-size">{{ formatFileSize(row.fileSize) }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="downloadCount" label="下载次数" width="100" align="center">
            <template #default="{ row }">
              <span class="download-count">{{ row.downloadCount || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="createTime" label="上传时间" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column prop="auditStatus" label="审核状态" width="120">
            <template #default="{ row }">
              <el-tag
                :type="getAuditStatusTag(row.auditStatus)"
                effect="dark"
              >
                {{ getAuditStatusText(row.auditStatus) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <!-- 第一列：查看按钮 -->
                <el-button
                  v-if="row.auditStatus === 1"
                  type="primary"
                  size="small"
                  @click="viewResource(row)"
                  class="action-btn"
                >
                  查看原页
                </el-button>
                <el-button
                  v-else-if="row.auditStatus === 2"
                  type="warning"
                  size="small"
                  @click="showRejectReason(row)"
                  class="action-btn"
                >
                  查看原因
                </el-button>

                <!-- 第二列：删除按钮（Grid会自动放到第二列） -->
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                  class="action-btn"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <el-empty v-else description="暂无上传的资料">
        <el-button type="primary" @click="$router.push('/upload')">上传资料</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getMyResources, deleteResource } from '../api/resource'
import { formatDate } from '../utils/auth'

// Router
const router = useRouter()

// Data
const resources = ref([])
const loading = ref(false)

// Fetch my resources
const fetchMyResources = async () => {
  loading.value = true
  try {
    const response = await getMyResources()
    resources.value = response.data
  } catch (error) {
    console.error('Failed to fetch my resources:', error)
    ElMessage.error('获取资料列表失败')
  } finally {
    loading.value = false
  }
}

// Get exam type text
const getExamTypeText = (examType) => {
  const examTypes = ['考研', '考公', '法考', '教资', '其他']
  return examTypes[examType] || '未知'
}

// Get exam type tag type
const getExamTypeTag = (examType) => {
  const tagTypes = ['primary', 'success', 'warning', 'danger', '']
  return tagTypes[examType] || ''
}

// Get audit status text
const getAuditStatusText = (status) => {
  const statusTexts = ['待审核', '已通过', '已驳回']
  return statusTexts[status] || '未知'
}

// Get audit status tag type
const getAuditStatusTag = (status) => {
  const tagTypes = ['info', 'success', 'danger']
  return tagTypes[status] || 'info'
}

// Get file type display
const getFileTypeDisplay = (filename) => {
  if (!filename) return '未知'

  const extension = filename.split('.').pop()?.toUpperCase()

  switch (extension) {
    case 'PDF':
      return 'PDF'
    case 'DOC':
    case 'DOCX':
      return 'DOC'
    case 'PPT':
    case 'PPTX':
      return 'PPT'
    case 'XLS':
    case 'XLSX':
      return 'XLS'
    case 'TXT':
      return 'TXT'
    case 'JPG':
    case 'JPEG':
    case 'PNG':
    case 'GIF':
    case 'BMP':
      return '图片'
    default:
      return '其他'
  }
}

// Format file size
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// View resource detail
const viewResource = (resource) => {
  // Navigate to resource detail page using router
  const routeData = router.resolve({ name: 'ResourceDetail', params: { id: resource.id } })
  window.open(routeData.href, '_blank')
}

// Show reject reason
const showRejectReason = (resource) => {
  ElMessageBox.alert(
    resource.rejectReason || '管理员未提供驳回原因',
    '驳回原因',
    {
      confirmButtonText: '确定'
    }
  )
}

// Handle delete
const handleDelete = async (resource) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该资料吗？删除后无法恢复。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteResource(resource.id)
    ElMessage.success('资料已删除')

    // Refresh list
    fetchMyResources()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete resource:', error)
      ElMessage.error('删除失败')
    }
  }
}

// Fetch data on component mount
onMounted(() => {
  fetchMyResources()
})
</script>

<style lang="scss" scoped>
.my-resources {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .resources-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;
    min-height: 300px;
  }

  .resource-link {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  .file-info {
    display: flex;
    align-items: center;
    gap: 6px;

    .file-type {
      font-size: 12px;
      font-weight: 500;
      color: #606266;
      background-color: #f4f4f5;
      padding: 2px 6px;
      border-radius: 3px;
      display: inline-block;
      flex-shrink: 0;
    }

    .file-size {
      font-size: 12px;
      color: #909399;
      white-space: nowrap;
    }
  }

  .download-count {
    font-weight: 500;
    color: #409eff;

    &:hover {
      color: #66b1ff;
    }
  }

  .action-buttons {
    display: flex;
    justify-content: flex-end; // 右对齐
    align-items: center;
    gap: 6px;
    height: 24px;

    .action-btn {
      width: 68px;
      height: 24px;
      font-size: 12px;
      padding: 0;
      border-radius: 4px;
      transition: all 0.3s ease;
      flex-shrink: 0;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      // 确保按钮文字居中
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

// 响应式设计 - 保持水平排列
@media (max-width: 768px) {
  .my-resources {
    .action-buttons {
      gap: 4px;
      height: 22px;

      .action-btn {
        width: 56px;
        height: 22px;
        font-size: 11px;
        padding: 0;
      }
    }
  }

  // 自定义紫色标签样式 - 用于"其他"类型，模仿Element Plus的设计
  :deep(.el-tag) {
    &.custom-purple {
      background-color: #f4f0ff;
      border: 1px solid #d4c5f9;
      color: #7c3aed;

      // 保持与Element Plus标签一致的样式
      border-radius: 4px;
      font-size: 12px;
      line-height: 1;
      white-space: nowrap;

      // 悬停效果
      &:hover {
        background-color: #ede9fe;
        border-color: #c4b5fd;
      }
    }
  }
}
</style>
