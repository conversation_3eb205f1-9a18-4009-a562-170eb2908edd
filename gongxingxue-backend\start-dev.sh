#!/bin/bash

# 开发环境启动脚本
echo "🚀 启动开发环境..."

# 设置环境变量
export SPRING_PROFILES_ACTIVE=dev

# 如果存在 .env.dev 文件，加载环境变量
if [ -f .env.dev ]; then
    echo "📝 加载开发环境配置文件..."
    export $(cat .env.dev | grep -v '^#' | xargs)
fi

# 创建必要的目录
mkdir -p uploads/dev
mkdir -p logs

echo "🔧 环境配置："
echo "  - Profile: $SPRING_PROFILES_ACTIVE"
echo "  - Upload Dir: $FILE_UPLOAD_DIR"
echo "  - Server Port: $SERVER_PORT"

# 启动应用
echo "🎯 启动Spring Boot应用..."
mvn spring-boot:run
