<template>
  <div class="messages">
    <h2 class="page-title">消息中心</h2>

    <div class="messages-container">
      <div class="messages-header">
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane label="全部消息" name="all" />
          <el-tab-pane label="未读消息" name="unread" />
        </el-tabs>

        <div class="header-actions">
          <el-button
            v-if="activeTab === 'all'"
            type="primary"
            size="small"
            @click="handleMarkAllRead"
            :disabled="markingAll"
          >
            {{ markingAll ? '标记中...' : '全部已读' }}
          </el-button>
        </div>
      </div>

      <div class="messages-list" v-loading="loading">
        <template v-if="messages.length > 0">
          <div
            v-for="message in messages"
            :key="message.id"
            class="message-item"
            :class="{ 'unread': !message.isRead }"
          >
            <div class="message-header">
              <div class="message-title">
                <span class="title-text">{{ message.title }}</span>
                <el-tag v-if="!message.isRead" type="danger" size="small">未读</el-tag>
              </div>
              <span class="message-time">{{ formatTime(message.createTime) }}</span>
            </div>

            <div class="message-content">{{ message.content }}</div>

            <div class="message-actions" v-if="!message.isRead">
              <el-button
                size="small"
                type="primary"
                @click="handleMarkRead(message)"
                :loading="message.marking"
              >
                标记已读
              </el-button>
            </div>
          </div>
        </template>

        <el-empty v-else description="暂无消息" />
      </div>

      <div class="pagination" v-if="total > pageSize">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getMessages, markAsRead, markAllAsRead } from '../api/message'
import { formatDate } from '../utils/auth'

// Data
const activeTab = ref('all')
const loading = ref(false)
const markingAll = ref(false)
const messages = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// Computed
const formatTime = computed(() => {
  return (time) => formatDate(time)
})

// Methods
const fetchMessages = async () => {
  loading.value = true
  try {
    const type = activeTab.value === 'unread' ? 'unread' : null
    const response = await getMessages(currentPage.value, pageSize.value, type)

    if (response.success) {
      messages.value = response.data.records.map(msg => ({
        ...msg,
        marking: false
      }))
      total.value = response.data.total
    }
  } catch (error) {
    ElMessage.error('获取消息失败')
  } finally {
    loading.value = false
  }
}

const handleTabChange = (tab) => {
  activeTab.value = tab
  currentPage.value = 1
  fetchMessages()
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchMessages()
}

const handleMarkRead = async (message) => {
  message.marking = true
  try {
    await markAsRead(message.id)
    message.isRead = 1
    ElMessage.success('已标记为已读')

    // Emit event to update unread count in navigation
    window.dispatchEvent(new CustomEvent('messageRead'))

    // If in unread tab, remove from list
    if (activeTab.value === 'unread') {
      const index = messages.value.findIndex(m => m.id === message.id)
      if (index > -1) {
        messages.value.splice(index, 1)
        total.value -= 1
      }
    }
  } catch (error) {
    ElMessage.error('标记失败')
  } finally {
    message.marking = false
  }
}

const handleMarkAllRead = async () => {
  markingAll.value = true
  try {
    await markAllAsRead()
    ElMessage.success('全部消息已标记为已读')

    // Emit event to update unread count in navigation
    window.dispatchEvent(new CustomEvent('messageRead'))

    // Refresh messages
    fetchMessages()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    markingAll.value = false
  }
}

// Lifecycle
onMounted(() => {
  fetchMessages()
})
</script>

<style lang="scss" scoped>
.messages {
  .page-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }

  .messages-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 20px;

    .messages-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      border-bottom: 1px solid #ebeef5;
      padding-bottom: 10px;

      .header-actions {
        flex-shrink: 0;
      }
    }

    .messages-list {
      min-height: 300px;

      .message-item {
        border: 1px solid #ebeef5;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 10px;
        transition: all 0.3s;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.unread {
          border-left: 4px solid #409eff;
          background-color: #f0f9ff;
        }

        .message-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .message-title {
            display: flex;
            align-items: center;
            gap: 10px;

            .title-text {
              font-weight: 500;
              font-size: 16px;
            }
          }

          .message-time {
            color: #909399;
            font-size: 14px;
          }
        }

        .message-content {
          color: #606266;
          line-height: 1.6;
          margin-bottom: 10px;
        }

        .message-actions {
          text-align: right;
        }
      }
    }

    .pagination {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

@media (max-width: 768px) {
  .messages {
    .messages-container {
      padding: 15px;

      .messages-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
      }

      .message-item {
        .message-header {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }
      }
    }
  }
}
</style>
