import{X as h,r as p,x as Z,o as ye,b as g,d as o,e as t,q as ee,f as a,g as u,s as ge,h as k,F as N,v as le,i as A,t as v,j,G,E as m,k as ke,l as i,m as f,H as he,D as te}from"./index-BCDD51NH.js";import{b as De}from"./resource-BWs-v3qB.js";import{_ as be}from"./_plugin-vue_export-helper-DlAUqK2U.js";function Ve(c){return h({url:"/study-plans",method:"post",params:c})}function Te(){return h({url:"/study-plans",method:"get"})}function we(c){return h({url:`/study-plans/${c}`,method:"delete"})}function Ye(c,P){return h({url:`/study-plans/${c}/tasks`,method:"post",params:P})}function Ce(c){return h({url:`/study-plans/${c}/tasks`,method:"get"})}function xe(c){return h({url:`/study-plans/tasks/${c}/complete`,method:"put"})}function Me(c){return h({url:`/study-plans/tasks/${c}`,method:"delete"})}const Fe={class:"study-plan"},Re={class:"plan-container"},Pe={class:"plan-sidebar"},Ie={class:"sidebar-header"},$e={class:"plan-list"},Be=["onClick"],Ue={class:"plan-name"},Se={class:"plan-type"},ze={class:"plan-content"},qe={class:"plan-header"},He={class:"plan-info"},Ee={class:"plan-meta"},Ne={key:0,class:"plan-description"},Ae={class:"plan-actions"},je={class:"task-list"},Ge={class:"task-actions"},Le={class:"action-slot complete-slot"},Xe={class:"action-slot resource-slot"},Je={class:"action-slot delete-slot"},Ke={__name:"StudyPlan",setup(c){const P=ke(),D=p([]),n=p(null),Y=p([]),I=p([]),$=p(!1),B=p(!1),b=p(!1),V=p(!1),U=p(!1),S=p(!1),T=p(null),w=p(null),r=Z({name:"",planType:null,dateRange:[],description:""}),d=Z({name:"",scheduledDate:"",resourceId:null,description:""}),ae={name:[{required:!0,message:"请输入计划名称",trigger:"blur"}],planType:[{required:!0,message:"请选择计划类型",trigger:"change"}],dateRange:[{required:!0,message:"请选择起止时间",trigger:"change"}]},se={name:[{required:!0,message:"请输入任务名称",trigger:"blur"}],scheduledDate:[{required:!0,message:"请选择计划日期",trigger:"change"}]},L=s=>["日计划","周计划","月计划"][s]||"未知",z=async()=>{$.value=!0;try{const s=await Te();D.value=s.data,D.value.length>0&&!n.value&&X(D.value[0])}catch(s){console.error("Failed to fetch study plans:",s),m.error("获取学习计划失败")}finally{$.value=!1}},C=async()=>{if(n.value){B.value=!0;try{const s=await Ce(n.value.id);Y.value=s.data}catch(s){console.error("Failed to fetch tasks:",s),m.error("获取任务列表失败")}finally{B.value=!1}}},oe=async()=>{try{const s=await De();I.value=s.data.filter(e=>e.auditStatus===1)}catch(s){console.error("Failed to fetch resources:",s)}},X=s=>{n.value=s,C()},ne=()=>{T.value&&T.value.resetFields(),r.name="",r.planType=null,r.dateRange=[],r.description="",b.value=!0},re=()=>{w.value&&w.value.resetFields(),d.name="",d.scheduledDate="",d.resourceId=null,d.description="",I.value.length===0&&oe(),V.value=!0},de=async()=>{T.value&&await T.value.validate(async s=>{if(s){U.value=!0;try{const[e,x]=r.dateRange;await Ve({name:r.name,planType:r.planType,startDate:e,endDate:x,description:r.description}),m.success("学习计划创建成功"),b.value=!1,await z()}catch(e){console.error("Failed to create study plan:",e),m.error("创建学习计划失败")}finally{U.value=!1}}})},ue=async()=>{!w.value||!n.value||await w.value.validate(async s=>{if(s){S.value=!0;try{await Ye(n.value.id,{name:d.name,scheduledDate:d.scheduledDate,resourceId:d.resourceId||void 0,description:d.description}),m.success("任务添加成功"),V.value=!1,await C()}catch(e){console.error("Failed to add task:",e),m.error("添加任务失败")}finally{S.value=!1}}})},ie=async s=>{try{await xe(s.id),m.success("任务已完成"),await C()}catch(e){console.error("Failed to complete task:",e),m.error("操作失败")}},ce=async s=>{try{await te.confirm("确定要删除该任务吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await Me(s.id),m.success("任务已删除"),await C()}catch(e){e!=="cancel"&&(console.error("Failed to delete task:",e),m.error("删除失败"))}},pe=async()=>{if(n.value)try{await te.confirm("确定要删除该学习计划吗？计划中的所有任务也将被删除。","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await we(n.value.id),m.success("学习计划已删除"),n.value=null,Y.value=[],await z()}catch(s){s!=="cancel"&&(console.error("Failed to delete study plan:",s),m.error("删除失败"))}},me=s=>{P.push({name:"ResourceDetail",params:{id:s}})};return ye(()=>{z()}),(s,e)=>{const x=u("el-icon-plus"),q=u("el-icon"),_=u("el-button"),H=u("el-empty"),fe=u("el-icon-delete"),M=u("el-table-column"),_e=u("el-tag"),ve=u("el-table"),F=u("el-input"),y=u("el-form-item"),R=u("el-option"),J=u("el-select"),K=u("el-date-picker"),O=u("el-form"),Q=u("el-dialog"),W=ge("loading");return i(),g("div",Fe,[e[23]||(e[23]=o("h2",{class:"page-title"},"学习规划",-1)),o("div",Re,[o("div",Pe,[o("div",Ie,[e[13]||(e[13]=o("h3",null,"我的计划",-1)),t(_,{type:"primary",size:"small",onClick:ne},{default:a(()=>[t(q,null,{default:a(()=>[t(x)]),_:1}),e[12]||(e[12]=f(" 新建计划 "))]),_:1,__:[12]})]),ee((i(),g("div",$e,[D.value.length>0?(i(!0),g(N,{key:0},le(D.value,l=>(i(),g("div",{key:l.id,class:he(["plan-item",{active:n.value&&n.value.id===l.id}]),onClick:E=>X(l)},[o("div",Ue,v(l.name),1),o("div",Se,v(L(l.planType)),1)],10,Be))),128)):(i(),k(H,{key:1,description:"暂无学习计划"}))])),[[W,$.value]])]),o("div",ze,[n.value?(i(),g(N,{key:0},[o("div",qe,[o("div",He,[o("h3",null,v(n.value.name),1),o("div",Ee,[o("span",null,v(L(n.value.planType)),1),o("span",null,v(j(G)(n.value.startDate,"YYYY-MM-DD"))+" 至 "+v(j(G)(n.value.endDate,"YYYY-MM-DD")),1)]),n.value.description?(i(),g("div",Ne,v(n.value.description),1)):A("",!0)]),o("div",Ae,[t(_,{type:"primary",size:"small",onClick:re},{default:a(()=>[t(q,null,{default:a(()=>[t(x)]),_:1}),e[14]||(e[14]=f(" 添加任务 "))]),_:1,__:[14]}),t(_,{type:"danger",size:"small",onClick:pe},{default:a(()=>[t(q,null,{default:a(()=>[t(fe)]),_:1}),e[15]||(e[15]=f(" 删除计划 "))]),_:1,__:[15]})])]),ee((i(),g("div",je,[Y.value.length>0?(i(),k(ve,{key:0,data:Y.value,style:{width:"100%"}},{default:a(()=>[t(M,{prop:"name",label:"任务名称","min-width":"200"}),t(M,{prop:"scheduledDate",label:"计划日期",width:"180"},{default:a(({row:l})=>[f(v(j(G)(l.scheduledDate,"YYYY-MM-DD")),1)]),_:1}),t(M,{prop:"status",label:"状态",width:"100"},{default:a(({row:l})=>[t(_e,{type:l.status===1?"success":"info"},{default:a(()=>[f(v(l.status===1?"已完成":"未完成"),1)]),_:2},1032,["type"])]),_:1}),t(M,{label:"操作",width:"280",fixed:"right"},{default:a(({row:l})=>[o("div",Ge,[o("div",Le,[l.status===0?(i(),k(_,{key:0,type:"success",size:"small",onClick:E=>ie(l)},{default:a(()=>e[16]||(e[16]=[f(" 完成 ")])),_:2,__:[16]},1032,["onClick"])):A("",!0)]),o("div",Xe,[l.resourceId?(i(),k(_,{key:0,type:"primary",size:"small",onClick:E=>me(l.resourceId)},{default:a(()=>e[17]||(e[17]=[f(" 查看资料 ")])),_:2,__:[17]},1032,["onClick"])):A("",!0)]),o("div",Je,[t(_,{type:"danger",size:"small",onClick:E=>ce(l)},{default:a(()=>e[18]||(e[18]=[f(" 删除 ")])),_:2,__:[18]},1032,["onClick"])])])]),_:1})]),_:1},8,["data"])):(i(),k(H,{key:1,description:"暂无任务，点击'添加任务'开始规划"}))])),[[W,B.value]])],64)):(i(),k(H,{key:1,description:"请选择或创建一个学习计划"}))])]),t(Q,{modelValue:b.value,"onUpdate:modelValue":e[5]||(e[5]=l=>b.value=l),title:"创建学习计划",width:"500px"},{footer:a(()=>[t(_,{onClick:e[4]||(e[4]=l=>b.value=!1)},{default:a(()=>e[19]||(e[19]=[f("取消")])),_:1,__:[19]}),t(_,{type:"primary",loading:U.value,onClick:de},{default:a(()=>e[20]||(e[20]=[f(" 创建 ")])),_:1,__:[20]},8,["loading"])]),default:a(()=>[t(O,{ref_key:"planFormRef",ref:T,model:r,rules:ae,"label-width":"100px"},{default:a(()=>[t(y,{label:"计划名称",prop:"name"},{default:a(()=>[t(F,{modelValue:r.name,"onUpdate:modelValue":e[0]||(e[0]=l=>r.name=l),placeholder:"请输入计划名称"},null,8,["modelValue"])]),_:1}),t(y,{label:"计划类型",prop:"planType"},{default:a(()=>[t(J,{modelValue:r.planType,"onUpdate:modelValue":e[1]||(e[1]=l=>r.planType=l),placeholder:"请选择计划类型"},{default:a(()=>[t(R,{label:"日计划",value:0}),t(R,{label:"周计划",value:1}),t(R,{label:"月计划",value:2})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"起止时间",prop:"dateRange"},{default:a(()=>[t(K,{modelValue:r.dateRange,"onUpdate:modelValue":e[2]||(e[2]=l=>r.dateRange=l),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DDTHH:mm:ss"},null,8,["modelValue"])]),_:1}),t(y,{label:"计划描述",prop:"description"},{default:a(()=>[t(F,{modelValue:r.description,"onUpdate:modelValue":e[3]||(e[3]=l=>r.description=l),type:"textarea",rows:3,placeholder:"请输入计划描述（选填）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(Q,{modelValue:V.value,"onUpdate:modelValue":e[11]||(e[11]=l=>V.value=l),title:"添加任务",width:"500px"},{footer:a(()=>[t(_,{onClick:e[10]||(e[10]=l=>V.value=!1)},{default:a(()=>e[21]||(e[21]=[f("取消")])),_:1,__:[21]}),t(_,{type:"primary",loading:S.value,onClick:ue},{default:a(()=>e[22]||(e[22]=[f(" 添加 ")])),_:1,__:[22]},8,["loading"])]),default:a(()=>[t(O,{ref_key:"taskFormRef",ref:w,model:d,rules:se,"label-width":"100px"},{default:a(()=>[t(y,{label:"任务名称",prop:"name"},{default:a(()=>[t(F,{modelValue:d.name,"onUpdate:modelValue":e[6]||(e[6]=l=>d.name=l),placeholder:"请输入任务名称"},null,8,["modelValue"])]),_:1}),t(y,{label:"计划日期",prop:"scheduledDate"},{default:a(()=>[t(K,{modelValue:d.scheduledDate,"onUpdate:modelValue":e[7]||(e[7]=l=>d.scheduledDate=l),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DDTHH:mm:ss"},null,8,["modelValue"])]),_:1}),t(y,{label:"关联资料",prop:"resourceId"},{default:a(()=>[t(J,{modelValue:d.resourceId,"onUpdate:modelValue":e[8]||(e[8]=l=>d.resourceId=l),placeholder:"请选择关联资料（选填）",filterable:"",clearable:""},{default:a(()=>[(i(!0),g(N,null,le(I.value,l=>(i(),k(R,{key:l.id,label:l.name,value:l.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"任务描述",prop:"description"},{default:a(()=>[t(F,{modelValue:d.description,"onUpdate:modelValue":e[9]||(e[9]=l=>d.description=l),type:"textarea",rows:3,placeholder:"请输入任务描述（选填）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},Ze=be(Ke,[["__scopeId","data-v-0f9f2afe"]]);export{Ze as default};
